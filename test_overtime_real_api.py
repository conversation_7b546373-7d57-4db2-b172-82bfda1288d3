"""
Test Overtime Implementation in Real API Data Processor
Tests the overtime logic without browser automation
"""

import asyncio
import logging
import sys
import os

# Add src directory to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from test_real_api_data import RealAPIDataProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

async def test_overtime_logic():
    """Test the overtime logic in RealAPIDataProcessor"""
    print("=" * 80)
    print("TESTING OVERTIME LOGIC IN REAL API DATA PROCESSOR")
    print("=" * 80)
    
    processor = RealAPIDataProcessor()
    
    # Get sample data for testing
    sample_records = processor._get_sample_data()
    
    print(f"\n📊 Original Sample Records: {len(sample_records)}")
    
    all_entries = []
    for i, record in enumerate(sample_records, 1):
        print(f"\n--- Processing Record {i}: {record['employee_name']} ---")
        print(f"📅 Date: {record['date']}")
        print(f"⏰ Regular Hours: {record['regular_hours']}")
        print(f"⏱️  Overtime Hours: {record['overtime_hours']}")
        print(f"🎯 Total Hours: {record['total_hours']}")
        print(f"🔧 Raw Charge Job: {record['raw_charge_job'][:50]}...")
        
        # Create overtime entries
        entries = processor.create_overtime_entries(record)
        all_entries.extend(entries)
        
        print(f"✅ Created {len(entries)} entries:")
        for j, entry in enumerate(entries, 1):
            entry_type = entry.get('entry_type', 'unknown')
            transaction_type = entry.get('transaction_type', 'Unknown')
            hours = entry.get('hours', 0)
            
            print(f"   Entry {j}: {transaction_type} ({entry_type}) - {hours} hours")
    
    # Summary
    normal_entries = [e for e in all_entries if e.get('transaction_type') == 'Normal']
    overtime_entries = [e for e in all_entries if e.get('transaction_type') == 'Overtime']
    
    print(f"\n{'='*80}")
    print("OVERTIME LOGIC TEST SUMMARY")
    print("="*80)
    print(f"📊 Original Records: {len(sample_records)}")
    print(f"📊 Total Entries Created: {len(all_entries)}")
    print(f"📝 Normal Entries: {len(normal_entries)}")
    print(f"⏰ Overtime Entries: {len(overtime_entries)}")
    print(f"📈 Average Entries per Record: {len(all_entries)/len(sample_records):.1f}")
    
    # Expected results based on sample data:
    # Record 1: Ade Prasetya (8.0 + 4.0) -> 2 entries
    # Record 2: Budi Santoso (8.0 + 2.0) -> 2 entries  
    # Record 3: Citra Dewi (8.0 + 0.0) -> 1 entry
    # Total expected: 5 entries
    
    expected_entries = 5
    success = len(all_entries) == expected_entries
    
    print(f"\n📋 Expected Results Check:")
    print(f"   Expected Total Entries: {expected_entries}")
    print(f"   Actual Total Entries: {len(all_entries)}")
    if success:
        print("   ✅ PASS: Correct number of entries created")
    else:
        print("   ❌ FAIL: Incorrect number of entries")
    
    # Test charge job parsing
    print(f"\n{'='*80}")
    print("TESTING CHARGE JOB PARSING")
    print("="*80)
    
    for i, record in enumerate(sample_records, 1):
        print(f"\n--- Charge Job Parsing Test {i}: {record['employee_name']} ---")
        raw_charge_job = record['raw_charge_job']
        print(f"📝 Raw: {raw_charge_job}")
        
        components = processor.parse_raw_charge_job(raw_charge_job)
        print(f"🔧 Parsed {len(components)} components:")
        for j, component in enumerate(components):
            print(f"   [{j}]: {component}")
    
    return success

def test_api_automation_integration():
    """Test the integration with APIDataAutomation"""
    print(f"\n{'='*80}")
    print("TESTING API AUTOMATION INTEGRATION") 
    print("="*80)
    
    processor = RealAPIDataProcessor()
    
    # Test sample record
    test_record = {
        "employee_name": "Test Employee",
        "date": "2025-05-18",
        "regular_hours": 7.0,
        "overtime_hours": 3.0,
        "raw_charge_job": "(OC7190) BOILER OPERATION / STN-BLR (STATION BOILER) / BLR00000 (LABOUR COST) / L (LABOUR)"
    }
    
    print(f"📝 Test Record: {test_record['employee_name']}")
    print(f"⏰ Regular: {test_record['regular_hours']}h, Overtime: {test_record['overtime_hours']}h")
    
    entries = processor.create_overtime_entries(test_record)
    
    print(f"✅ Integration Test Results:")
    print(f"   Created {len(entries)} entries (expected: 2)")
    
    for i, entry in enumerate(entries, 1):
        print(f"   Entry {i}:")
        print(f"     Type: {entry.get('transaction_type')}")
        print(f"     Hours: {entry.get('hours')}")
        print(f"     Entry Type: {entry.get('entry_type')}")
        
        # Verify all original fields are preserved
        preserved_fields = ['employee_name', 'date', 'raw_charge_job']
        for field in preserved_fields:
            if field in entry:
                print(f"     {field}: {entry[field][:30]}{'...' if len(str(entry[field])) > 30 else ''}")
    
    return len(entries) == 2

def main():
    """Main test function"""
    print("VENUS AUTOFILL - OVERTIME IMPLEMENTATION TEST")
    print("Real API Data Processor with Overtime Support")
    print("=" * 60)
    
    # Test 1: Overtime logic
    test1_success = asyncio.run(test_overtime_logic())
    
    # Test 2: API automation integration
    test2_success = test_api_automation_integration()
    
    print(f"\n{'='*80}")
    print("FINAL TEST RESULTS")
    print("="*80)
    
    if test1_success and test2_success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Overtime implementation is ready for use")
        print("✅ Real API Data Processor supports overtime handling")
        print("✅ Integration with APIDataAutomation working correctly")
    else:
        print("❌ SOME TESTS FAILED!")
        print(f"   Overtime Logic Test: {'✅ PASS' if test1_success else '❌ FAIL'}")
        print(f"   Integration Test: {'✅ PASS' if test2_success else '❌ FAIL'}")
    
    print("="*80)

if __name__ == "__main__":
    main() 