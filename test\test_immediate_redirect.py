#!/usr/bin/env python3
"""
Test script for immediate redirect from location setting page
Tests the fix for getting stuck on frmSystemUserSetlocation.aspx
"""

import asyncio
import logging
import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from automation_service import get_automation_service


def setup_logging():
    """Setup basic logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def load_config():
    """Load test configuration"""
    return {
        "browser": {
            "headless": False,
            "window_size": [1280, 720],
            "disable_notifications": True
        },
        "automation": {
            "implicit_wait": 10,
            "page_load_timeout": 30,
            "script_timeout": 30,
            "max_retries": 3
        },
        "credentials": {
            "username": "adm075",
            "password": "adm075"
        },
        "urls": {
            "login": "http://millwarep3:8004/",
            "taskRegister": "http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx"
        }
    }


def test_immediate_console_feedback():
    """Test immediate console feedback when processing selected records"""
    print("\n" + "="*70)
    print("🧪 TESTING IMMEDIATE CONSOLE FEEDBACK")
    print("="*70)
    
    config = load_config()
    
    try:
        print("1️⃣ Creating automation service...")
        service = get_automation_service(config)
        
        print("\n2️⃣ Testing immediate feedback (simulating user clicking 'Process Selected Records')...")
        
        # Simulate selected record IDs
        test_record_ids = ['c1e595b4-d498-4320-bce3-8d0f0cf52060', 'emp002_20250611']
        
        print(f"\n🖱️ USER ACTION: Clicking 'Process Selected Records' for {len(test_record_ids)} records")
        
        # Start automation job (this should show immediate feedback)
        start_time = time.time()
        try:
            job_id = service.start_automation_job(test_record_ids)
            print(f"✅ Job started successfully: {job_id}")
        except Exception as e:
            print(f"⚠️ Job failed to start: {e}")
            print("   This is expected if the engine is still initializing")
        
        feedback_time = time.time() - start_time
        print(f"\n📊 Feedback Response Time: {feedback_time:.2f} seconds")
        
        if feedback_time < 2:
            print("✅ IMMEDIATE FEEDBACK TEST PASSED - Response was instant!")
        else:
            print("⚠️ FEEDBACK COULD BE FASTER - Consider optimizing")
        
        return True
        
    except Exception as e:
        print(f"❌ Console feedback test failed: {e}")
        return False


def monitor_engine_initialization():
    """Monitor and display engine initialization progress"""
    print("\n" + "="*70)
    print("🧪 MONITORING ENGINE INITIALIZATION")
    print("="*70)
    
    config = load_config()
    
    try:
        print("1️⃣ Creating automation service (this starts background initialization)...")
        service = get_automation_service(config)
        
        print("\n2️⃣ Monitoring initialization progress...")
        start_time = time.time()
        max_wait = 90  # 90 seconds max wait
        
        while time.time() - start_time < max_wait:
            elapsed = int(time.time() - start_time)
            engine_status = service.get_engine_status()
            
            print(f"⏳ {elapsed}s - Engine Status: {engine_status}")
            
            if engine_status['initialized']:
                total_time = time.time() - start_time
                print(f"✅ ENGINE READY in {total_time:.2f} seconds!")
                return True
            
            time.sleep(3)
        
        print("❌ ENGINE INITIALIZATION TIMEOUT")
        return False
        
    except Exception as e:
        print(f"❌ Engine monitoring failed: {e}")
        return False


def main():
    """Main test function"""
    print("🧪 IMMEDIATE REDIRECT & CONSOLE FEEDBACK TESTS")
    print("=" * 80)
    
    setup_logging()
    
    # Test 1: Immediate console feedback
    feedback_test_passed = test_immediate_console_feedback()
    
    # Test 2: Monitor engine initialization
    print("\n" + "⏳" + "="*60)
    print("⏳ WAITING FOR ENGINE TO INITIALIZE...")
    print("   This tests the location page redirect functionality")
    print("   Watch the browser window for redirect behavior")
    print("="*62)
    
    engine_test_passed = monitor_engine_initialization()
    
    # Summary
    print("\n" + "="*70)
    print("📋 TEST SUMMARY")
    print("="*70)
    print(f"Immediate Console Feedback: {'✅ PASSED' if feedback_test_passed else '❌ FAILED'}")
    print(f"Engine Initialization: {'✅ PASSED' if engine_test_passed else '❌ FAILED'}")
    
    if feedback_test_passed and engine_test_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Immediate console feedback is working")
        print("✅ Engine initialization completed successfully")
        print("✅ Location page redirect should be working")
        
        print("\n📝 What this means:")
        print("  • When you click 'Process Selected Records', you'll see immediate feedback")
        print("  • The system will show what records are being processed")
        print("  • Location page redirects should happen automatically")
        print("  • Engine initialization should complete within 60-90 seconds")
    else:
        print("\n❌ SOME TESTS FAILED")
        if not feedback_test_passed:
            print("  ❌ Console feedback needs improvement")
        if not engine_test_passed:
            print("  ❌ Engine initialization failed (likely location page issue)")
    
    # Cleanup
    try:
        from automation_service import cleanup_automation_service
        cleanup_automation_service()
        print("\n🧹 Cleanup completed")
    except Exception as e:
        print(f"⚠️ Cleanup warning: {e}")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        import traceback
        traceback.print_exc() 