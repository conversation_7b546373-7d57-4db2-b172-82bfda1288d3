"""
Venus AutoFill - API Data Automation Demo
Demonstrates the complete workflow with sample data and REAL BROWSER INTERACTION
"""

import asyncio
import logging
import json
from datetime import datetime
from pathlib import Path
import sys
import os

# Add src directory to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.persistent_browser_manager import PersistentBrowserManager
from src.core.api_data_automation import APIDataAutomation

# Sample data structure (dari contoh yang user berikan)
SAMPLE_API_DATA = [
    {
        "check_in": "08:00",
        "check_out": "17:00",
        "created_at": "2025-06-10 04:43:03",
        "date": "2025-05-30",
        "day_of_week": "Sab",
        "employee_id": "PTRJ.241000001",
        "employee_name": "Ade Prasetya",
        "expense_code": "L (LABOUR)",
        "id": "c1e595b4-d498-4320-bce3-8d0f0cf52060",
        "machine_code": "BLR00000 (LABOUR COST)",
        "notes": "Transferred from Monthly Grid - May 2025",
        "overtime_hours": 0.0,
        "raw_charge_job": "(OC7190) BOILER OPERATION / STN-BLR (STATION BOILER) / BLR00000 (LABOUR COST) / L (LABOUR)",
        "regular_hours": 0.0,
        "shift": "Regular",
        "source_record_id": "",
        "station_code": "STN-BLR (STATION BOILER)",
        "status": "staged",
        "task_code": "(OC7190) BOILER OPERATION",
        "total_hours": 0.0,
        "transfer_status": "success",
        "updated_at": "2025-06-10 04:43:03"
    },
    {
        "check_in": "08:00",
        "check_out": "17:00",
        "created_at": "2025-06-10 04:43:03",
        "date": "2025-06-01",
        "day_of_week": "Sen",
        "employee_id": "PTRJ.241000002",
        "employee_name": "Budi Santoso",
        "expense_code": "M (MATERIAL)",
        "id": "d2f696c5-e599-5431-cdf4-9e1f1dg63171",
        "machine_code": "TUR00001 (TURBINE COST)",
        "notes": "Transferred from Monthly Grid - June 2025",
        "overtime_hours": 2.0,
        "raw_charge_job": "(OC7191) TURBINE MAINTENANCE / STN-TUR (STATION TURBINE) / TUR00001 (TURBINE COST) / M (MATERIAL)",
        "regular_hours": 8.0,
        "shift": "Regular",
        "source_record_id": "",
        "station_code": "STN-TUR (STATION TURBINE)",
        "status": "staged",
        "task_code": "(OC7191) TURBINE MAINTENANCE",
        "total_hours": 10.0,
        "transfer_status": "success",
        "updated_at": "2025-06-10 04:43:03"
    }
]


class APIDataAutomationDemo:
    """Demo class untuk menunjukkan workflow API automation dengan REAL BROWSER"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.browser_manager = None
        self.api_automation = None
        self.config = self._load_config()
    
    def _load_config(self) -> dict:
        """Load configuration from file"""
        try:
            config_path = Path("config/app_config.json")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.logger.info("✅ Configuration loaded from file")
                    return config
            else:
                # Default configuration
                default_config = {
                    "browser": {
                        "chrome_options": [
                            "--no-sandbox",
                            "--disable-dev-shm-usage",
                            "--start-maximized"
                        ],
                        "page_load_timeout": 30,
                        "implicit_wait": 10
                    },
                    "urls": {
                        "login": "http://millwarep3:8004/",
                        "taskRegister": "http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx"
                    },
                    "credentials": {
                        "username": "adm075",
                        "password": "adm075"
                    },
                    "automation": {
                        "max_retries": 3,
                        "retry_delay": 2.0,
                        "element_timeout": 15.0
                    }
                }
                self.logger.info("⚠️ Using default configuration")
                return default_config
        except Exception as e:
            self.logger.error(f"❌ Failed to load configuration: {e}")
            return {}
    
    async def initialize_browser(self) -> bool:
        """Initialize browser with real WebDriver"""
        try:
            self.logger.info("🚀 Initializing browser manager...")
            self.browser_manager = PersistentBrowserManager(self.config)
            
            # Initialize browser with login
            success = await self.browser_manager.initialize()
            if success:
                self.logger.info("✅ Browser initialized and logged in successfully")
                
                # Navigate to task register page
                await self.browser_manager.navigate_to_task_register()
                self.logger.info("✅ Navigated to task register page")
                
                return True
            else:
                self.logger.error("❌ Browser initialization failed")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Browser initialization error: {e}")
            return False
    
    async def initialize_api_automation(self) -> bool:
        """Initialize API automation with browser"""
        try:
            if not self.browser_manager:
                self.logger.error("❌ Browser manager not initialized")
                return False
            
            driver = self.browser_manager.get_driver()
            if not driver:
                self.logger.error("❌ WebDriver not available")
                return False
            
            self.logger.info("🔧 Initializing API automation...")
            self.api_automation = APIDataAutomation(self.config)
            
            # Set the driver from browser manager
            self.api_automation.browser_manager = self.browser_manager
            
            self.logger.info("✅ API automation initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ API automation initialization error: {e}")
            return False
    
    def parse_charge_job(self, raw_charge_job: str) -> tuple:
        """Parse raw_charge_job menjadi komponen-komponen"""
        try:
            parts = [part.strip() for part in raw_charge_job.split(' / ')]
            
            task_code = parts[0] if len(parts) > 0 else ""
            station_code = parts[1] if len(parts) > 1 else ""
            machine_code = parts[2] if len(parts) > 2 else ""
            expense_code = parts[3] if len(parts) > 3 else ""
            
            return task_code, station_code, machine_code, expense_code
            
        except Exception as e:
            self.logger.error(f"❌ Failed to parse charge job: {e}")
            return "", "", "", ""
    
    def format_date(self, date_str: str) -> str:
        """Convert date dari format API (2025-05-30) ke format form (30/05/2025)"""
        try:
            date_obj = datetime.strptime(date_str, "%Y-%m-%d")
            formatted = date_obj.strftime("%d/%m/%Y")
            return formatted
        except Exception as e:
            self.logger.error(f"❌ Date formatting failed: {e}")
            return date_str
    
    async def process_record_with_real_browser(self, record: dict) -> bool:
        """Process record with REAL browser interaction"""
        try:
            print(f"\n🔄 Processing Record: {record['employee_name']}")
            print(f"📅 Date: {record['date']}")
            print(f"👤 Employee: {record['employee_name']}")
            
            # Parse charge job
            task_code, station_code, machine_code, expense_code = self.parse_charge_job(
                record['raw_charge_job']
            )
            
            print(f"🎯 Parsed Components:")
            print(f"   Task: {task_code}")
            print(f"   Station: {station_code}")
            print(f"   Machine: {machine_code}")
            print(f"   Expense: {expense_code}")
            
            # Format date
            formatted_date = self.format_date(record['date'])
            
            # Use API automation to fill form
            if self.api_automation:
                print(f"\n🖥️ FILLING FORM WITH REAL BROWSER...")
                
                # Prepare form data
                form_data = {
                    'date': formatted_date,
                    'employee': record['employee_name'],
                    'task_code': task_code,
                    'station_code': station_code,
                    'machine_code': machine_code,
                    'expense_code': expense_code
                }
                
                # Fill form using API automation
                success = await self.api_automation.fill_single_record(form_data)
                
                if success:
                    print(f"✅ Record processed successfully in browser!")
                    return True
                else:
                    print(f"❌ Failed to process record in browser")
                    return False
            else:
                print(f"❌ API automation not initialized")
                return False
                
        except Exception as e:
            print(f"❌ Error processing record: {e}")
            return False
    
    async def debug_form_fields(self) -> bool:
        """Debug method to inspect form fields on the page"""
        try:
            if not self.browser_manager:
                return False
            
            driver = self.browser_manager.get_driver()
            if not driver:
                return False
            
            print(f"\n🔍 DEBUGGING FORM FIELDS ON PAGE:")
            print(f"Current URL: {driver.current_url}")
            
            # Find all input fields with more details
            script = """
                var inputs = document.querySelectorAll('input[type="text"], input.ui-autocomplete-input, input[id*="txt"], input[id*="ChargeJob"]');
                var results = [];
                for (var i = 0; i < inputs.length; i++) {
                    var input = inputs[i];
                    var isVisible = input.offsetWidth > 0 && input.offsetHeight > 0;
                    results.push({
                        id: input.id || 'no-id',
                        name: input.name || 'no-name',
                        type: input.type || 'no-type',
                        className: input.className || 'no-class',
                        placeholder: input.placeholder || 'no-placeholder',
                        visible: isVisible,
                        value: input.value || 'empty'
                    });
                }
                return results;
            """
            
            fields = driver.execute_script(script)
            
            print(f"📝 Found {len(fields)} relevant input fields:")
            for i, field in enumerate(fields):
                visibility = "👁️" if field['visible'] else "🙈"
                print(f"  {i+1:2d}. {visibility} ID: {field['id'][:40]:<40} | Class: {field['className'][:40]:<40}")
                if field.get('value') and field['value'] != 'empty':
                    print(f"      💡 Current Value: {field['value']}")
                    
                # Mark potential targets
                field_id = field['id'].lower()
                if any(keyword in field_id for keyword in ['chargejob', 'task', 'employee', 'date']):
                    print(f"      ⭐ POTENTIAL TARGET: {field}")
            
            # Also check for autocomplete inputs specifically
            print(f"\n🔍 Checking for autocomplete inputs:")
            autocomplete_script = """
                var autocompletes = document.querySelectorAll('.ui-autocomplete-input');
                var results = [];
                for (var i = 0; i < autocompletes.length; i++) {
                    var input = autocompletes[i];
                    var isVisible = input.offsetWidth > 0 && input.offsetHeight > 0; 
                    results.push({
                        id: input.id || 'no-id',
                        className: input.className || 'no-class',
                        visible: isVisible,
                        tagName: input.tagName,
                        parentId: input.parentElement ? input.parentElement.id : 'no-parent-id'
                    });
                }
                return results;
            """
            
            autocomplete_fields = driver.execute_script(autocomplete_script)
            print(f"📝 Found {len(autocomplete_fields)} autocomplete fields:")
            for i, field in enumerate(autocomplete_fields):
                visibility = "👁️" if field['visible'] else "🙈"
                print(f"  {i+1}. {visibility} ID: {field['id']:<40} | Parent: {field['parentId']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Debug failed: {e}")
            return False
    
    async def run_real_browser_demo(self):
        """Menjalankan demo dengan REAL browser interaction"""
        print("🚀 Venus AutoFill - REAL BROWSER API Data Automation Demo")
        print("=" * 65)
        
        # Initialize browser
        print("🔧 Phase 1: Browser Initialization")
        if not await self.initialize_browser():
            print("❌ Browser initialization failed. Cannot continue.")
            return False
        
        # Initialize API automation
        print("\n🔧 Phase 2: API Automation Setup")
        if not await self.initialize_api_automation():
            print("❌ API automation setup failed. Cannot continue.")
            return False
        
        # DEBUG: Inspect form fields
        print("\n🔧 Phase 2.5: Form Field Debugging")
        await self.debug_form_fields()
        
        print(f"\n📊 Phase 3: Processing {len(SAMPLE_API_DATA)} records")
        print("🔄 Sequence: Date → Employee → Task → Station → Machine → Expense")
        print("⚠️  Transaction type and shift will be skipped")
        print("\n🖥️ Watch the browser for REAL form filling...")
        
        successful_records = 0
        
        for i, record in enumerate(SAMPLE_API_DATA, 1):
            print(f"\n{'='*60}")
            print(f"Processing Record {i}/{len(SAMPLE_API_DATA)}")
            print(f"{'='*60}")
            
            if await self.process_record_with_real_browser(record):
                successful_records += 1
            
            # Delay antar record
            if i < len(SAMPLE_API_DATA):
                print(f"\n⏳ Waiting 3 seconds before next record...")
                await asyncio.sleep(3)
        
        # Summary
        print(f"\n🎯 REAL Browser Demo Complete!")
        print(f"✅ Successfully processed: {successful_records}/{len(SAMPLE_API_DATA)} records")
        print(f"📈 Success Rate: {(successful_records/len(SAMPLE_API_DATA))*100:.1f}%")
        
        # Keep browser open for inspection
        print(f"\n🔍 Browser will remain open for 30 seconds to inspect results...")
        await asyncio.sleep(30)
        
        return successful_records == len(SAMPLE_API_DATA)
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.browser_manager:
                await self.browser_manager.cleanup()
                self.logger.info("✅ Browser manager cleaned up")
        except Exception as e:
            self.logger.error(f"❌ Cleanup error: {e}")


def setup_logging():
    """Setup logging untuk demo"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('demo_automation.log', encoding='utf-8')
        ]
    )


async def main():
    """Main demo function dengan REAL browser"""
    setup_logging()
    
    demo = APIDataAutomationDemo()
    
    try:
        success = await demo.run_real_browser_demo()
        
        if success:
            print("\n🎉 Demo completed successfully!")
        else:
            print("\n⚠️ Demo completed with some issues.")
            
    except KeyboardInterrupt:
        print("\n🛑 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        logging.error(f"Demo error: {e}")
    finally:
        await demo.cleanup()


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())