"""
Test Real API Data - Venus AutoFill
Fetches data directly from localhost:5173/api/staging/data and processes first record
Uses sequential filling strategy based on raw_charge_job split by "/"
"""

import asyncio
import logging
import json
import requests
import sys
import os
from datetime import datetime
from typing import Dict, List, Optional
from calendar import monthrange

# Add src directory to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.persistent_browser_manager import PersistentBrowserManager
from src.core.api_data_automation import APIDataAutomation


class RealAPIDataProcessor:
    """Processor for real API data with sequential charge job filling and overtime support"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.browser_manager = None
        self.api_url = "http://localhost:5173/api/staging/data"
        self.config = self._load_config()
        
        # Initialize API automation for overtime handling
        self.api_automation = APIDataAutomation()
        self.api_automation.logger = self.logger
    
    def _load_config(self) -> dict:
        """Load configuration"""
        try:
            from pathlib import Path
            config_path = Path("config/app_config.json")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"Config file not found, using default: {e}")
        
        # Default config
        return {
            "browser": {
                "chrome_options": ["--no-sandbox", "--disable-dev-shm-usage", "--start-maximized"],
                "page_load_timeout": 30,
                "implicit_wait": 10
            },
            "urls": {
                "login": "http://millwarep3:8004/",
                "taskRegister": "http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx"
            },
            "credentials": {
                "username": "adm075",
                "password": "adm075"
            }
        }
    
    async def fetch_real_api_data(self) -> List[Dict]:
        """Fetch data from real API endpoint"""
        try:
            self.logger.info(f"🌐 Fetching data from: {self.api_url}")
            response = requests.get(self.api_url, timeout=30)  # Increased timeout
            response.raise_for_status()
            
            response_data = response.json()
            self.logger.info(f"✅ API response received")
            
            # The API returns a dict with 'data' key containing the actual records
            if isinstance(response_data, dict) and 'data' in response_data:
                data = response_data['data']
                self.logger.info(f"✅ Fetched {len(data)} records from API")
                
                # Log first record for verification
                if data:
                    first_record = data[0]
                    self.logger.info(f"📋 First record employee: {first_record.get('employee_name', 'Unknown')}")
                    self.logger.info(f"📋 First record date: {first_record.get('date', 'Unknown')}")
                    self.logger.info(f"📋 First record raw_charge_job: {first_record.get('raw_charge_job', 'Unknown')}")
                
                return data
            else:
                self.logger.error(f"❌ Unexpected API response structure: {type(response_data)}")
                return []
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"❌ API request failed: {e}")
            self.logger.warning("⚠️ Falling back to sample data for demonstration")
            return self._get_sample_data()
        except json.JSONDecodeError as e:
            self.logger.error(f"❌ Invalid JSON response: {e}")
            self.logger.warning("⚠️ Falling back to sample data for demonstration")
            return self._get_sample_data()
    
    def _get_sample_data(self) -> List[Dict]:
        """Get sample data for testing when API is not available"""
        sample_data = [
            {
                "check_in": "08:00",
                "check_out": "17:00",
                "created_at": "2025-06-10 04:43:03",
                "date": "2025-05-30",
                "day_of_week": "Sab",
                "employee_id": "PTRJ.241000001",
                "employee_name": "Ade Prasetya",
                "expense_code": "L (LABOUR)",
                "id": "c1e595b4-d498-4320-bce3-8d0f0cf52060",
                "machine_code": "BLR00000 (LABOUR COST)",
                "notes": "Transferred from Monthly Grid - May 2025",
                "overtime_hours": 4.0,
                "raw_charge_job": "(OC7190) BOILER OPERATION / STN-BLR (STATION BOILER) / BLR00000 (LABOUR COST) / L (LABOUR)",
                "regular_hours": 8.0,
                "shift": "Regular",
                "source_record_id": "",
                "station_code": "STN-BLR (STATION BOILER)",
                "status": "staged",
                "task_code": "(OC7190) BOILER OPERATION",
                "total_hours": 12.0,
                "transfer_status": "success",
                "updated_at": "2025-06-10 04:43:03"
            },
            {
                "check_in": "08:00",
                "check_out": "17:00",
                "created_at": "2025-06-10 04:43:03",
                "date": "2025-06-01",
                "day_of_week": "Sen",
                "employee_id": "PTRJ.241000002",
                "employee_name": "Budi Santoso",
                "expense_code": "M (MATERIAL)",
                "id": "d2f696c5-e599-5431-cdf4-9e1f1dg63171",
                "machine_code": "TUR00001 (TURBINE COST)",
                "notes": "Transferred from Monthly Grid - June 2025",
                "overtime_hours": 2.0,
                "raw_charge_job": "(OC7191) TURBINE MAINTENANCE / STN-TUR (STATION TURBINE) / TUR00001 (TURBINE COST) / M (MATERIAL)",
                "regular_hours": 8.0,
                "shift": "Regular",
                "source_record_id": "",
                "station_code": "STN-TUR (STATION TURBINE)",
                "status": "staged",
                "task_code": "(OC7191) TURBINE MAINTENANCE",
                "total_hours": 10.0,
                "transfer_status": "success",
                "updated_at": "2025-06-10 04:43:03"
            },
            {
                "check_in": "08:00",
                "check_out": "16:00",
                "created_at": "2025-06-10 04:43:03",
                "date": "2025-06-02",
                "day_of_week": "Sel",
                "employee_id": "PTRJ.241000003",
                "employee_name": "Citra Dewi",
                "expense_code": "O (OVERHEAD)",
                "id": "e3g707d6-f610-6542-def5-0f2g2eh74282",
                "machine_code": "GEN00001 (GENERATOR COST)",
                "notes": "Transferred from Monthly Grid - June 2025",
                "overtime_hours": 0.0,
                "raw_charge_job": "(OC7192) GENERATOR MAINTENANCE / STN-GEN (STATION GENERATOR) / GEN00001 (GENERATOR COST) / O (OVERHEAD)",
                "regular_hours": 8.0,
                "shift": "Regular",
                "source_record_id": "",
                "station_code": "STN-GEN (STATION GENERATOR)",
                "status": "staged",
                "task_code": "(OC7192) GENERATOR MAINTENANCE",
                "total_hours": 8.0,
                "transfer_status": "success",
                "updated_at": "2025-06-10 04:43:03"
            }
        ]
        
        self.logger.info(f"📊 Using {len(sample_data)} sample records for demonstration")
        return sample_data
    
    def parse_raw_charge_job(self, raw_charge_job: str) -> List[str]:
        """Parse raw_charge_job by splitting with '/' separator"""
        try:
            # Split by '/' and clean up each component
            components = [component.strip() for component in raw_charge_job.split('/')]
            
            self.logger.info(f"🔧 Parsed charge job components:")
            for i, component in enumerate(components):
                self.logger.info(f"   [{i}]: {component}")
            
            return components
            
        except Exception as e:
            self.logger.error(f"❌ Failed to parse raw_charge_job: {e}")
            return []
    
    def format_date(self, date_str: str) -> str:
        """Convert date from API format to form format"""
        try:
            if '/' in date_str and len(date_str.split('/')) == 3:
                return date_str  # Already formatted
            
            date_obj = datetime.strptime(date_str, "%Y-%m-%d")
            return date_obj.strftime("%d/%m/%Y")
        except Exception as e:
            self.logger.error(f"❌ Date formatting failed: {e}")
            return date_str

    def calculate_document_date(self, date_str: str) -> str:
        """
        Calculate document date using today's date with transaction date's month and year
        Example: Today=11/06/2025, Transaction=18/05/2025 -> Document=11/05/2025
        Uses today's day with transaction date's month/year
        """
        try:
            # Get today's date
            today = datetime.now()
            
            # Parse the transaction date
            if '/' in date_str and len(date_str.split('/')) == 3:
                # Already formatted as DD/MM/YYYY, convert to datetime
                trans_date_obj = datetime.strptime(date_str, "%d/%m/%Y")
            else:
                # YYYY-MM-DD format
                trans_date_obj = datetime.strptime(date_str, "%Y-%m-%d")
            
            # Use today's day with transaction date's month and year
            target_year = trans_date_obj.year
            target_month = trans_date_obj.month
            target_day = today.day
            
            # Handle day overflow (e.g., today=31st but target month has only 30 days)
            try:
                doc_date = datetime(year=target_year, month=target_month, day=target_day)
            except ValueError:
                # Day is out of range for target month, use the last day of target month
                last_day = monthrange(target_year, target_month)[1]
                doc_date = datetime(year=target_year, month=target_month, day=last_day)
                self.logger.info(f"📅 Day adjusted from {target_day} to {last_day} for month {target_month}/{target_year}")
            
            # Format as DD/MM/YYYY for form input
            formatted_doc_date = doc_date.strftime("%d/%m/%Y")
            
            self.logger.info(f"📅 Document date calculated: Today={today.strftime('%d/%m/%Y')}, Transaction={date_str} -> Document={formatted_doc_date}")
            return formatted_doc_date
            
        except Exception as e:
            self.logger.error(f"❌ Document date calculation failed: {e}")
            # Fallback: return the original date formatted
            return self.format_date(date_str)

    async def fill_document_date_field(self, driver, date_value: str) -> bool:
        """Fill document date field (MainContent_txtDocDate) with calculated date"""
        try:
            # Calculate document date (1 month earlier)
            document_date = self.calculate_document_date(date_value)
            
            self.logger.info(f"📅 Filling document date: {document_date}")
            
            # Use JavaScript to fill document date field
            script = f"""
                var docDateField = document.getElementById('MainContent_txtDocDate');
                if (docDateField) {{
                    docDateField.value = '{document_date}';
                    docDateField.dispatchEvent(new Event('change', {{bubbles: true}}));
                    docDateField.dispatchEvent(new Event('blur', {{bubbles: true}}));
                    
                    // Trigger the SetTrxDate() function if it exists
                    if (typeof SetTrxDate === 'function') {{
                        SetTrxDate();
                    }}
                    
                    return true;
                }} else {{
                    return false;
                }}
            """
            
            result = driver.execute_script(script)
            if result:
                self.logger.info(f"✅ Document date filled: {document_date}")
                await asyncio.sleep(1.5)  # Wait for SetTrxDate() to process
                return True
            else:
                self.logger.error("❌ Document date field not found")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Document date field filling failed: {e}")
            return False

    def create_overtime_entries(self, record: Dict) -> List[Dict]:
        """
        Create separate entries for normal and overtime hours
        Uses the same logic as APIDataAutomation
        """
        return self.api_automation.create_overtime_entries(record)

    async def select_transaction_type(self, driver, transaction_type: str) -> bool:
        """
        Select transaction type radio button (Normal or Overtime)
        Uses the same logic as APIDataAutomation
        """
        return await self.api_automation.select_transaction_type(driver, transaction_type)

    async def fill_hours_field(self, driver, hours_value: float) -> bool:
        """
        Fill the hours field with the specified value
        Uses the same logic as APIDataAutomation
        """
        return await self.api_automation.fill_hours_field(driver, hours_value)
    
    async def initialize_browser(self) -> bool:
        """Initialize browser and navigate to task register"""
        try:
            self.logger.info("🚀 Initializing browser...")
            self.browser_manager = PersistentBrowserManager(self.config)
            
            success = await self.browser_manager.initialize()
            if success:
                await self.browser_manager.navigate_to_task_register()
                self.logger.info("✅ Browser initialized and ready")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Browser initialization failed: {e}")
            return False
    
    async def fill_sequential_charge_job_fields(self, driver, charge_components: List[str]) -> bool:
        """Fill charge job components sequentially into autocomplete fields"""
        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.common.keys import Keys
            from selenium.common.exceptions import InvalidSessionIdException, StaleElementReferenceException
            
            # Find all autocomplete fields
            autocomplete_fields = driver.find_elements(By.CSS_SELECTOR, ".ui-autocomplete-input")
            
            # We expect field 0 = Employee, field 1+ = Charge job components
            # So charge components start from autocomplete field index 1
            
            self.logger.info(f"🔍 Available autocomplete fields: {len(autocomplete_fields)}")
            
            for i, component in enumerate(charge_components):
                field_index = i + 1  # Skip employee field (index 0)
                
                if field_index >= len(autocomplete_fields):
                    self.logger.warning(f"⚠️ No more autocomplete fields available for component {i}: {component}")
                    break
                
                self.logger.info(f"🔧 Filling field {field_index} with component {i}: {component}")
                
                # Retry logic for stale elements
                max_retries = 3
                retry_count = 0
                success = False
                
                while retry_count < max_retries and not success:
                    try:
                        # Refresh field list in case of stale elements
                        autocomplete_fields = driver.find_elements(By.CSS_SELECTOR, ".ui-autocomplete-input")
                        
                        if field_index >= len(autocomplete_fields):
                            self.logger.warning(f"⚠️ Field {field_index} not available after refresh")
                            break
                        
                        field = autocomplete_fields[field_index]
                        
                        # Check if field is visible and enabled
                        if not field.is_displayed() or not field.is_enabled():
                            self.logger.warning(f"⚠️ Field {field_index} not interactable, skipping component: {component}")
                            break
                        
                        # Clear and type component
                        field.clear()
                        field.send_keys(component)
                        await asyncio.sleep(1.5)  # Wait for autocomplete
                        
                        # Arrow down + Enter
                        field.send_keys(Keys.ARROW_DOWN)
                        await asyncio.sleep(0.8)
                        field.send_keys(Keys.ENTER)
                        await asyncio.sleep(1.5)  # Wait for processing
                        
                        self.logger.info(f"✅ Component {i} filled: {component}")
                        success = True
                        
                        # Check if more fields become available after this input
                        await asyncio.sleep(1)
                        autocomplete_fields = driver.find_elements(By.CSS_SELECTOR, ".ui-autocomplete-input")
                        self.logger.info(f"🔍 Autocomplete fields after filling component {i}: {len(autocomplete_fields)}")
                        
                    except (StaleElementReferenceException, InvalidSessionIdException) as e:
                        retry_count += 1
                        self.logger.warning(f"⚠️ Retry {retry_count}/{max_retries} for component {i}: {e}")
                        if retry_count < max_retries:
                            await asyncio.sleep(2)  # Wait before retry
                        else:
                            self.logger.error(f"❌ Failed to fill component {i} after {max_retries} retries")
                            return False
                    except Exception as e:
                        self.logger.error(f"❌ Error filling component {i}: {e}")
                        return False
                
                if not success:
                    self.logger.error(f"❌ Failed to fill component {i}: {component}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Sequential charge job filling failed: {e}")
            import traceback
            self.logger.error(f"❌ Traceback: {traceback.format_exc()}")
            return False
    
    async def process_single_record(self, driver, record: Dict, record_index: int) -> bool:
        """Process a single record with sequential charge job filling and overtime support"""
        try:
            employee_name = record.get('employee_name', '')
            date_value = record.get('date', '')
            raw_charge_job = record.get('raw_charge_job', '')
            transaction_type = record.get('transaction_type', 'Normal')
            hours = record.get('hours', 0)
            entry_type = record.get('entry_type', 'normal')
            
            self.logger.info(f"🎯 Processing record {record_index}: {employee_name}")
            self.logger.info(f"📅 Date: {date_value}")
            self.logger.info(f"🔧 Raw charge job: {raw_charge_job}")
            self.logger.info(f"🔘 Transaction type: {transaction_type}")
            self.logger.info(f"⏰ Hours: {hours}")
            self.logger.info(f"📝 Entry type: {entry_type}")
            
            from selenium.webdriver.common.by import By
            from selenium.webdriver.common.keys import Keys
            from selenium.common.exceptions import InvalidSessionIdException
            
            # Check if session is valid before proceeding
            try:
                driver.current_url  # Test if session is alive
            except InvalidSessionIdException:
                self.logger.warning(f"⚠️ Session lost for record {record_index}, reinitializing browser...")
                if not await self.reinitialize_browser():
                    return False
                driver = self.browser_manager.get_driver()
            
            # Step 0: Fill document date field (1 month earlier than transaction date)
            self.logger.info(f"📅 Step 0: Filling document date field...")
            
            try:
                doc_date_success = await self.fill_document_date_field(driver, date_value)
                if doc_date_success:
                    self.logger.info("✅ Document date field filled successfully")
                else:
                    self.logger.warning("⚠️ Document date field filling failed, continuing anyway...")
                    # Don't return False here, continue with the process
            except InvalidSessionIdException:
                self.logger.error(f"❌ Session lost during document date filling for record {record_index}")
                return False
            except Exception as e:
                self.logger.warning(f"⚠️ Document date filling error: {e}, continuing anyway...")
            
            # Step 1: Fill transaction date field
            if not date_value:
                self.logger.error("❌ No date field in record")
                return False
                
            formatted_date = self.format_date(date_value)
            self.logger.info(f"📅 Step 1: Filling transaction date: {formatted_date}")
            
            # Use JavaScript to fill date field
            script = f"""
                var dateField = document.getElementById('MainContent_txtTrxDate');
                if (dateField) {{
                    dateField.value = '{formatted_date}';
                    dateField.dispatchEvent(new Event('change', {{bubbles: true}}));
                    return true;
                }} else {{
                    return false;
                }}
            """
            
            try:
                result = driver.execute_script(script)
                if result:
                    self.logger.info(f"✅ Date filled: {formatted_date}")
                    # Send Enter to trigger reload
                    date_field = driver.find_element(By.ID, "MainContent_txtTrxDate")
                    date_field.send_keys(Keys.ENTER)
                    await asyncio.sleep(2)  # Wait for reload
                else:
                    self.logger.error("❌ Date field not found")
                    return False
            except InvalidSessionIdException:
                self.logger.error(f"❌ Session lost during date filling for record {record_index}")
                return False
            
            # Step 2: Fill employee field (first autocomplete field)
            if not employee_name:
                self.logger.error("❌ No employee_name field in record")
                return False
                
            self.logger.info(f"👤 Step 2: Filling employee: {employee_name}")
            
            try:
                autocomplete_fields = driver.find_elements(By.CSS_SELECTOR, ".ui-autocomplete-input")
                self.logger.info(f"🔍 Found {len(autocomplete_fields)} autocomplete fields")
                
                if len(autocomplete_fields) > 0:
                    employee_field = autocomplete_fields[0]
                    employee_field.clear()
                    employee_field.send_keys(employee_name)
                    await asyncio.sleep(1.5)
                    employee_field.send_keys(Keys.ARROW_DOWN)
                    await asyncio.sleep(0.8)
                    employee_field.send_keys(Keys.ENTER)
                    await asyncio.sleep(2)  # Wait for field processing
                    self.logger.info(f"✅ Employee filled: {employee_name}")
                else:
                    self.logger.error("❌ Employee field not found")
                    return False
            except InvalidSessionIdException:
                self.logger.error(f"❌ Session lost during employee filling for record {record_index}")
                return False

            # Step 3: Select transaction type (Normal or Overtime)
            self.logger.info(f"🔘 Step 3: Selecting transaction type: {transaction_type}")
            
            try:
                success = await self.select_transaction_type(driver, transaction_type)
                if success:
                    self.logger.info(f"✅ Transaction type selected: {transaction_type}")
                else:
                    self.logger.error(f"❌ Failed to select transaction type: {transaction_type}")
                    return False
            except InvalidSessionIdException:
                self.logger.error(f"❌ Session lost during transaction type selection for record {record_index}")
                return False
            
            # Step 4: Parse and fill charge job components sequentially
            if not raw_charge_job:
                self.logger.error("❌ No raw_charge_job field in record")
                return False
                
            charge_components = self.parse_raw_charge_job(raw_charge_job)
            
            if charge_components:
                self.logger.info(f"🔧 Step 4: Filling {len(charge_components)} charge job components sequentially...")
                success = await self.fill_sequential_charge_job_fields(driver, charge_components)
                
                if success:
                    self.logger.info("✅ All charge job components filled successfully")
                    
                    # Step 5: Fill hours field
                    self.logger.info(f"⏰ Step 5: Filling hours field with: {hours}")
                    
                    try:
                        hours_success = await self.fill_hours_field(driver, hours)
                        if hours_success:
                            self.logger.info(f"✅ Hours field filled: {hours}")
                        else:
                            self.logger.error(f"❌ Failed to fill hours field: {hours}")
                            return False
                    except InvalidSessionIdException:
                        self.logger.error(f"❌ Session lost during hours filling for record {record_index}")
                        return False
                    
                    # Step 6: Click Add button to save the record
                    try:
                        add_selectors = [
                            "input[value='Add']",
                            "button[id*='Add']", 
                            "input[id*='Add']",
                            "input[id*='btn'][id*='Add']",
                            "button[value='Add']"
                        ]
                        
                        add_button = None
                        for selector in add_selectors:
                            try:
                                add_button = driver.find_element(By.CSS_SELECTOR, selector)
                                if add_button.is_displayed() and add_button.is_enabled():
                                    self.logger.info(f"✅ Found Add button with selector: {selector}")
                                    break
                            except:
                                continue
                        
                        if add_button:
                            add_button.click()
                            self.logger.info("✅ Add button clicked - Record saved!")
                            await asyncio.sleep(3)  # Wait for form to reset/process
                            return True
                        else:
                            self.logger.warning("⚠️ Add button not found with any selector")
                            return False
                            
                    except Exception as e:
                        self.logger.error(f"❌ Add button handling error: {e}")
                        return False
                else:
                    return False
            else:
                self.logger.error("❌ No charge job components to fill")
                return False
            
        except InvalidSessionIdException:
            self.logger.error(f"❌ Browser session lost for record {record_index}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Record {record_index} processing failed: {e}")
            import traceback
            self.logger.error(f"❌ Traceback: {traceback.format_exc()}")
            return False

    async def reinitialize_browser(self) -> bool:
        """Reinitialize browser session if it's lost"""
        try:
            self.logger.info("🔄 Reinitializing browser session...")
            
            # Cleanup current session
            if self.browser_manager:
                await self.browser_manager.cleanup()
            
            # Reinitialize
            self.browser_manager = PersistentBrowserManager(self.config)
            success = await self.browser_manager.initialize()
            
            if success:
                await self.browser_manager.navigate_to_task_register()
                self.logger.info("✅ Browser session reinitialized successfully")
                return True
            else:
                self.logger.error("❌ Failed to reinitialize browser session")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Browser reinitialization failed: {e}")
            return False

    async def process_all_records_from_api(self) -> bool:
        """Process all records from real API data with sequential charge job filling and overtime support"""
        try:
            # Fetch real API data
            raw_records = await self.fetch_real_api_data()
            
            if not raw_records:
                self.logger.error("❌ No data received from API")
                return False
            
            if len(raw_records) == 0:
                self.logger.error("❌ API returned empty array")
                return False
            
            # Process records to create separate normal/overtime entries
            all_entries = []
            for record in raw_records:
                entries = self.create_overtime_entries(record)
                all_entries.extend(entries)
            
            self.logger.info(f"📊 Created {len(all_entries)} entries from {len(raw_records)} records")
            
            # Log overtime breakdown
            normal_entries = [e for e in all_entries if e.get('transaction_type') == 'Normal']
            overtime_entries = [e for e in all_entries if e.get('transaction_type') == 'Overtime']
            self.logger.info(f"📊 Normal entries: {len(normal_entries)}")
            self.logger.info(f"📊 Overtime entries: {len(overtime_entries)}")
            
            # Initialize browser
            if not await self.initialize_browser():
                return False
            
            driver = self.browser_manager.get_driver()
            if not driver:
                self.logger.error("❌ Driver not available")
                return False
            
            # Process each entry
            successful_entries = 0
            failed_entries = 0
            
            for i, entry in enumerate(all_entries, 1):
                self.logger.info(f"\n{'='*70}")
                self.logger.info(f"Processing Entry {i}/{len(all_entries)}")
                self.logger.info(f"Record ID: {entry.get('id', 'Unknown')}")
                self.logger.info(f"Employee: {entry.get('employee_name', 'Unknown')}")
                self.logger.info(f"Date: {entry.get('date', 'Unknown')}")
                self.logger.info(f"Type: {entry.get('transaction_type', 'Normal')} - Hours: {entry.get('hours', 0)}")
                self.logger.info(f"{'='*70}")
                
                # Process single entry
                success = await self.process_single_record(driver, entry, i)
                
                if success:
                    successful_entries += 1
                    self.logger.info(f"✅ Entry {i} processed successfully")
                    print(f"✅ Entry {i}/{len(all_entries)} completed: {entry.get('employee_name', 'Unknown')} ({entry.get('transaction_type', 'Normal')} - {entry.get('hours', 0)}h)")
                else:
                    failed_entries += 1
                    self.logger.error(f"❌ Entry {i} failed to process")
                    print(f"❌ Entry {i}/{len(all_entries)} failed: {entry.get('employee_name', 'Unknown')} ({entry.get('transaction_type', 'Normal')})")
                
                # Add delay between entries to prevent overwhelming the system
                if i < len(all_entries):
                    self.logger.info(f"⏳ Waiting 3 seconds before next entry...")
                    await asyncio.sleep(3)  # Increased wait time to allow form to reset naturally
                    
                    # No reload needed - form should reset automatically after Add button
                    self.logger.info(f"🔄 Form ready for next entry (no reload required)")
            
            # Summary
            self.logger.info(f"\n🎯 All Entries Processing Complete!")
            self.logger.info(f"📊 Original Records: {len(raw_records)}")
            self.logger.info(f"📊 Total Entries Created: {len(all_entries)}")
            self.logger.info(f"✅ Successfully processed: {successful_entries}/{len(all_entries)} entries")
            self.logger.info(f"❌ Failed entries: {failed_entries}/{len(all_entries)} entries")
            self.logger.info(f"📈 Success Rate: {(successful_entries/len(all_entries))*100:.1f}%")
            
            # Overtime breakdown
            processed_normal = len([e for e in all_entries[:successful_entries] if e.get('transaction_type') == 'Normal'])
            processed_overtime = len([e for e in all_entries[:successful_entries] if e.get('transaction_type') == 'Overtime'])
            
            print(f"\n🎯 FINAL SUMMARY:")
            print(f"📊 Original Records: {len(raw_records)}")
            print(f"📊 Total Entries: {len(all_entries)} ({len(normal_entries)} Normal + {len(overtime_entries)} Overtime)")
            print(f"✅ Successful Entries: {successful_entries}/{len(all_entries)}")
            print(f"   📝 Normal Entries: {processed_normal}")
            print(f"   ⏰ Overtime Entries: {processed_overtime}")
            print(f"❌ Failed Entries: {failed_entries}/{len(all_entries)}")
            print(f"📈 Success Rate: {(successful_entries/len(all_entries))*100:.1f}%")
            
            return successful_entries > 0
            
        except Exception as e:
            self.logger.error(f"❌ Batch processing failed: {e}")
            import traceback
            self.logger.error(f"❌ Traceback: {traceback.format_exc()}")
            return False
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.browser_manager:
                await self.browser_manager.cleanup()
        except Exception as e:
            self.logger.error(f"❌ Cleanup error: {e}")


def setup_logging():
    """Setup comprehensive logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('real_api_test.log', encoding='utf-8')
        ]
    )


async def main():
    """Main function to test real API data processing"""
    setup_logging()
    
    processor = RealAPIDataProcessor()
    
    try:
        print("🚀 Venus AutoFill - Real API Data Test with Overtime Support")
        print("=" * 65)
        print("🌐 Fetching from: http://localhost:5173/api/staging/data")
        print("🎯 Processing: All records with overtime handling")
        print("🔧 Strategy: Sequential charge job component filling")
        print("⏰ Overtime: Automatic splitting into Normal + Overtime entries")
        print("🔘 Transaction Types: Normal and Overtime radio button selection")
        print("📊 Features: Hours field filling, multiple entries per record")
        print("=" * 65)
        
        success = await processor.process_all_records_from_api()
        
        if success:
            print("\n🎉 Real API data processing completed successfully!")
            print("🔍 Browser will remain open for 30 seconds to inspect results...")
            await asyncio.sleep(30)
        else:
            print("\n❌ Processing failed - check logs for details")
            
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        logging.error(f"Main error: {e}")
    finally:
        await processor.cleanup()


if __name__ == "__main__":
    asyncio.run(main()) 