2025-06-11 12:19:37,602 - INFO - ✅ Configuration loaded from file
2025-06-11 12:19:37,604 - INFO - 🚀 Initializing browser manager...
2025-06-11 12:19:37,604 - INFO - Initializing persistent browser session...
2025-06-11 12:19:37,605 - INFO - Creating WebDriver instance...
2025-06-11 12:19:37,775 - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 12:19:38,122 - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 12:19:38,123 - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 12:19:38,125 - INFO - ====== WebDriver manager ======
2025-06-11 12:19:39,428 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 12:19:39,556 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 12:19:39,717 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 12:19:39,718 - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 12:19:39,718 - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 12:19:39,719 - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 12:19:39,719 - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 12:19:41,138 - INFO - ✅ WebDriver created and responsive
2025-06-11 12:19:41,200 - INFO - Chrome WebDriver created successfully
2025-06-11 12:19:41,211 - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-11 12:19:48,034 - INFO - WebDriver created and page loaded successfully
2025-06-11 12:19:48,034 - INFO - Performing initial login...
2025-06-11 12:20:00,160 - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 12:20:02,175 - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 12:20:02,176 - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-11 12:20:02,176 - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:20:04,034 - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:20:04,035 - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-11 12:20:04,043 - INFO - ✅ Initial login completed successfully
2025-06-11 12:20:04,044 - INFO - Started session keepalive thread
2025-06-11 12:20:04,044 - INFO - ✅ Persistent browser session initialized successfully
2025-06-11 12:20:04,045 - INFO - ✅ Browser initialized and logged in successfully
2025-06-11 12:20:04,045 - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:20:04,063 - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:20:06,724 - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:20:06,725 - INFO - ✅ SUCCESS: Reached task register page!
2025-06-11 12:20:06,725 - INFO - ✅ Navigated to task register page
2025-06-11 12:20:06,731 - INFO - 🔧 Initializing API automation...
2025-06-11 12:20:06,731 - INFO - ✅ API automation initialized
2025-06-11 12:20:06,740 - INFO - 🔄 Filling form with data: Ade Prasetya
2025-06-11 12:20:06,742 - ERROR - ❌ Date formatting failed: time data '30/05/2025' does not match format '%Y-%m-%d'
2025-06-11 12:20:06,755 - INFO - ✅ Date filled via JavaScript: 30/05/2025
2025-06-11 12:20:07,701 - INFO - 📤 Enter sent, waiting for reload...
2025-06-11 12:20:09,832 - INFO - 📝 Employee name typed: Ade Prasetya
2025-06-11 12:20:11,429 - INFO - ✅ Employee selected with arrow down + enter
2025-06-11 12:20:12,637 - INFO - 📝 Task Code typed: (OC7190) BOILER OPERATION
2025-06-11 12:20:14,216 - INFO - ✅ Task Code selected with arrow down + enter
2025-06-11 12:20:32,335 - ERROR - ❌ Station Code field filling failed: Message: 
Stacktrace:
	GetHandleVerifier [0x0x6e3783+63299]
	GetHandleVerifier [0x0x6e37c4+63364]
	(No symbol) [0x0x511113]
	(No symbol) [0x0x55987e]
	(No symbol) [0x0x559c1b]
	(No symbol) [0x0x5a2212]
	(No symbol) [0x0x57e5c4]
	(No symbol) [0x0x59fa4a]
	(No symbol) [0x0x57e376]
	(No symbol) [0x0x54d6e0]
	(No symbol) [0x0x54e544]
	GetHandleVerifier [0x0x93e073+2531379]
	GetHandleVerifier [0x0x939372+2511666]
	GetHandleVerifier [0x0x709efa+220858]
	GetHandleVerifier [0x0x6fa548+156936]
	GetHandleVerifier [0x0x700c7d+183357]
	GetHandleVerifier [0x0x6eb6e8+95912]
	GetHandleVerifier [0x0x6eb890+96336]
	GetHandleVerifier [0x0x6d666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 12:20:35,357 - INFO - 🔄 Filling form with data: Budi Santoso
2025-06-11 12:20:35,357 - ERROR - ❌ Date formatting failed: time data '01/06/2025' does not match format '%Y-%m-%d'
2025-06-11 12:20:35,368 - INFO - ✅ Date filled via JavaScript: 01/06/2025
2025-06-11 12:20:35,906 - INFO - 📤 Enter sent, waiting for reload...
2025-06-11 12:20:38,039 - INFO - 📝 Employee name typed: Budi Santoso
2025-06-11 12:20:39,623 - INFO - ✅ Employee selected with arrow down + enter
2025-06-11 12:20:40,797 - INFO - 📝 Task Code typed: (OC7191) TURBINE MAINTENANCE
2025-06-11 12:20:42,377 - INFO - ✅ Task Code selected with arrow down + enter
2025-06-11 12:21:00,450 - ERROR - ❌ Station Code field filling failed: Message: 
Stacktrace:
	GetHandleVerifier [0x0x6e3783+63299]
	GetHandleVerifier [0x0x6e37c4+63364]
	(No symbol) [0x0x511113]
	(No symbol) [0x0x55987e]
	(No symbol) [0x0x559c1b]
	(No symbol) [0x0x5a2212]
	(No symbol) [0x0x57e5c4]
	(No symbol) [0x0x59fa4a]
	(No symbol) [0x0x57e376]
	(No symbol) [0x0x54d6e0]
	(No symbol) [0x0x54e544]
	GetHandleVerifier [0x0x93e073+2531379]
	GetHandleVerifier [0x0x939372+2511666]
	GetHandleVerifier [0x0x709efa+220858]
	GetHandleVerifier [0x0x6fa548+156936]
	GetHandleVerifier [0x0x700c7d+183357]
	GetHandleVerifier [0x0x6eb6e8+95912]
	GetHandleVerifier [0x0x6eb890+96336]
	GetHandleVerifier [0x0x6d666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 12:21:30,456 - INFO - Cleaning up persistent browser manager...
2025-06-11 12:21:32,663 - INFO - Chrome WebDriver quit successfully
2025-06-11 12:21:32,664 - INFO - ✅ Browser manager cleaned up
2025-06-11 12:22:40,273 - INFO - ✅ Configuration loaded from file
2025-06-11 12:22:40,274 - INFO - 🚀 Initializing browser manager...
2025-06-11 12:22:40,275 - INFO - Initializing persistent browser session...
2025-06-11 12:22:40,275 - INFO - Creating WebDriver instance...
2025-06-11 12:22:40,489 - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 12:22:40,701 - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 12:22:40,701 - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 12:22:40,702 - INFO - ====== WebDriver manager ======
2025-06-11 12:22:41,788 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 12:22:41,910 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 12:22:42,057 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 12:22:42,058 - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 12:22:42,058 - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 12:22:42,059 - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 12:22:42,059 - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 12:22:43,296 - INFO - ✅ WebDriver created and responsive
2025-06-11 12:22:43,338 - INFO - Chrome WebDriver created successfully
2025-06-11 12:22:43,341 - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-11 12:22:46,199 - INFO - WebDriver created and page loaded successfully
2025-06-11 12:22:46,199 - INFO - Performing initial login...
2025-06-11 12:22:57,528 - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 12:22:59,535 - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 12:22:59,535 - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-11 12:22:59,536 - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:23:01,196 - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:23:01,197 - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-11 12:23:01,205 - INFO - ✅ Initial login completed successfully
2025-06-11 12:23:01,205 - INFO - Started session keepalive thread
2025-06-11 12:23:01,206 - INFO - ✅ Persistent browser session initialized successfully
2025-06-11 12:23:01,206 - INFO - ✅ Browser initialized and logged in successfully
2025-06-11 12:23:01,206 - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:23:01,220 - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:23:03,888 - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:23:03,889 - INFO - ✅ SUCCESS: Reached task register page!
2025-06-11 12:23:03,889 - INFO - ✅ Navigated to task register page
2025-06-11 12:23:03,897 - INFO - 🔧 Initializing API automation...
2025-06-11 12:23:03,897 - INFO - ✅ API automation initialized
2025-06-11 12:23:03,909 - INFO - 🔄 Filling form with data: Ade Prasetya
2025-06-11 12:23:03,909 - INFO - Date already formatted: 30/05/2025
2025-06-11 12:23:03,921 - INFO - ✅ Date filled via JavaScript: 30/05/2025
2025-06-11 12:23:04,415 - INFO - 📤 Enter sent, waiting for reload...
2025-06-11 12:23:06,549 - INFO - 📝 Employee name typed: Ade Prasetya
2025-06-11 12:23:08,145 - INFO - ✅ Employee selected with arrow down + enter
2025-06-11 12:23:17,720 - ERROR - ❌ Task Code field filling failed: 'NoneType' object has no attribute 'is_displayed'
2025-06-11 12:23:20,734 - ERROR - ❌ WebDriver not available
2025-06-11 12:23:50,740 - INFO - Cleaning up persistent browser manager...
2025-06-11 12:23:52,853 - INFO - Chrome WebDriver quit successfully
2025-06-11 12:23:52,853 - INFO - ✅ Browser manager cleaned up
2025-06-11 12:24:25,171 - INFO - ✅ Configuration loaded from file
2025-06-11 12:24:25,173 - INFO - 🚀 Initializing browser manager...
2025-06-11 12:24:25,173 - INFO - Initializing persistent browser session...
2025-06-11 12:24:25,173 - INFO - Creating WebDriver instance...
2025-06-11 12:24:25,244 - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 12:24:25,347 - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 12:24:25,348 - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 12:24:25,350 - INFO - ====== WebDriver manager ======
2025-06-11 12:24:26,575 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 12:24:26,701 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 12:24:26,830 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 12:24:26,830 - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 12:24:26,831 - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 12:24:26,831 - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 12:24:26,832 - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 12:24:28,079 - INFO - ✅ WebDriver created and responsive
2025-06-11 12:24:28,520 - INFO - Chrome WebDriver created successfully
2025-06-11 12:24:28,528 - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-11 12:24:31,894 - INFO - WebDriver created and page loaded successfully
2025-06-11 12:24:31,895 - INFO - Performing initial login...
2025-06-11 12:24:38,899 - INFO - ✅ Configuration loaded from file
2025-06-11 12:24:38,900 - INFO - 🚀 Initializing browser manager...
2025-06-11 12:24:38,900 - INFO - Initializing persistent browser session...
2025-06-11 12:24:38,900 - INFO - Creating WebDriver instance...
2025-06-11 12:24:38,955 - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 12:24:39,074 - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 12:24:39,075 - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 12:24:39,076 - INFO - ====== WebDriver manager ======
2025-06-11 12:24:40,342 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 12:24:40,814 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 12:24:41,255 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 12:24:41,255 - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 12:24:41,255 - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 12:24:41,255 - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 12:24:41,255 - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 12:24:42,490 - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 12:24:42,534 - INFO - ✅ WebDriver created and responsive
2025-06-11 12:24:42,608 - INFO - Chrome WebDriver created successfully
2025-06-11 12:24:42,614 - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-11 12:24:44,504 - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 12:24:44,505 - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-11 12:24:44,505 - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:24:46,347 - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:24:46,348 - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-11 12:24:46,356 - INFO - ✅ Initial login completed successfully
2025-06-11 12:24:46,356 - INFO - Started session keepalive thread
2025-06-11 12:24:46,357 - INFO - ✅ Persistent browser session initialized successfully
2025-06-11 12:24:46,357 - INFO - ✅ Browser initialized and logged in successfully
2025-06-11 12:24:46,357 - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:24:46,378 - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:24:47,094 - INFO - WebDriver created and page loaded successfully
2025-06-11 12:24:47,094 - INFO - Performing initial login...
2025-06-11 12:24:49,030 - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:24:49,030 - INFO - ✅ SUCCESS: Reached task register page!
2025-06-11 12:24:49,031 - INFO - ✅ Navigated to task register page
2025-06-11 12:24:49,038 - INFO - 🔧 Initializing API automation...
2025-06-11 12:24:49,038 - INFO - ✅ API automation initialized
2025-06-11 12:24:49,049 - INFO - 🔄 Filling form with data: Ade Prasetya
2025-06-11 12:24:49,049 - INFO - Date already formatted: 30/05/2025
2025-06-11 12:24:49,062 - INFO - ✅ Date filled via JavaScript: 30/05/2025
2025-06-11 12:24:49,579 - INFO - 📤 Enter sent, waiting for reload...
2025-06-11 12:24:51,736 - INFO - 📝 Employee name typed: Ade Prasetya
2025-06-11 12:24:51,831 - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 12:24:53,315 - INFO - ✅ Employee selected with arrow down + enter
2025-06-11 12:24:56,495 - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 12:24:56,496 - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-11 12:24:56,496 - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:24:58,149 - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:24:58,149 - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-11 12:24:58,156 - INFO - ✅ Initial login completed successfully
2025-06-11 12:24:58,157 - INFO - Started session keepalive thread
2025-06-11 12:24:58,157 - INFO - ✅ Persistent browser session initialized successfully
2025-06-11 12:24:58,158 - INFO - ✅ Browser initialized and logged in successfully
2025-06-11 12:24:58,158 - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:24:58,175 - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:25:00,831 - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:25:00,831 - INFO - ✅ SUCCESS: Reached task register page!
2025-06-11 12:25:00,832 - INFO - ✅ Navigated to task register page
2025-06-11 12:25:00,838 - INFO - 🔧 Initializing API automation...
2025-06-11 12:25:00,839 - INFO - ✅ API automation initialized
2025-06-11 12:25:00,874 - INFO - 🔄 Filling form with data: Ade Prasetya
2025-06-11 12:25:00,875 - INFO - Date already formatted: 30/05/2025
2025-06-11 12:25:00,885 - INFO - ✅ Date filled via JavaScript: 30/05/2025
2025-06-11 12:25:01,364 - INFO - 📤 Enter sent, waiting for reload...
2025-06-11 12:25:03,470 - INFO - 📝 Employee name typed: Ade Prasetya
2025-06-11 12:25:05,046 - INFO - ✅ Employee selected with arrow down + enter
2025-06-11 12:25:19,919 - ERROR - ❌ Task Code field not found with any selector
2025-06-11 12:25:22,933 - INFO - 🔄 Filling form with data: Budi Santoso
2025-06-11 12:25:22,934 - INFO - Date already formatted: 01/06/2025
2025-06-11 12:25:22,944 - INFO - ✅ Date filled via JavaScript: 01/06/2025
2025-06-11 12:25:23,469 - INFO - 📤 Enter sent, waiting for reload...
2025-06-11 12:25:25,627 - INFO - 📝 Employee name typed: Budi Santoso
2025-06-11 12:25:27,196 - INFO - ✅ Employee selected with arrow down + enter
2025-06-11 12:25:31,708 - ERROR - ❌ Task Code field not found with any selector
2025-06-11 12:25:34,724 - INFO - 🔄 Filling form with data: Budi Santoso
2025-06-11 12:25:34,724 - INFO - Date already formatted: 01/06/2025
2025-06-11 12:25:34,735 - INFO - ✅ Date filled via JavaScript: 01/06/2025
2025-06-11 12:25:34,986 - ERROR - ❌ Task Code field filling failed: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))
2025-06-11 12:25:35,305 - INFO - 📤 Enter sent, waiting for reload...
2025-06-11 12:25:37,419 - INFO - 📝 Employee name typed: Budi Santoso
2025-06-11 12:25:38,991 - INFO - ✅ Employee selected with arrow down + enter
2025-06-11 12:26:04,992 - INFO - Cleaning up persistent browser manager...
2025-06-11 12:26:05,584 - ERROR - ❌ Task Code field not found with any selector
2025-06-11 12:26:09,044 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E899A4C8C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/12687306daf5b9990aceb303e82599b9
2025-06-11 12:26:13,100 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E899A4C5C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/12687306daf5b9990aceb303e82599b9
2025-06-11 12:26:17,188 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E899A4CFE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/12687306daf5b9990aceb303e82599b9
2025-06-11 12:26:25,344 - INFO - Chrome WebDriver quit successfully
2025-06-11 12:26:25,344 - INFO - ✅ Browser manager cleaned up
2025-06-11 12:26:35,601 - INFO - Cleaning up persistent browser manager...
2025-06-11 12:26:37,767 - INFO - Chrome WebDriver quit successfully
2025-06-11 12:26:37,767 - INFO - ✅ Browser manager cleaned up
2025-06-11 12:27:15,740 - INFO - ✅ Configuration loaded from file
2025-06-11 12:27:15,741 - INFO - 🚀 Initializing browser manager...
2025-06-11 12:27:15,741 - INFO - Initializing persistent browser session...
2025-06-11 12:27:15,742 - INFO - Creating WebDriver instance...
2025-06-11 12:27:15,874 - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 12:27:15,956 - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 12:27:15,956 - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 12:27:15,958 - INFO - ====== WebDriver manager ======
2025-06-11 12:27:17,069 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 12:27:17,293 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 12:27:17,410 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 12:27:17,410 - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 12:27:17,410 - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 12:27:17,410 - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 12:27:17,410 - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 12:27:18,609 - INFO - ✅ WebDriver created and responsive
2025-06-11 12:27:18,683 - INFO - Chrome WebDriver created successfully
2025-06-11 12:27:18,695 - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-11 12:27:22,576 - INFO - WebDriver created and page loaded successfully
2025-06-11 12:27:22,576 - INFO - Performing initial login...
2025-06-11 12:27:34,108 - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 12:27:36,124 - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 12:27:36,125 - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-11 12:27:36,125 - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:27:37,800 - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:27:37,801 - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-11 12:27:37,808 - INFO - ✅ Initial login completed successfully
2025-06-11 12:27:37,809 - INFO - Started session keepalive thread
2025-06-11 12:27:37,810 - INFO - ✅ Persistent browser session initialized successfully
2025-06-11 12:27:37,810 - INFO - ✅ Browser initialized and logged in successfully
2025-06-11 12:27:37,810 - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:27:37,827 - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:27:40,517 - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:27:40,518 - INFO - ✅ SUCCESS: Reached task register page!
2025-06-11 12:27:40,518 - INFO - ✅ Navigated to task register page
2025-06-11 12:27:40,525 - INFO - 🔧 Initializing API automation...
2025-06-11 12:27:40,526 - INFO - ✅ API automation initialized
2025-06-11 12:27:40,562 - INFO - 🔄 Filling form with data: Ade Prasetya
2025-06-11 12:27:40,562 - INFO - Date already formatted: 30/05/2025
2025-06-11 12:27:40,571 - INFO - ✅ Date filled via JavaScript: 30/05/2025
2025-06-11 12:27:41,245 - INFO - 📤 Enter sent, waiting for reload...
2025-06-11 12:27:43,373 - INFO - 📝 Employee name typed: Ade Prasetya
2025-06-11 12:27:44,960 - INFO - ✅ Employee selected with arrow down + enter
2025-06-11 12:28:11,630 - ERROR - ❌ Task Code field not found with any selector
2025-06-11 12:28:14,652 - INFO - 🔄 Filling form with data: Budi Santoso
2025-06-11 12:28:14,652 - INFO - Date already formatted: 01/06/2025
2025-06-11 12:28:14,662 - INFO - ✅ Date filled via JavaScript: 01/06/2025
2025-06-11 12:28:15,173 - INFO - 📤 Enter sent, waiting for reload...
2025-06-11 12:28:17,310 - INFO - 📝 Employee name typed: Budi Santoso
2025-06-11 12:28:18,897 - INFO - ✅ Employee selected with arrow down + enter
2025-06-11 12:28:45,530 - ERROR - ❌ Task Code field not found with any selector
2025-06-11 12:29:15,537 - INFO - Cleaning up persistent browser manager...
2025-06-11 12:29:17,788 - INFO - Chrome WebDriver quit successfully
2025-06-11 12:29:17,789 - INFO - ✅ Browser manager cleaned up
2025-06-11 12:30:15,224 - INFO - ✅ Configuration loaded from file
2025-06-11 12:30:15,225 - INFO - 🚀 Initializing browser manager...
2025-06-11 12:30:15,225 - INFO - Initializing persistent browser session...
2025-06-11 12:30:15,226 - INFO - Creating WebDriver instance...
2025-06-11 12:30:15,283 - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 12:30:15,354 - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 12:30:15,354 - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 12:30:15,356 - INFO - ====== WebDriver manager ======
2025-06-11 12:30:16,656 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 12:30:16,840 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 12:30:16,975 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 12:30:16,977 - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 12:30:16,977 - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 12:30:16,979 - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 12:30:16,980 - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 12:30:18,258 - INFO - ✅ WebDriver created and responsive
2025-06-11 12:30:18,331 - INFO - Chrome WebDriver created successfully
2025-06-11 12:30:18,341 - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-11 12:30:21,509 - INFO - WebDriver created and page loaded successfully
2025-06-11 12:30:21,509 - INFO - Performing initial login...
2025-06-11 12:30:34,064 - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 12:30:36,076 - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 12:30:36,077 - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-11 12:30:36,077 - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:30:37,756 - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:30:37,756 - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-11 12:30:37,770 - INFO - ✅ Initial login completed successfully
2025-06-11 12:30:37,770 - INFO - Started session keepalive thread
2025-06-11 12:30:37,771 - INFO - ✅ Persistent browser session initialized successfully
2025-06-11 12:30:37,771 - INFO - ✅ Browser initialized and logged in successfully
2025-06-11 12:30:37,771 - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:30:37,788 - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:30:40,404 - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:30:40,404 - INFO - ✅ SUCCESS: Reached task register page!
2025-06-11 12:30:40,404 - INFO - ✅ Navigated to task register page
2025-06-11 12:30:40,416 - INFO - 🔧 Initializing API automation...
2025-06-11 12:30:40,416 - INFO - ✅ API automation initialized
2025-06-11 12:30:40,466 - INFO - 🔄 Filling form with data: Ade Prasetya
2025-06-11 12:30:40,466 - INFO - Date already formatted: 30/05/2025
2025-06-11 12:30:40,479 - INFO - ✅ Date filled via JavaScript: 30/05/2025
2025-06-11 12:30:40,985 - INFO - 📤 Enter sent, waiting for reload...
2025-06-11 12:30:43,019 - INFO - ✅ Found Employee field at index 0
2025-06-11 12:30:43,136 - INFO - 📝 Employee typed: Ade Prasetya
2025-06-11 12:30:45,518 - INFO - ✅ Employee selected with arrow down + enter
2025-06-11 12:30:47,076 - INFO - ✅ Found Task Code field at index 1
2025-06-11 12:30:47,209 - INFO - 📝 Task Code typed: (OC7190) BOILER OPERATION
2025-06-11 12:30:49,617 - INFO - ✅ Task Code selected with arrow down + enter
2025-06-11 12:30:51,120 - INFO - ✅ Form filled successfully (Date + Employee + Task)
2025-06-11 12:30:51,120 - INFO - ℹ️ Station, Machine, Expense codes may be handled differently in this form
2025-06-11 12:30:54,142 - INFO - 🔄 Filling form with data: Budi Santoso
2025-06-11 12:30:54,142 - INFO - Date already formatted: 01/06/2025
2025-06-11 12:30:54,161 - INFO - ✅ Date filled via JavaScript: 01/06/2025
2025-06-11 12:30:56,028 - INFO - 📤 Enter sent, waiting for reload...
2025-06-11 12:30:58,154 - INFO - ✅ Found Employee field at index 0
2025-06-11 12:30:58,277 - INFO - 📝 Employee typed: Budi Santoso
2025-06-11 12:31:00,662 - INFO - ✅ Employee selected with arrow down + enter
2025-06-11 12:31:02,783 - INFO - ✅ Found Task Code field at index 1
2025-06-11 12:31:02,930 - INFO - 📝 Task Code typed: (OC7191) TURBINE MAINTENANCE
2025-06-11 12:31:05,328 - INFO - ✅ Task Code selected with arrow down + enter
2025-06-11 12:31:06,829 - INFO - ✅ Form filled successfully (Date + Employee + Task)
2025-06-11 12:31:06,829 - INFO - ℹ️ Station, Machine, Expense codes may be handled differently in this form
2025-06-11 12:31:30,945 - INFO - ✅ Configuration loaded from file
2025-06-11 12:31:30,945 - INFO - 🚀 Initializing browser manager...
2025-06-11 12:31:30,945 - INFO - Initializing persistent browser session...
2025-06-11 12:31:30,945 - INFO - Creating WebDriver instance...
2025-06-11 12:31:31,000 - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 12:31:31,157 - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 12:31:31,157 - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 12:31:31,158 - INFO - ====== WebDriver manager ======
2025-06-11 12:31:32,275 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 12:31:32,405 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 12:31:32,588 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 12:31:32,588 - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 12:31:32,588 - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 12:31:32,588 - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 12:31:32,588 - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 12:31:34,124 - INFO - ✅ WebDriver created and responsive
2025-06-11 12:31:34,187 - INFO - Chrome WebDriver created successfully
2025-06-11 12:31:34,224 - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-11 12:31:36,842 - INFO - Cleaning up persistent browser manager...
2025-06-11 12:31:37,769 - INFO - WebDriver created and page loaded successfully
2025-06-11 12:31:37,769 - INFO - Performing initial login...
2025-06-11 12:31:39,022 - INFO - Chrome WebDriver quit successfully
2025-06-11 12:31:39,022 - INFO - ✅ Browser manager cleaned up
2025-06-11 12:31:50,838 - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 12:31:52,851 - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 12:31:52,851 - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-11 12:31:52,852 - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:31:54,598 - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:31:54,598 - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-11 12:31:54,607 - INFO - ✅ Initial login completed successfully
2025-06-11 12:31:54,608 - INFO - Started session keepalive thread
2025-06-11 12:31:54,608 - INFO - ✅ Persistent browser session initialized successfully
2025-06-11 12:31:54,608 - INFO - ✅ Browser initialized and logged in successfully
2025-06-11 12:31:54,608 - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:31:54,631 - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:31:57,292 - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:31:57,292 - INFO - ✅ SUCCESS: Reached task register page!
2025-06-11 12:31:57,294 - INFO - ✅ Navigated to task register page
2025-06-11 12:31:57,301 - INFO - 🔧 Initializing API automation...
2025-06-11 12:31:57,302 - INFO - ✅ API automation initialized
2025-06-11 12:31:57,354 - INFO - 🔄 Filling form with data: Ade Prasetya
2025-06-11 12:31:57,354 - INFO - Date already formatted: 30/05/2025
2025-06-11 12:31:57,386 - INFO - ✅ Date filled via JavaScript: 30/05/2025
2025-06-11 12:31:57,962 - INFO - 📤 Enter sent, waiting for reload...
2025-06-11 12:32:00,009 - INFO - ✅ Found Employee field at index 0
2025-06-11 12:32:00,140 - INFO - 📝 Employee typed: Ade Prasetya
2025-06-11 12:32:02,522 - INFO - ✅ Employee selected with arrow down + enter
2025-06-11 12:32:04,083 - INFO - ✅ Found Task Code field at index 1
2025-06-11 12:32:04,247 - INFO - 📝 Task Code typed: (OC7190) BOILER OPERATION
2025-06-11 12:32:08,033 - INFO - ✅ Task Code selected with arrow down + enter
2025-06-11 12:32:09,536 - INFO - ✅ Form filled successfully (Date + Employee + Task)
2025-06-11 12:32:09,536 - INFO - ℹ️ Station, Machine, Expense codes may be handled differently in this form
2025-06-11 12:32:12,551 - INFO - 🔄 Filling form with data: Budi Santoso
2025-06-11 12:32:12,551 - INFO - Date already formatted: 01/06/2025
2025-06-11 12:32:12,570 - INFO - ✅ Date filled via JavaScript: 01/06/2025
2025-06-11 12:32:14,367 - INFO - 📤 Enter sent, waiting for reload...
2025-06-11 12:32:16,837 - INFO - ✅ Found Employee field at index 0
2025-06-11 12:32:16,953 - INFO - 📝 Employee typed: Budi Santoso
2025-06-11 12:32:19,329 - INFO - ✅ Employee selected with arrow down + enter
2025-06-11 12:32:21,528 - INFO - ✅ Found Task Code field at index 1
2025-06-11 12:32:21,648 - INFO - 📝 Task Code typed: (OC7191) TURBINE MAINTENANCE
2025-06-11 12:32:24,004 - INFO - ✅ Task Code selected with arrow down + enter
2025-06-11 12:32:25,505 - INFO - ✅ Form filled successfully (Date + Employee + Task)
2025-06-11 12:32:25,505 - INFO - ℹ️ Station, Machine, Expense codes may be handled differently in this form
2025-06-11 12:32:55,510 - INFO - Cleaning up persistent browser manager...
2025-06-11 12:32:57,701 - INFO - Chrome WebDriver quit successfully
2025-06-11 12:32:57,701 - INFO - ✅ Browser manager cleaned up
2025-06-11 12:37:17,964 - INFO - ✅ Configuration loaded from file
2025-06-11 12:37:17,964 - INFO - 🚀 Initializing browser manager...
2025-06-11 12:37:17,964 - INFO - Initializing persistent browser session...
2025-06-11 12:37:17,964 - INFO - Creating WebDriver instance...
2025-06-11 12:37:18,092 - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 12:37:18,269 - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 12:37:18,269 - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 12:37:18,269 - INFO - ====== WebDriver manager ======
2025-06-11 12:37:19,266 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 12:37:19,389 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 12:37:19,521 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 12:37:19,521 - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 12:37:19,521 - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 12:37:19,521 - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 12:37:19,521 - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 12:37:20,711 - INFO - ✅ WebDriver created and responsive
2025-06-11 12:37:20,775 - INFO - Chrome WebDriver created successfully
2025-06-11 12:37:20,783 - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-11 12:37:24,949 - INFO - WebDriver created and page loaded successfully
2025-06-11 12:37:24,949 - INFO - Performing initial login...
2025-06-11 12:37:35,854 - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 12:37:37,858 - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 12:37:37,858 - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-11 12:37:37,858 - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:37:39,585 - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:37:39,585 - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-11 12:37:39,585 - INFO - ✅ Initial login completed successfully
2025-06-11 12:37:39,585 - INFO - Started session keepalive thread
2025-06-11 12:37:39,585 - INFO - ✅ Persistent browser session initialized successfully
2025-06-11 12:37:39,596 - INFO - ✅ Browser initialized and logged in successfully
2025-06-11 12:37:39,596 - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:37:39,608 - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:37:42,252 - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:37:42,252 - INFO - ✅ SUCCESS: Reached task register page!
2025-06-11 12:37:42,252 - INFO - ✅ Navigated to task register page
2025-06-11 12:37:42,269 - INFO - 🔧 Initializing API automation...
2025-06-11 12:37:42,269 - INFO - ✅ API automation initialized
2025-06-11 12:37:42,316 - INFO - 🔄 Filling form with data: Ade Prasetya
2025-06-11 12:37:42,317 - INFO - Date already formatted: 30/05/2025
2025-06-11 12:37:42,329 - INFO - ✅ Date filled via JavaScript: 30/05/2025
2025-06-11 12:37:42,871 - INFO - 📤 Enter sent, waiting for reload...
2025-06-11 12:37:44,915 - INFO - ✅ Found Employee field at index 0
2025-06-11 12:37:45,038 - INFO - 📝 Employee typed: Ade Prasetya
2025-06-11 12:37:47,490 - INFO - ✅ Employee selected with arrow down + enter
2025-06-11 12:37:49,039 - INFO - ✅ Found Task Code field at index 1
2025-06-11 12:37:49,180 - INFO - 📝 Task Code typed: (OC7190) BOILER OPERATION
2025-06-11 12:37:51,566 - INFO - ✅ Task Code selected with arrow down + enter
2025-06-11 12:37:53,070 - INFO - ✅ Form filled successfully (Date + Employee + Task)
2025-06-11 12:37:53,071 - INFO - ℹ️ Station, Machine, Expense codes may be handled differently in this form
2025-06-11 12:37:56,100 - INFO - 🔄 Filling form with data: Budi Santoso
2025-06-11 12:37:56,112 - INFO - Date already formatted: 01/06/2025
2025-06-11 12:37:56,131 - INFO - ✅ Date filled via JavaScript: 01/06/2025
2025-06-11 12:37:58,364 - INFO - 📤 Enter sent, waiting for reload...
2025-06-11 12:38:01,674 - INFO - ✅ Found Employee field at index 0
2025-06-11 12:38:01,798 - INFO - 📝 Employee typed: Budi Santoso
2025-06-11 12:38:04,188 - INFO - ✅ Employee selected with arrow down + enter
2025-06-11 12:38:05,917 - INFO - ✅ Found Task Code field at index 1
2025-06-11 12:38:06,070 - INFO - 📝 Task Code typed: (OC7191) TURBINE MAINTENANCE
2025-06-11 12:38:08,489 - INFO - ✅ Task Code selected with arrow down + enter
2025-06-11 12:38:10,002 - INFO - ✅ Form filled successfully (Date + Employee + Task)
2025-06-11 12:38:10,002 - INFO - ℹ️ Station, Machine, Expense codes may be handled differently in this form
2025-06-11 12:38:40,003 - INFO - Cleaning up persistent browser manager...
2025-06-11 12:38:42,143 - INFO - Chrome WebDriver quit successfully
2025-06-11 12:38:42,143 - INFO - ✅ Browser manager cleaned up
