"""
Simple test for overtime entry creation logic
No browser required - just tests the logic
"""

import logging
from src.core.api_data_automation import APIDataAutomation

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(message)s')

# Test data
test_records = [
    {
        "employee_name": "<PERSON><PERSON> Prasetya",
        "date": "2025-05-18", 
        "regular_hours": 7.0,
        "overtime_hours": 16.0,
        "raw_charge_job": "(OC7190) BOILER OPERATION / STN-BLR (STATION BOILER) / BLR00000 (LABOUR COST) / L (LABOUR)",
        "id": "test-1"
    },
    {
        "employee_name": "<PERSON><PERSON>",
        "date": "2025-05-19",
        "regular_hours": 8.0,
        "overtime_hours": 0.0,
        "raw_charge_job": "(OC7191) TURBINE MAINTENANCE / STN-TUR (STATION TURBINE) / TUR00001 (TURBINE COST) / M (MATERIAL)",
        "id": "test-2"
    },
    {
        "employee_name": "<PERSON><PERSON><PERSON>",
        "date": "2025-05-20",
        "regular_hours": 0.0,
        "overtime_hours": 4.0,
        "raw_charge_job": "(OC7240) LABORATORY ANALYSIS / STN-LAB (STATION LABORATORY) / LAB00000 (LABOUR COST) / L (LABOUR)",
        "id": "test-3"
    },
    {
        "employee_name": "Dedi Kurniawan", 
        "date": "2025-05-21",
        "regular_hours": 0.0,
        "overtime_hours": 0.0,
        "raw_charge_job": "(OC7250) WORKSHOP MAINTENANCE / STN-WRK (STATION WORKSHOP) / WRK00000 (LABOUR COST) / L (LABOUR)",
        "id": "test-4"
    }
]

def test_overtime_entries():
    """Test overtime entry creation"""
    print("="*80)
    print("TESTING OVERTIME ENTRY CREATION LOGIC")
    print("="*80)
    
    automation = APIDataAutomation()
    
    total_records = len(test_records)
    total_entries = 0
    
    for i, record in enumerate(test_records, 1):
        print(f"\n--- Test {i}/{total_records}: {record['employee_name']} ---")
        print(f"📅 Date: {record['date']}")
        print(f"⏰ Regular Hours: {record['regular_hours']}")
        print(f"⏱️  Overtime Hours: {record['overtime_hours']}")
        print(f"🎯 Total Hours: {record['regular_hours'] + record['overtime_hours']}")
        
        # Create entries
        entries = automation.create_overtime_entries(record)
        total_entries += len(entries)
        
        print(f"✅ Created {len(entries)} entries:")
        for j, entry in enumerate(entries, 1):
            entry_type = entry.get('entry_type', 'unknown')
            transaction_type = entry.get('transaction_type', 'Unknown')
            hours = entry.get('hours', 0)
            
            print(f"   Entry {j}: {transaction_type} ({entry_type}) - {hours} hours")
            
            # Verify entry has all required fields
            required_fields = ['employee_name', 'date', 'transaction_type', 'hours', 'entry_type']
            missing_fields = [field for field in required_fields if field not in entry]
            
            if missing_fields:
                print(f"   ❌ Missing fields: {missing_fields}")
            else:
                print(f"   ✅ All required fields present")
    
    print(f"\n{'='*80}")
    print(f"SUMMARY")
    print(f"{'='*80}")
    print(f"📊 Total Records Processed: {total_records}")
    print(f"📊 Total Entries Created: {total_entries}")
    print(f"📊 Average Entries per Record: {total_entries/total_records:.1f}")
    
    # Test scenarios summary
    print(f"\n📋 Test Scenarios Covered:")
    print(f"   ✅ Normal + Overtime (Record 1): 2 entries expected")
    print(f"   ✅ Normal Only (Record 2): 1 entry expected")
    print(f"   ✅ Overtime Only (Record 3): 1 entry expected") 
    print(f"   ✅ Zero Hours (Record 4): 1 entry expected")
    
    return total_entries == 5  # Expected: 2+1+1+1 = 5 entries

def test_charge_job_parsing():
    """Test charge job parsing"""
    print(f"\n{'='*80}")
    print("TESTING CHARGE JOB PARSING")
    print("="*80)
    
    automation = APIDataAutomation()
    
    for i, record in enumerate(test_records, 1):
        print(f"\n--- Parsing Test {i}: {record['employee_name']} ---")
        print(f"📝 Raw Charge Job: {record['raw_charge_job']}")
        
        task_code, station_code, machine_code, expense_code = automation.parse_charge_job(
            record['raw_charge_job']
        )
        
        print(f"✅ Parsed Components:")
        print(f"   🎯 Task Code: {task_code}")
        print(f"   🏭 Station Code: {station_code}")
        print(f"   ⚙️  Machine Code: {machine_code}")
        print(f"   💰 Expense Code: {expense_code}")

if __name__ == "__main__":
    print("VENUS AUTOFILL - OVERTIME LOGIC TEST")
    print("="*50)
    
    # Test 1: Entry creation logic
    success = test_overtime_entries()
    
    # Test 2: Charge job parsing
    test_charge_job_parsing()
    
    print(f"\n{'='*80}")
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Overtime logic is working correctly")
    else:
        print("❌ TESTS FAILED!")
        print("⚠️  Check the logic implementation")
    print("="*80) 