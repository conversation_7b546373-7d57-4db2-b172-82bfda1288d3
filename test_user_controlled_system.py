#!/usr/bin/env python3
"""
Test Enhanced User-Controlled Automation System
Verifies that the system correctly initializes and processes selected records
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Import the enhanced system
from run_user_controlled_automation import UserControlledAutomationSystem, setup_logging


async def test_system_initialization():
    """Test that the system initializes correctly"""
    print("🧪 TESTING SYSTEM INITIALIZATION")
    print("="*50)
    
    try:
        # Setup logging
        setup_logging()
        
        # Initialize system
        system = UserControlledAutomationSystem()
        
        # Test API data fetching
        print("📊 Testing API data fetching...")
        staging_data = await system.fetch_staging_data()
        
        if staging_data:
            print(f"✅ Successfully fetched {len(staging_data)} records from API")
            
            # Display first record structure
            if len(staging_data) > 0:
                first_record = staging_data[0]
                print(f"\n📋 First Record Structure:")
                print(f"🆔 ID: {first_record.get('id', 'N/A')}")
                print(f"👤 Employee: {first_record.get('employee_name', 'N/A')}")
                print(f"📅 Date: {first_record.get('date', 'N/A')}")
                print(f"🔧 Raw Charge Job: {first_record.get('raw_charge_job', 'N/A')}")
                
                # Test charge job parsing using the processor method
                charge_components = system.processor.parse_raw_charge_job(first_record.get('raw_charge_job', ''))
                print(f"🔧 Parsed Components: {len(charge_components)} parts")
                for i, component in enumerate(charge_components):
                    print(f"   [{i}]: {component}")
                
                # Test overtime entry creation using the processor method
                overtime_entries = system.processor.create_overtime_entries(first_record)
                print(f"⏰ Overtime Entries: {len(overtime_entries)} entries created")
                for i, entry in enumerate(overtime_entries):
                    print(f"   Entry {i+1}: {entry.get('transaction_type', 'N/A')} - {entry.get('hours', 0)}h")
        else:
            print("⚠️ No staging data available (API might be down)")
            
        # Test display function
        print(f"\n📺 Testing display function...")
        test_indices = [0, 1] if len(staging_data) >= 2 else [0] if len(staging_data) >= 1 else []
        
        if test_indices:
            system.display_selected_records(test_indices, staging_data)
            print("✅ Display function works correctly")
        else:
            print("⚠️ Cannot test display function - no data available")
        
        print(f"\n✅ SYSTEM INITIALIZATION TEST COMPLETED")
        return True
        
    except Exception as e:
        print(f"❌ System initialization test failed: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False


async def test_web_interface():
    """Test web interface startup"""
    print("\n🌐 TESTING WEB INTERFACE")
    print("="*50)
    
    try:
        system = UserControlledAutomationSystem()
        
        # Test web interface startup
        print("🚀 Starting web interface...")
        success = system.start_web_interface()
        
        if success:
            print("✅ Web interface started successfully")
            print("📱 Interface available at: http://localhost:5000")
            
            # Test API endpoints
            import requests
            import time
            
            # Wait for server to start
            time.sleep(3)
            
            try:
                # Test staging data endpoint
                response = requests.get("http://localhost:5000/api/staging/data", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ Staging data endpoint works: {len(data.get('data', []))} records")
                else:
                    print(f"⚠️ Staging data endpoint returned status: {response.status_code}")
                    
                # Test employees endpoint
                response = requests.get("http://localhost:5000/api/employees", timeout=10)
                if response.status_code == 200:
                    employees = response.json()
                    print(f"✅ Employees endpoint works: {len(employees)} employees")
                else:
                    print(f"⚠️ Employees endpoint returned status: {response.status_code}")
                    
            except Exception as e:
                print(f"⚠️ Could not test API endpoints: {e}")
                
        else:
            print("❌ Web interface failed to start")
            return False
            
        print("✅ WEB INTERFACE TEST COMPLETED")
        return True
        
    except Exception as e:
        print(f"❌ Web interface test failed: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False


async def main():
    """Run all tests"""
    print("🚀 ENHANCED USER-CONTROLLED AUTOMATION SYSTEM TESTS")
    print("="*60)
    print("🎯 This test will verify that the system components work correctly")
    print("🔧 Testing API integration, data processing, and web interface")
    print("✨ Using exact same logic as test_real_api_data.py")
    print("="*60)
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: System initialization
    if await test_system_initialization():
        tests_passed += 1
    
    # Test 2: Web interface
    if await test_web_interface():
        tests_passed += 1
    
    # Summary
    print(f"\n📊 TEST SUMMARY")
    print("="*60)
    print(f"✅ Tests Passed: {tests_passed}/{total_tests}")
    print(f"📈 Success Rate: {(tests_passed/total_tests)*100:.1f}%")
    
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED! System is ready for production use.")
        print("\n🚦 NEXT STEPS:")
        print("1. Run: python run_user_controlled_automation.py")
        print("2. Wait for browser initialization")
        print("3. Open http://localhost:5000 in your browser")
        print("4. Select records and click 'Process Selected Records'")
        print("5. Monitor console for automation progress")
        print("✨ Uses exact same proven flow as test_real_api_data.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        
    print("="*60)


if __name__ == "__main__":
    asyncio.run(main()) 