2025-06-10 14:46:00,874 - __main__ - INFO - Configuration loaded successfully
2025-06-10 14:46:02,983 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-06-10 14:46:02,984 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 14:46:10,304 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:10] "GET / HTTP/1.1" 200 -
2025-06-10 14:46:10,923 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-10 14:46:10,928 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-10 14:46:11,445 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:11] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-10 14:46:14,973 - data_interface.app - ERROR - Error fetching staging data: HTTPConnectionPool(host='localhost', port=5173): Max retries exceeded with url: /api/staging/data (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C44E9A1940>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-10 14:46:14,974 - data_interface.app - ERROR - Error fetching staging data: HTTPConnectionPool(host='localhost', port=5173): Max retries exceeded with url: /api/staging/data?status=staged&limit=50 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C44E99B4D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-10 14:46:14,975 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:14] "[35m[1mGET /api/employees HTTP/1.1[0m" 500 -
2025-06-10 14:46:14,976 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:14] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-10 14:46:14,982 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-10 14:46:19,028 - data_interface.app - ERROR - Error fetching staging data: HTTPConnectionPool(host='localhost', port=5173): Max retries exceeded with url: /api/staging/data?status=staged&limit=50 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C44E99A350>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-10 14:46:19,030 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:19] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-10 14:46:23,582 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:23] "GET / HTTP/1.1" 200 -
2025-06-10 14:46:23,648 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-10 14:46:23,650 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-10 14:46:30,669 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 14:46:30] "GET / HTTP/1.1" 200 -
2025-06-10 14:46:31,493 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-10 14:46:31,796 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-10 14:46:38,688 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-10 14:46:38,689 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:38] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-10 14:46:39,480 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-10 14:46:39,482 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 14:46:39] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-10 14:46:39,648 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-10 14:46:39,648 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 14:46:39] "GET /api/employees HTTP/1.1" 200 -
2025-06-10 14:46:39,872 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-10 14:46:39,873 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:39] "GET /api/employees HTTP/1.1" 200 -
2025-06-10 14:46:50,102 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:50] "GET / HTTP/1.1" 200 -
2025-06-10 14:46:50,200 - data_interface.app - INFO - Using cached staging data
2025-06-10 14:46:50,201 - data_interface.app - INFO - Using cached staging data
2025-06-10 14:46:50,204 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:50] "GET /api/employees HTTP/1.1" 200 -
2025-06-10 14:46:50,206 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:50] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-10 14:46:55,599 - data_interface.app - INFO - Processing 1 selected records
2025-06-10 14:46:55,601 - automation_service - INFO - Starting automation job auto_20250610_144655
2025-06-10 14:46:55,601 - automation_service - INFO - Started automation job auto_20250610_144655 for 1 records
2025-06-10 14:46:55,602 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:55] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-10 14:46:55,608 - core.staging_automation - INFO - Initializing Staging Automation Engine
2025-06-10 14:46:55,609 - WDM - INFO - ====== WebDriver manager ======
2025-06-10 14:46:56,646 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-10 14:46:56,866 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-10 14:46:56,977 - WDM - INFO - There is no [win64] chromedriver "137.0.7151.68" for browser google-chrome "137.0.7151" in cache
2025-06-10 14:46:56,978 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-10 14:46:57,396 - WDM - INFO - WebDriver version 137.0.7151.68 selected
2025-06-10 14:46:57,399 - WDM - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.68/win32/chromedriver-win32.zip
2025-06-10 14:46:57,400 - WDM - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.68/win32/chromedriver-win32.zip
2025-06-10 14:46:57,612 - data_interface.app - INFO - Using cached staging data
2025-06-10 14:46:57,613 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:57] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-10 14:46:57,669 - WDM - INFO - Driver downloading response is 200
2025-06-10 14:47:00,027 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-10 14:47:01,076 - WDM - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.68]
2025-06-10 14:47:03,536 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-10 14:47:03,546 - core.staging_automation - INFO - Staging Automation Engine initialized successfully
2025-06-10 14:47:03,547 - automation_service - INFO - Created 1 mock staging records
2025-06-10 14:47:03,547 - automation_service - INFO - Processing 1 staging records
2025-06-10 14:47:23,650 - core.staging_automation - INFO - Performing login...
2025-06-10 14:47:23,650 - core.staging_automation - INFO - Navigating to login page: http://millwarep3:8004/
2025-06-10 14:47:53,675 - core.staging_automation - ERROR - Login failed: Message: timeout: Timed out receiving message from renderer: 29.155
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0xd03763+63299]
	GetHandleVerifier [0x0xd037a4+63364]
	(No symbol) [0x0xb31113]
	(No symbol) [0x0xb21beb]
	(No symbol) [0x0xb21921]
	(No symbol) [0x0xb1f8c4]
	(No symbol) [0x0xb2038d]
	(No symbol) [0x0xb2cb99]
	(No symbol) [0x0xb3e265]
	(No symbol) [0x0xb43c96]
	(No symbol) [0x0xb209cd]
	(No symbol) [0x0xb3dfc9]
	(No symbol) [0x0xbbfd65]
	(No symbol) [0x0xb9e376]
	(No symbol) [0x0xb6d6e0]
	(No symbol) [0x0xb6e544]
	GetHandleVerifier [0x0xf5e033+2531347]
	GetHandleVerifier [0x0xf59332+2511634]
	GetHandleVerifier [0x0xd29eda+220858]
	GetHandleVerifier [0x0xd1a528+156936]
	GetHandleVerifier [0x0xd20c5d+183357]
	GetHandleVerifier [0x0xd0b6c8+95912]
	GetHandleVerifier [0x0xd0b870+96336]
	GetHandleVerifier [0x0xcf664a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-10 14:47:53,678 - core.staging_automation - ERROR - Login check failed: Message: timeout: Timed out receiving message from renderer: 29.155
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0xd03763+63299]
	GetHandleVerifier [0x0xd037a4+63364]
	(No symbol) [0x0xb31113]
	(No symbol) [0x0xb21beb]
	(No symbol) [0x0xb21921]
	(No symbol) [0x0xb1f8c4]
	(No symbol) [0x0xb2038d]
	(No symbol) [0x0xb2cb99]
	(No symbol) [0x0xb3e265]
	(No symbol) [0x0xb43c96]
	(No symbol) [0x0xb209cd]
	(No symbol) [0x0xb3dfc9]
	(No symbol) [0x0xbbfd65]
	(No symbol) [0x0xb9e376]
	(No symbol) [0x0xb6d6e0]
	(No symbol) [0x0xb6e544]
	GetHandleVerifier [0x0xf5e033+2531347]
	GetHandleVerifier [0x0xf59332+2511634]
	GetHandleVerifier [0x0xd29eda+220858]
	GetHandleVerifier [0x0xd1a528+156936]
	GetHandleVerifier [0x0xd20c5d+183357]
	GetHandleVerifier [0x0xd0b6c8+95912]
	GetHandleVerifier [0x0xd0b870+96336]
	GetHandleVerifier [0x0xcf664a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-10 14:47:53,679 - core.staging_automation - INFO - Navigating to login page: http://millwarep3:8004/
2025-06-10 14:48:17,378 - core.staging_automation - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-10 14:48:17,379 - core.staging_automation - INFO - Login completed successfully
2025-06-10 14:48:17,379 - core.staging_automation - INFO - Processing record 1/1: Employee c1e595b4-d498-4320-bce3-8d0f0cf52060
2025-06-10 14:48:17,380 - core.staging_automation - INFO - Processing record: Employee c1e595b4-d498-4320-bce3-8d0f0cf52060 - 2025-06-10
2025-06-10 14:48:17,380 - core.staging_automation - INFO - Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-10 14:48:24,293 - core.staging_automation - ERROR - Failed to fill date field: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xd03763+63299]
	GetHandleVerifier [0x0xd037a4+63364]
	(No symbol) [0x0xb31113]
	(No symbol) [0x0xb42c01]
	(No symbol) [0x0xb41ce0]
	(No symbol) [0x0xb383d2]
	(No symbol) [0x0xb368d1]
	(No symbol) [0x0xb39c4a]
	(No symbol) [0x0xb39cc7]
	(No symbol) [0x0xb7a51a]
	(No symbol) [0x0xb7a5a1]
	(No symbol) [0x0xb71fd4]
	(No symbol) [0x0xb9e57c]
	(No symbol) [0x0xb6eed4]
	(No symbol) [0x0xb9e7f4]
	(No symbol) [0x0xbbfa4a]
	(No symbol) [0x0xb9e376]
	(No symbol) [0x0xb6d6e0]
	(No symbol) [0x0xb6e544]
	GetHandleVerifier [0x0xf5e033+2531347]
	GetHandleVerifier [0x0xf59332+2511634]
	GetHandleVerifier [0x0xd29eda+220858]
	GetHandleVerifier [0x0xd1a528+156936]
	GetHandleVerifier [0x0xd20c5d+183357]
	GetHandleVerifier [0x0xd0b6c8+95912]
	GetHandleVerifier [0x0xd0b870+96336]
	GetHandleVerifier [0x0xcf664a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-10 14:48:24,293 - core.staging_automation - ERROR - Failed to fill form for record c1e595b4-d498-4320-bce3-8d0f0cf52060: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xd03763+63299]
	GetHandleVerifier [0x0xd037a4+63364]
	(No symbol) [0x0xb31113]
	(No symbol) [0x0xb42c01]
	(No symbol) [0x0xb41ce0]
	(No symbol) [0x0xb383d2]
	(No symbol) [0x0xb368d1]
	(No symbol) [0x0xb39c4a]
	(No symbol) [0x0xb39cc7]
	(No symbol) [0x0xb7a51a]
	(No symbol) [0x0xb7a5a1]
	(No symbol) [0x0xb71fd4]
	(No symbol) [0x0xb9e57c]
	(No symbol) [0x0xb6eed4]
	(No symbol) [0x0xb9e7f4]
	(No symbol) [0x0xbbfa4a]
	(No symbol) [0x0xb9e376]
	(No symbol) [0x0xb6d6e0]
	(No symbol) [0x0xb6e544]
	GetHandleVerifier [0x0xf5e033+2531347]
	GetHandleVerifier [0x0xf59332+2511634]
	GetHandleVerifier [0x0xd29eda+220858]
	GetHandleVerifier [0x0xd1a528+156936]
	GetHandleVerifier [0x0xd20c5d+183357]
	GetHandleVerifier [0x0xd0b6c8+95912]
	GetHandleVerifier [0x0xd0b870+96336]
	GetHandleVerifier [0x0xcf664a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-10 14:48:24,294 - core.staging_automation - ERROR - Failed to process record c1e595b4-d498-4320-bce3-8d0f0cf52060: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xd03763+63299]
	GetHandleVerifier [0x0xd037a4+63364]
	(No symbol) [0x0xb31113]
	(No symbol) [0x0xb42c01]
	(No symbol) [0x0xb41ce0]
	(No symbol) [0x0xb383d2]
	(No symbol) [0x0xb368d1]
	(No symbol) [0x0xb39c4a]
	(No symbol) [0x0xb39cc7]
	(No symbol) [0x0xb7a51a]
	(No symbol) [0x0xb7a5a1]
	(No symbol) [0x0xb71fd4]
	(No symbol) [0x0xb9e57c]
	(No symbol) [0x0xb6eed4]
	(No symbol) [0x0xb9e7f4]
	(No symbol) [0x0xbbfa4a]
	(No symbol) [0x0xb9e376]
	(No symbol) [0x0xb6d6e0]
	(No symbol) [0x0xb6e544]
	GetHandleVerifier [0x0xf5e033+2531347]
	GetHandleVerifier [0x0xf59332+2511634]
	GetHandleVerifier [0x0xd29eda+220858]
	GetHandleVerifier [0x0xd1a528+156936]
	GetHandleVerifier [0x0xd20c5d+183357]
	GetHandleVerifier [0x0xd0b6c8+95912]
	GetHandleVerifier [0x0xd0b870+96336]
	GetHandleVerifier [0x0xcf664a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-10 14:48:24,294 - core.staging_automation - INFO - Completed processing 1 records
2025-06-10 14:48:24,295 - automation_service - INFO - Job auto_20250610_144655 completed: 0 successful, 1 failed
2025-06-10 14:48:26,574 - core.browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-10 14:48:26,575 - core.staging_automation - INFO - Staging automation engine cleaned up
2025-06-10 14:52:47,181 - werkzeug - INFO - ********** - - [10/Jun/2025 14:52:47] "GET / HTTP/1.1" 200 -
2025-06-10 14:52:47,365 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-10 14:52:47,366 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-10 14:52:52,462 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-10 14:52:52,477 - werkzeug - INFO - ********** - - [10/Jun/2025 14:52:52] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-10 14:52:52,633 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-10 14:52:52,635 - werkzeug - INFO - ********** - - [10/Jun/2025 14:52:52] "GET /api/employees HTTP/1.1" 200 -
2025-06-10 14:53:03,016 - data_interface.app - INFO - Processing 1 selected records
2025-06-10 14:53:03,017 - automation_service - INFO - Starting automation job auto_20250610_145303
2025-06-10 14:53:03,020 - core.staging_automation - INFO - Initializing Staging Automation Engine
2025-06-10 14:53:03,021 - WDM - INFO - ====== WebDriver manager ======
2025-06-10 14:53:03,018 - automation_service - INFO - Started automation job auto_20250610_145303 for 1 records
2025-06-10 14:53:03,027 - werkzeug - INFO - ********** - - [10/Jun/2025 14:53:03] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-10 14:53:04,024 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-10 14:53:04,313 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-10 14:53:04,476 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.68\chromedriver-win32/chromedriver.exe] found in cache
2025-06-10 14:53:05,055 - data_interface.app - INFO - Using cached staging data
2025-06-10 14:53:05,057 - werkzeug - INFO - ********** - - [10/Jun/2025 14:53:05] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-10 14:53:05,996 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-10 14:53:06,007 - core.staging_automation - INFO - Staging Automation Engine initialized successfully
2025-06-10 14:53:06,008 - automation_service - INFO - Created 1 mock staging records
2025-06-10 14:53:06,008 - automation_service - INFO - Processing 1 staging records
2025-06-10 14:53:26,113 - core.staging_automation - INFO - Performing login...
2025-06-10 14:53:26,114 - core.staging_automation - INFO - Navigating to login page: http://millwarep3:8004/
2025-06-10 14:53:34,324 - core.staging_automation - ERROR - Login failed: Message: unknown error: net::ERR_NAME_NOT_RESOLVED
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0xd03763+63299]
	GetHandleVerifier [0x0xd037a4+63364]
	(No symbol) [0x0xb31113]
	(No symbol) [0x0xb2e64e]
	(No symbol) [0x0xb21e42]
	(No symbol) [0x0xb23878]
	(No symbol) [0x0xb220d8]
	(No symbol) [0x0xb21c13]
	(No symbol) [0x0xb21921]
	(No symbol) [0x0xb1f8c4]
	(No symbol) [0x0xb2026b]
	(No symbol) [0x0xb34b3e]
	(No symbol) [0x0xbc0657]
	(No symbol) [0x0xb9e57c]
	(No symbol) [0x0xbbfa4a]
	(No symbol) [0x0xb9e376]
	(No symbol) [0x0xb6d6e0]
	(No symbol) [0x0xb6e544]
	GetHandleVerifier [0x0xf5e033+2531347]
	GetHandleVerifier [0x0xf59332+2511634]
	GetHandleVerifier [0x0xd29eda+220858]
	GetHandleVerifier [0x0xd1a528+156936]
	GetHandleVerifier [0x0xd20c5d+183357]
	GetHandleVerifier [0x0xd0b6c8+95912]
	GetHandleVerifier [0x0xd0b870+96336]
	GetHandleVerifier [0x0xcf664a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-10 14:53:34,326 - core.staging_automation - ERROR - Login check failed: Message: unknown error: net::ERR_NAME_NOT_RESOLVED
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0xd03763+63299]
	GetHandleVerifier [0x0xd037a4+63364]
	(No symbol) [0x0xb31113]
	(No symbol) [0x0xb2e64e]
	(No symbol) [0x0xb21e42]
	(No symbol) [0x0xb23878]
	(No symbol) [0x0xb220d8]
	(No symbol) [0x0xb21c13]
	(No symbol) [0x0xb21921]
	(No symbol) [0x0xb1f8c4]
	(No symbol) [0x0xb2026b]
	(No symbol) [0x0xb34b3e]
	(No symbol) [0x0xbc0657]
	(No symbol) [0x0xb9e57c]
	(No symbol) [0x0xbbfa4a]
	(No symbol) [0x0xb9e376]
	(No symbol) [0x0xb6d6e0]
	(No symbol) [0x0xb6e544]
	GetHandleVerifier [0x0xf5e033+2531347]
	GetHandleVerifier [0x0xf59332+2511634]
	GetHandleVerifier [0x0xd29eda+220858]
	GetHandleVerifier [0x0xd1a528+156936]
	GetHandleVerifier [0x0xd20c5d+183357]
	GetHandleVerifier [0x0xd0b6c8+95912]
	GetHandleVerifier [0x0xd0b870+96336]
	GetHandleVerifier [0x0xcf664a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-10 14:53:34,327 - core.staging_automation - INFO - Navigating to login page: http://millwarep3:8004/
2025-06-10 14:53:42,582 - core.staging_automation - ERROR - Login failed: Message: unknown error: net::ERR_NAME_NOT_RESOLVED
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0xd03763+63299]
	GetHandleVerifier [0x0xd037a4+63364]
	(No symbol) [0x0xb31113]
	(No symbol) [0x0xb2e64e]
	(No symbol) [0x0xb21e42]
	(No symbol) [0x0xb23878]
	(No symbol) [0x0xb220d8]
	(No symbol) [0x0xb21c13]
	(No symbol) [0x0xb21921]
	(No symbol) [0x0xb1f8c4]
	(No symbol) [0x0xb2026b]
	(No symbol) [0x0xb34b3e]
	(No symbol) [0x0xbc0657]
	(No symbol) [0x0xb9e57c]
	(No symbol) [0x0xbbfa4a]
	(No symbol) [0x0xb9e376]
	(No symbol) [0x0xb6d6e0]
	(No symbol) [0x0xb6e544]
	GetHandleVerifier [0x0xf5e033+2531347]
	GetHandleVerifier [0x0xf59332+2511634]
	GetHandleVerifier [0x0xd29eda+220858]
	GetHandleVerifier [0x0xd1a528+156936]
	GetHandleVerifier [0x0xd20c5d+183357]
	GetHandleVerifier [0x0xd0b6c8+95912]
	GetHandleVerifier [0x0xd0b870+96336]
	GetHandleVerifier [0x0xcf664a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-10 14:53:42,584 - core.staging_automation - ERROR - Error processing staging records: Message: unknown error: net::ERR_NAME_NOT_RESOLVED
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0xd03763+63299]
	GetHandleVerifier [0x0xd037a4+63364]
	(No symbol) [0x0xb31113]
	(No symbol) [0x0xb2e64e]
	(No symbol) [0x0xb21e42]
	(No symbol) [0x0xb23878]
	(No symbol) [0x0xb220d8]
	(No symbol) [0x0xb21c13]
	(No symbol) [0x0xb21921]
	(No symbol) [0x0xb1f8c4]
	(No symbol) [0x0xb2026b]
	(No symbol) [0x0xb34b3e]
	(No symbol) [0x0xbc0657]
	(No symbol) [0x0xb9e57c]
	(No symbol) [0x0xbbfa4a]
	(No symbol) [0x0xb9e376]
	(No symbol) [0x0xb6d6e0]
	(No symbol) [0x0xb6e544]
	GetHandleVerifier [0x0xf5e033+2531347]
	GetHandleVerifier [0x0xf59332+2511634]
	GetHandleVerifier [0x0xd29eda+220858]
	GetHandleVerifier [0x0xd1a528+156936]
	GetHandleVerifier [0x0xd20c5d+183357]
	GetHandleVerifier [0x0xd0b6c8+95912]
	GetHandleVerifier [0x0xd0b870+96336]
	GetHandleVerifier [0x0xcf664a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-10 14:53:42,585 - automation_service - INFO - Job auto_20250610_145303 completed: 0 successful, 1 failed
2025-06-10 14:53:44,852 - core.browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-10 14:53:44,853 - core.staging_automation - INFO - Staging automation engine cleaned up
2025-06-10 15:57:15,102 - __main__ - INFO - System shutdown complete
2025-06-11 07:38:43,878 - __main__ - INFO - Configuration loaded successfully
2025-06-11 07:38:45,903 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-06-11 07:38:45,903 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 07:39:15,532 - werkzeug - INFO - ********** - - [11/Jun/2025 07:39:15] "GET / HTTP/1.1" 200 -
2025-06-11 07:39:16,041 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 07:39:16,043 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 07:39:16,666 - werkzeug - INFO - ********** - - [11/Jun/2025 07:39:16] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-11 07:39:21,078 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 07:39:21,080 - werkzeug - INFO - ********** - - [11/Jun/2025 07:39:21] "GET /api/employees HTTP/1.1" 200 -
2025-06-11 07:39:21,346 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 07:39:21,347 - werkzeug - INFO - ********** - - [11/Jun/2025 07:39:21] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-11 07:39:25,862 - data_interface.app - INFO - Processing 1 selected records
2025-06-11 07:39:25,863 - automation_service - INFO - Starting automation job auto_20250611_073925
2025-06-11 07:39:25,863 - automation_service - INFO - Started automation job auto_20250611_073925 for 1 records
2025-06-11 07:39:25,865 - werkzeug - INFO - ********** - - [11/Jun/2025 07:39:25] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-11 07:39:25,867 - core.staging_automation - INFO - Initializing Staging Automation Engine
2025-06-11 07:39:25,868 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 07:39:27,498 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 07:39:27,844 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 07:39:28,009 - WDM - INFO - There is no [win64] chromedriver "137.0.7151.70" for browser google-chrome "137.0.7151" in cache
2025-06-11 07:39:28,010 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 07:39:28,199 - data_interface.app - INFO - Using cached staging data
2025-06-11 07:39:28,201 - werkzeug - INFO - ********** - - [11/Jun/2025 07:39:28] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-11 07:39:28,486 - WDM - INFO - WebDriver version 137.0.7151.70 selected
2025-06-11 07:39:28,491 - WDM - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.70/win32/chromedriver-win32.zip
2025-06-11 07:39:28,492 - WDM - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.70/win32/chromedriver-win32.zip
2025-06-11 07:39:28,710 - WDM - INFO - Driver downloading response is 200
2025-06-11 07:39:37,094 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 07:39:38,096 - WDM - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70]
2025-06-11 07:39:40,292 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-11 07:39:40,297 - core.staging_automation - INFO - Staging Automation Engine initialized successfully
2025-06-11 07:39:40,297 - automation_service - INFO - Created 1 mock staging records
2025-06-11 07:39:40,298 - automation_service - INFO - Processing 1 staging records
2025-06-11 07:40:00,368 - core.staging_automation - INFO - Performing login...
2025-06-11 07:40:00,368 - core.staging_automation - INFO - Navigating to login page: http://millwarep3:8004/
2025-06-11 07:40:18,432 - core.staging_automation - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 07:40:18,432 - core.staging_automation - INFO - Login completed successfully
2025-06-11 07:40:18,433 - core.staging_automation - INFO - Processing record 1/1: Employee c1e595b4-d498-4320-bce3-8d0f0cf52060
2025-06-11 07:40:18,433 - core.staging_automation - INFO - Processing record: Employee c1e595b4-d498-4320-bce3-8d0f0cf52060 - 2025-06-10
2025-06-11 07:40:18,442 - core.staging_automation - INFO - Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 07:40:40,427 - core.staging_automation - ERROR - Failed to fill date field: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xd73783+63299]
	GetHandleVerifier [0x0xd737c4+63364]
	(No symbol) [0x0xba1113]
	(No symbol) [0x0xbb2c01]
	(No symbol) [0x0xbb1ce0]
	(No symbol) [0x0xba83d2]
	(No symbol) [0x0xba68d1]
	(No symbol) [0x0xba9c4a]
	(No symbol) [0x0xba9cc7]
	(No symbol) [0x0xbea51a]
	(No symbol) [0x0xbea5a1]
	(No symbol) [0x0xbe1fd4]
	(No symbol) [0x0xc0e57c]
	(No symbol) [0x0xbdeed4]
	(No symbol) [0x0xc0e7f4]
	(No symbol) [0x0xc2fa4a]
	(No symbol) [0x0xc0e376]
	(No symbol) [0x0xbdd6e0]
	(No symbol) [0x0xbde544]
	GetHandleVerifier [0x0xfce073+2531379]
	GetHandleVerifier [0x0xfc9372+2511666]
	GetHandleVerifier [0x0xd99efa+220858]
	GetHandleVerifier [0x0xd8a548+156936]
	GetHandleVerifier [0x0xd90c7d+183357]
	GetHandleVerifier [0x0xd7b6e8+95912]
	GetHandleVerifier [0x0xd7b890+96336]
	GetHandleVerifier [0x0xd6666a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-11 07:40:40,428 - core.staging_automation - ERROR - Failed to fill form for record c1e595b4-d498-4320-bce3-8d0f0cf52060: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xd73783+63299]
	GetHandleVerifier [0x0xd737c4+63364]
	(No symbol) [0x0xba1113]
	(No symbol) [0x0xbb2c01]
	(No symbol) [0x0xbb1ce0]
	(No symbol) [0x0xba83d2]
	(No symbol) [0x0xba68d1]
	(No symbol) [0x0xba9c4a]
	(No symbol) [0x0xba9cc7]
	(No symbol) [0x0xbea51a]
	(No symbol) [0x0xbea5a1]
	(No symbol) [0x0xbe1fd4]
	(No symbol) [0x0xc0e57c]
	(No symbol) [0x0xbdeed4]
	(No symbol) [0x0xc0e7f4]
	(No symbol) [0x0xc2fa4a]
	(No symbol) [0x0xc0e376]
	(No symbol) [0x0xbdd6e0]
	(No symbol) [0x0xbde544]
	GetHandleVerifier [0x0xfce073+2531379]
	GetHandleVerifier [0x0xfc9372+2511666]
	GetHandleVerifier [0x0xd99efa+220858]
	GetHandleVerifier [0x0xd8a548+156936]
	GetHandleVerifier [0x0xd90c7d+183357]
	GetHandleVerifier [0x0xd7b6e8+95912]
	GetHandleVerifier [0x0xd7b890+96336]
	GetHandleVerifier [0x0xd6666a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-11 07:40:40,429 - core.staging_automation - ERROR - Failed to process record c1e595b4-d498-4320-bce3-8d0f0cf52060: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xd73783+63299]
	GetHandleVerifier [0x0xd737c4+63364]
	(No symbol) [0x0xba1113]
	(No symbol) [0x0xbb2c01]
	(No symbol) [0x0xbb1ce0]
	(No symbol) [0x0xba83d2]
	(No symbol) [0x0xba68d1]
	(No symbol) [0x0xba9c4a]
	(No symbol) [0x0xba9cc7]
	(No symbol) [0x0xbea51a]
	(No symbol) [0x0xbea5a1]
	(No symbol) [0x0xbe1fd4]
	(No symbol) [0x0xc0e57c]
	(No symbol) [0x0xbdeed4]
	(No symbol) [0x0xc0e7f4]
	(No symbol) [0x0xc2fa4a]
	(No symbol) [0x0xc0e376]
	(No symbol) [0x0xbdd6e0]
	(No symbol) [0x0xbde544]
	GetHandleVerifier [0x0xfce073+2531379]
	GetHandleVerifier [0x0xfc9372+2511666]
	GetHandleVerifier [0x0xd99efa+220858]
	GetHandleVerifier [0x0xd8a548+156936]
	GetHandleVerifier [0x0xd90c7d+183357]
	GetHandleVerifier [0x0xd7b6e8+95912]
	GetHandleVerifier [0x0xd7b890+96336]
	GetHandleVerifier [0x0xd6666a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-11 07:40:40,429 - core.staging_automation - INFO - Completed processing 1 records
2025-06-11 07:40:40,429 - automation_service - INFO - Job auto_20250611_073925 completed: 0 successful, 1 failed
2025-06-11 07:40:42,602 - core.browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-11 07:40:42,603 - core.staging_automation - INFO - Staging automation engine cleaned up
2025-06-11 07:42:52,278 - __main__ - INFO - System shutdown complete
2025-06-11 07:52:22,817 - __main__ - INFO - Configuration loaded successfully
2025-06-11 07:52:23,084 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-11 07:52:23,094 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 07:52:23,095 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 07:52:23,095 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 07:52:23,095 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 07:52:24,459 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 07:52:24,789 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 07:52:24,849 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-06-11 07:52:24,850 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 07:52:25,205 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/chromedriver.exe] found in cache
2025-06-11 07:52:29,999 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-11 07:52:30,002 - core.persistent_browser_manager - INFO - Navigating to login page: http://millwarep3:8004/
2025-06-11 07:52:39,082 - werkzeug - INFO - ********** - - [11/Jun/2025 07:52:39] "GET / HTTP/1.1" 200 -
2025-06-11 07:52:40,038 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 07:52:40,047 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 07:52:46,030 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 07:52:46,033 - werkzeug - INFO - ********** - - [11/Jun/2025 07:52:46] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-11 07:52:46,961 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 07:52:46,963 - werkzeug - INFO - ********** - - [11/Jun/2025 07:52:46] "GET /api/employees HTTP/1.1" 200 -
2025-06-11 07:52:55,635 - data_interface.app - INFO - Processing 1 selected records
2025-06-11 07:52:55,636 - automation_service - WARNING - ⚠️ Automation engine not yet ready, will wait for initialization...
2025-06-11 07:53:01,295 - core.persistent_browser_manager - INFO - WebDriver created and warmed up successfully
2025-06-11 07:53:01,295 - core.persistent_browser_manager - INFO - Performing initial login...
2025-06-11 07:53:25,650 - automation_service - ERROR - Failed to start automation job: Automation engine initialization timeout
2025-06-11 07:53:25,652 - data_interface.app - ERROR - Error processing selected records: Automation engine initialization timeout
2025-06-11 07:53:25,654 - werkzeug - INFO - ********** - - [11/Jun/2025 07:53:25] "[35m[1mPOST /api/process-selected HTTP/1.1[0m" 500 -
2025-06-11 07:53:31,650 - core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 07:53:42,770 - core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-11 07:53:42,773 - core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-11 07:53:42,774 - core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-11 07:53:42,794 - core.enhanced_staging_automation - INFO - ✅ Enhanced Staging Automation Engine initialized successfully
2025-06-11 07:53:42,796 - automation_service - ERROR - ❌ Failed to pre-initialize automation engine
2025-06-11 08:01:38,879 - automation_service - INFO - Cleaning up automation service...
2025-06-11 08:01:38,880 - core.enhanced_staging_automation - INFO - Cleaning up Enhanced Staging Automation Engine
2025-06-11 08:01:38,881 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 08:01:42,979 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001FFAACC5550>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/35cf19de05334c410a10ceffe55aa67e
2025-06-11 08:01:47,088 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001FFAABC6D70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/35cf19de05334c410a10ceffe55aa67e
2025-06-11 08:01:51,169 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001FFAABC68B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/35cf19de05334c410a10ceffe55aa67e
2025-06-11 08:03:48,985 - __main__ - INFO - Configuration loaded successfully
2025-06-11 08:03:49,330 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-11 08:03:49,332 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 08:03:49,333 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 08:03:49,333 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 08:03:49,333 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 08:03:51,523 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-06-11 08:03:51,524 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 08:03:53,174 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 08:03:53,636 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 08:03:54,199 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/chromedriver.exe] found in cache
2025-06-11 08:04:02,324 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-11 08:04:02,325 - core.persistent_browser_manager - INFO - Navigating to login page: http://millwarep3:8004/
2025-06-11 08:04:07,877 - core.persistent_browser_manager - ERROR - Failed to create WebDriver: Message: unknown error: net::ERR_NAME_NOT_RESOLVED
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0x1073783+63299]
	GetHandleVerifier [0x0x10737c4+63364]
	(No symbol) [0x0xea1113]
	(No symbol) [0x0xe9e64e]
	(No symbol) [0x0xe91e42]
	(No symbol) [0x0xe93878]
	(No symbol) [0x0xe920d8]
	(No symbol) [0x0xe91c13]
	(No symbol) [0x0xe91921]
	(No symbol) [0x0xe8f8c4]
	(No symbol) [0x0xe9026b]
	(No symbol) [0x0xea4b3e]
	(No symbol) [0x0xf30657]
	(No symbol) [0x0xf0e57c]
	(No symbol) [0x0xf2fa4a]
	(No symbol) [0x0xf0e376]
	(No symbol) [0x0xedd6e0]
	(No symbol) [0x0xede544]
	GetHandleVerifier [0x0x12ce073+2531379]
	GetHandleVerifier [0x0x12c9372+2511666]
	GetHandleVerifier [0x0x1099efa+220858]
	GetHandleVerifier [0x0x108a548+156936]
	GetHandleVerifier [0x0x1090c7d+183357]
	GetHandleVerifier [0x0x107b6e8+95912]
	GetHandleVerifier [0x0x107b890+96336]
	GetHandleVerifier [0x0x106666a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-11 08:04:07,878 - core.persistent_browser_manager - ERROR - ❌ Failed to initialize persistent browser session: Message: unknown error: net::ERR_NAME_NOT_RESOLVED
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0x1073783+63299]
	GetHandleVerifier [0x0x10737c4+63364]
	(No symbol) [0x0xea1113]
	(No symbol) [0x0xe9e64e]
	(No symbol) [0x0xe91e42]
	(No symbol) [0x0xe93878]
	(No symbol) [0x0xe920d8]
	(No symbol) [0x0xe91c13]
	(No symbol) [0x0xe91921]
	(No symbol) [0x0xe8f8c4]
	(No symbol) [0x0xe9026b]
	(No symbol) [0x0xea4b3e]
	(No symbol) [0x0xf30657]
	(No symbol) [0x0xf0e57c]
	(No symbol) [0x0xf2fa4a]
	(No symbol) [0x0xf0e376]
	(No symbol) [0x0xedd6e0]
	(No symbol) [0x0xede544]
	GetHandleVerifier [0x0x12ce073+2531379]
	GetHandleVerifier [0x0x12c9372+2511666]
	GetHandleVerifier [0x0x1099efa+220858]
	GetHandleVerifier [0x0x108a548+156936]
	GetHandleVerifier [0x0x1090c7d+183357]
	GetHandleVerifier [0x0x107b6e8+95912]
	GetHandleVerifier [0x0x107b890+96336]
	GetHandleVerifier [0x0x106666a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-11 08:04:07,879 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 08:04:10,076 - core.persistent_browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-11 08:04:10,077 - core.enhanced_staging_automation - ERROR - ❌ Failed to initialize automation engine: Failed to initialize persistent browser manager
2025-06-11 08:04:10,077 - automation_service - ERROR - ❌ Pre-initialization failed: Failed to initialize persistent browser manager
2025-06-11 08:04:21,895 - automation_service - INFO - Cleaning up automation service...
2025-06-11 08:04:21,895 - core.enhanced_staging_automation - INFO - Cleaning up Enhanced Staging Automation Engine
2025-06-11 08:04:21,896 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 08:04:21,896 - __main__ - INFO - System shutdown complete
2025-06-11 08:04:25,163 - __main__ - INFO - Configuration loaded successfully
2025-06-11 08:04:25,318 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-11 08:04:25,319 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 08:04:25,319 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 08:04:25,320 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 08:04:25,320 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 08:04:26,120 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 08:04:26,392 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 08:04:26,746 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/chromedriver.exe] found in cache
2025-06-11 08:04:27,174 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-06-11 08:04:27,174 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 08:04:28,184 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-11 08:04:28,184 - core.persistent_browser_manager - INFO - Navigating to login page: http://millwarep3:8004/
2025-06-11 08:04:38,839 - werkzeug - INFO - ********** - - [11/Jun/2025 08:04:38] "GET / HTTP/1.1" 200 -
2025-06-11 08:04:39,145 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 08:04:39,145 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 08:04:44,898 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 08:04:44,900 - werkzeug - INFO - ********** - - [11/Jun/2025 08:04:44] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-11 08:04:50,060 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 08:04:50,060 - werkzeug - INFO - ********** - - [11/Jun/2025 08:04:50] "GET /api/employees HTTP/1.1" 200 -
2025-06-11 08:04:50,350 - data_interface.app - INFO - Processing 1 selected records
2025-06-11 08:04:50,351 - automation_service - WARNING - ⚠️ Automation engine not yet ready, will wait for initialization...
2025-06-11 08:04:56,874 - core.persistent_browser_manager - INFO - WebDriver created and warmed up successfully
2025-06-11 08:04:56,875 - core.persistent_browser_manager - INFO - Performing initial login...
2025-06-11 08:05:13,760 - core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 08:05:19,804 - core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 08:05:19,805 - core.persistent_browser_manager - INFO - 🎯 Detected location setting page - handling redirect...
2025-06-11 08:05:20,365 - automation_service - ERROR - Failed to start automation job: Automation engine initialization timeout
2025-06-11 08:05:20,365 - data_interface.app - ERROR - Error processing selected records: Automation engine initialization timeout
2025-06-11 08:05:20,366 - werkzeug - INFO - ********** - - [11/Jun/2025 08:05:20] "[35m[1mPOST /api/process-selected HTTP/1.1[0m" 500 -
2025-06-11 08:05:22,819 - core.persistent_browser_manager - INFO - 🔄 Trying location page strategy 1/4
2025-06-11 08:06:25,191 - core.persistent_browser_manager - WARNING - Strategy 1 failed: Click OK strategy failed: No clickable buttons found
2025-06-11 08:06:25,191 - core.persistent_browser_manager - INFO - 🔄 Trying location page strategy 2/4
2025-06-11 08:07:15,378 - core.persistent_browser_manager - WARNING - Strategy 2 failed: Skip strategy failed: No skip options found
2025-06-11 08:07:15,379 - core.persistent_browser_manager - INFO - 🔄 Trying location page strategy 3/4
2025-06-11 08:07:15,380 - core.persistent_browser_manager - INFO - Attempting direct navigation to: http://millwarep3:8004/en/main.aspx
2025-06-11 08:07:25,032 - core.persistent_browser_manager - WARNING - Strategy 3 failed: Navigate away strategy failed: Navigation didn't work
2025-06-11 08:07:25,033 - core.persistent_browser_manager - INFO - 🔄 Trying location page strategy 4/4
2025-06-11 08:07:25,033 - core.persistent_browser_manager - INFO - Refreshing page to bypass location setting...
2025-06-11 08:07:39,441 - core.persistent_browser_manager - WARNING - ⚠️ All location page strategies failed, continuing anyway...
2025-06-11 08:07:39,444 - core.persistent_browser_manager - INFO - Verification attempt 1: Current URL = http://millwarep3:8004/en/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 08:07:41,455 - core.persistent_browser_manager - INFO - Verification attempt 2: Current URL = http://millwarep3:8004/en/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 08:07:43,482 - core.persistent_browser_manager - INFO - Verification attempt 3: Current URL = http://millwarep3:8004/en/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 08:07:45,488 - core.persistent_browser_manager - WARNING - ⚠️ Still on location setting page after all attempts
2025-06-11 08:07:55,509 - core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-11 08:07:55,510 - core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-11 08:07:55,510 - core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-11 08:07:55,513 - core.enhanced_staging_automation - INFO - ✅ Enhanced Staging Automation Engine initialized successfully
2025-06-11 08:07:55,513 - automation_service - ERROR - ❌ Failed to pre-initialize automation engine
2025-06-11 08:09:51,133 - automation_service - INFO - Cleaning up automation service...
2025-06-11 08:09:51,133 - core.enhanced_staging_automation - INFO - Cleaning up Enhanced Staging Automation Engine
2025-06-11 08:09:51,134 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 08:18:48,779 - __main__ - INFO - Configuration loaded successfully
2025-06-11 08:18:48,942 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-11 08:18:48,944 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 08:18:48,944 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 08:18:48,945 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 08:18:48,945 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 08:18:49,758 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 08:18:49,931 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 08:18:50,553 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/chromedriver.exe] found in cache
2025-06-11 08:18:50,789 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-06-11 08:18:50,790 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 08:18:52,008 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-11 08:18:52,009 - core.persistent_browser_manager - INFO - Navigating to login page: http://millwarep3:8004/
2025-06-11 08:18:56,268 - werkzeug - INFO - ********** - - [11/Jun/2025 08:18:56] "GET / HTTP/1.1" 200 -
2025-06-11 08:18:56,566 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 08:18:56,567 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 08:19:04,474 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 08:19:04,474 - werkzeug - INFO - ********** - - [11/Jun/2025 08:19:04] "GET /api/employees HTTP/1.1" 200 -
2025-06-11 08:19:04,510 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 08:19:04,511 - werkzeug - INFO - ********** - - [11/Jun/2025 08:19:04] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-11 08:19:22,024 - core.persistent_browser_manager - ERROR - Failed to create WebDriver: Message: timeout: Timed out receiving message from renderer: 26.016
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0x1073783+63299]
	GetHandleVerifier [0x0x10737c4+63364]
	(No symbol) [0x0xea1113]
	(No symbol) [0x0xe91beb]
	(No symbol) [0x0xe91921]
	(No symbol) [0x0xe8f8c4]
	(No symbol) [0x0xe9038d]
	(No symbol) [0x0xe9cb99]
	(No symbol) [0x0xeae265]
	(No symbol) [0x0xeb3c96]
	(No symbol) [0x0xe909cd]
	(No symbol) [0x0xeadfc9]
	(No symbol) [0x0xf2fd65]
	(No symbol) [0x0xf0e376]
	(No symbol) [0x0xedd6e0]
	(No symbol) [0x0xede544]
	GetHandleVerifier [0x0x12ce073+2531379]
	GetHandleVerifier [0x0x12c9372+2511666]
	GetHandleVerifier [0x0x1099efa+220858]
	GetHandleVerifier [0x0x108a548+156936]
	GetHandleVerifier [0x0x1090c7d+183357]
	GetHandleVerifier [0x0x107b6e8+95912]
	GetHandleVerifier [0x0x107b890+96336]
	GetHandleVerifier [0x0x106666a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-11 08:19:22,024 - core.persistent_browser_manager - ERROR - ❌ Failed to initialize persistent browser session: Message: timeout: Timed out receiving message from renderer: 26.016
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0x1073783+63299]
	GetHandleVerifier [0x0x10737c4+63364]
	(No symbol) [0x0xea1113]
	(No symbol) [0x0xe91beb]
	(No symbol) [0x0xe91921]
	(No symbol) [0x0xe8f8c4]
	(No symbol) [0x0xe9038d]
	(No symbol) [0x0xe9cb99]
	(No symbol) [0x0xeae265]
	(No symbol) [0x0xeb3c96]
	(No symbol) [0x0xe909cd]
	(No symbol) [0x0xeadfc9]
	(No symbol) [0x0xf2fd65]
	(No symbol) [0x0xf0e376]
	(No symbol) [0x0xedd6e0]
	(No symbol) [0x0xede544]
	GetHandleVerifier [0x0x12ce073+2531379]
	GetHandleVerifier [0x0x12c9372+2511666]
	GetHandleVerifier [0x0x1099efa+220858]
	GetHandleVerifier [0x0x108a548+156936]
	GetHandleVerifier [0x0x1090c7d+183357]
	GetHandleVerifier [0x0x107b6e8+95912]
	GetHandleVerifier [0x0x107b890+96336]
	GetHandleVerifier [0x0x106666a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-11 08:19:22,025 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 08:19:24,249 - core.persistent_browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-11 08:19:24,250 - core.enhanced_staging_automation - ERROR - ❌ Failed to initialize automation engine: Failed to initialize persistent browser manager
2025-06-11 08:19:24,250 - automation_service - ERROR - ❌ Pre-initialization failed: Failed to initialize persistent browser manager
2025-06-11 08:19:34,639 - automation_service - INFO - Cleaning up automation service...
2025-06-11 08:19:34,639 - core.enhanced_staging_automation - INFO - Cleaning up Enhanced Staging Automation Engine
2025-06-11 08:19:34,640 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 08:19:34,640 - __main__ - INFO - System shutdown complete
2025-06-11 08:19:42,652 - __main__ - INFO - Configuration loaded successfully
2025-06-11 08:19:42,800 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-11 08:19:42,801 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 08:19:42,802 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 08:19:42,802 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 08:19:42,802 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 08:19:43,583 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 08:19:44,210 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 08:19:44,537 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/chromedriver.exe] found in cache
2025-06-11 08:19:44,662 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-06-11 08:19:44,663 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 08:19:45,922 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-11 08:19:45,923 - core.persistent_browser_manager - INFO - Navigating to login page: http://millwarep3:8004/
2025-06-11 08:20:15,937 - core.persistent_browser_manager - ERROR - Failed to create WebDriver: Message: timeout: Timed out receiving message from renderer: 27.795
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0x1073783+63299]
	GetHandleVerifier [0x0x10737c4+63364]
	(No symbol) [0x0xea1113]
	(No symbol) [0x0xe91beb]
	(No symbol) [0x0xe91921]
	(No symbol) [0x0xe8f8c4]
	(No symbol) [0x0xe9038d]
	(No symbol) [0x0xe9cb99]
	(No symbol) [0x0xeae265]
	(No symbol) [0x0xeb3c96]
	(No symbol) [0x0xe909cd]
	(No symbol) [0x0xeadfc9]
	(No symbol) [0x0xf2fd65]
	(No symbol) [0x0xf0e376]
	(No symbol) [0x0xedd6e0]
	(No symbol) [0x0xede544]
	GetHandleVerifier [0x0x12ce073+2531379]
	GetHandleVerifier [0x0x12c9372+2511666]
	GetHandleVerifier [0x0x1099efa+220858]
	GetHandleVerifier [0x0x108a548+156936]
	GetHandleVerifier [0x0x1090c7d+183357]
	GetHandleVerifier [0x0x107b6e8+95912]
	GetHandleVerifier [0x0x107b890+96336]
	GetHandleVerifier [0x0x106666a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-11 08:20:15,938 - core.persistent_browser_manager - ERROR - ❌ Failed to initialize persistent browser session: Message: timeout: Timed out receiving message from renderer: 27.795
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0x1073783+63299]
	GetHandleVerifier [0x0x10737c4+63364]
	(No symbol) [0x0xea1113]
	(No symbol) [0x0xe91beb]
	(No symbol) [0x0xe91921]
	(No symbol) [0x0xe8f8c4]
	(No symbol) [0x0xe9038d]
	(No symbol) [0x0xe9cb99]
	(No symbol) [0x0xeae265]
	(No symbol) [0x0xeb3c96]
	(No symbol) [0x0xe909cd]
	(No symbol) [0x0xeadfc9]
	(No symbol) [0x0xf2fd65]
	(No symbol) [0x0xf0e376]
	(No symbol) [0x0xedd6e0]
	(No symbol) [0x0xede544]
	GetHandleVerifier [0x0x12ce073+2531379]
	GetHandleVerifier [0x0x12c9372+2511666]
	GetHandleVerifier [0x0x1099efa+220858]
	GetHandleVerifier [0x0x108a548+156936]
	GetHandleVerifier [0x0x1090c7d+183357]
	GetHandleVerifier [0x0x107b6e8+95912]
	GetHandleVerifier [0x0x107b890+96336]
	GetHandleVerifier [0x0x106666a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-11 08:20:15,938 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 08:20:18,122 - core.persistent_browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-11 08:20:18,122 - core.enhanced_staging_automation - ERROR - ❌ Failed to initialize automation engine: Failed to initialize persistent browser manager
2025-06-11 08:20:18,123 - automation_service - ERROR - ❌ Pre-initialization failed: Failed to initialize persistent browser manager
2025-06-11 08:59:08,851 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 08:59:08] "GET / HTTP/1.1" 200 -
2025-06-11 08:59:09,624 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 08:59:09,935 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 08:59:16,588 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 08:59:16,590 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 08:59:16] "GET /api/employees HTTP/1.1" 200 -
2025-06-11 08:59:17,147 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 08:59:17,149 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 08:59:17] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-11 10:47:35,442 - __main__ - INFO - Configuration loaded successfully
2025-06-11 10:47:36,427 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-11 10:47:36,432 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 10:47:36,432 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 10:47:36,432 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 10:47:36,432 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 10:47:37,494 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-11 10:47:37,495 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 10:47:38,168 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 10:47:38,402 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 10:47:38,756 - WDM - INFO - There is no [win64] chromedriver "137.0.7151.70" for browser google-chrome "137.0.7151" in cache
2025-06-11 10:47:38,756 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 10:47:39,297 - WDM - INFO - WebDriver version 137.0.7151.70 selected
2025-06-11 10:47:39,302 - WDM - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.70/win32/chromedriver-win32.zip
2025-06-11 10:47:39,303 - WDM - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.70/win32/chromedriver-win32.zip
2025-06-11 10:47:39,420 - WDM - INFO - Driver downloading response is 200
2025-06-11 10:47:44,981 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 10:47:46,383 - WDM - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70]
2025-06-11 10:47:46,429 - core.browser_manager - ERROR - Failed to create Chrome WebDriver: [WinError 193] %1 is not a valid Win32 application
2025-06-11 10:47:46,430 - core.persistent_browser_manager - ERROR - Failed to create WebDriver: [WinError 193] %1 is not a valid Win32 application
2025-06-11 10:47:46,430 - core.persistent_browser_manager - ERROR - ❌ Failed to initialize persistent browser session: [WinError 193] %1 is not a valid Win32 application
2025-06-11 10:47:46,430 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 10:47:46,430 - core.enhanced_staging_automation - ERROR - ❌ Failed to initialize automation engine: Failed to initialize persistent browser manager
2025-06-11 10:47:46,430 - automation_service - ERROR - ❌ Pre-initialization failed: Failed to initialize persistent browser manager
2025-06-11 10:57:18,953 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:57:18] "GET / HTTP/1.1" 200 -
2025-06-11 10:57:36,440 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 10:57:36,449 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 10:57:37,515 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:57:37] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-11 10:57:41,478 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 10:57:41,478 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:57:41] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-11 10:57:41,571 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 10:57:41,571 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:57:41] "GET /api/employees HTTP/1.1" 200 -
2025-06-11 11:06:46,097 - automation_service - INFO - Cleaning up automation service...
2025-06-11 11:06:46,097 - core.enhanced_staging_automation - INFO - Cleaning up Enhanced Staging Automation Engine
2025-06-11 11:06:46,097 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 11:06:46,112 - __main__ - INFO - System shutdown complete
2025-06-11 11:06:55,707 - __main__ - INFO - Configuration loaded successfully
2025-06-11 11:06:56,121 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-11 11:06:56,121 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 11:06:56,121 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 11:06:56,121 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 11:06:56,218 - core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 11:06:56,402 - core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 11:06:56,404 - core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 11:06:56,406 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 11:06:57,734 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-11 11:06:57,734 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 11:06:57,930 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:06:58,061 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:06:58,182 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 11:06:58,182 - core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 11:06:58,182 - core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 11:06:58,220 - core.browser_manager - WARNING - WebDriver creation attempt 1 failed: [WinError 193] %1 is not a valid Win32 application
2025-06-11 11:07:01,226 - core.browser_manager - INFO - Creating WebDriver (attempt 2/3)
2025-06-11 11:07:01,229 - core.browser_manager - WARNING - WebDriver creation attempt 2 failed: [WinError 193] %1 is not a valid Win32 application
2025-06-11 11:07:04,231 - core.browser_manager - INFO - Creating WebDriver (attempt 3/3)
2025-06-11 11:07:04,231 - core.browser_manager - WARNING - WebDriver creation attempt 3 failed: [WinError 193] %1 is not a valid Win32 application
2025-06-11 11:07:04,233 - core.browser_manager - ERROR - Failed to create WebDriver after 3 attempts
2025-06-11 11:07:04,233 - core.browser_manager - ERROR - Failed to create Chrome WebDriver: [WinError 193] %1 is not a valid Win32 application
2025-06-11 11:07:04,233 - core.browser_manager - ERROR - 🔧 SOLUTION: ChromeDriver architecture mismatch detected
2025-06-11 11:07:04,233 - core.browser_manager - ERROR -    Try: 1. Clear WebDriver cache: rm -rf ~/.wdm
2025-06-11 11:07:04,234 - core.browser_manager - ERROR -         2. Restart the application
2025-06-11 11:07:04,234 - core.browser_manager - ERROR -         3. Check Chrome browser version compatibility
2025-06-11 11:07:04,235 - core.persistent_browser_manager - ERROR - Failed to create WebDriver: [WinError 193] %1 is not a valid Win32 application
2025-06-11 11:07:04,235 - core.persistent_browser_manager - ERROR - ❌ Failed to initialize persistent browser session: [WinError 193] %1 is not a valid Win32 application
2025-06-11 11:07:04,235 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 11:07:04,235 - core.enhanced_staging_automation - ERROR - ❌ Failed to initialize automation engine: Failed to initialize persistent browser manager
2025-06-11 11:07:04,236 - automation_service - ERROR - ❌ Pre-initialization failed: Failed to initialize persistent browser manager
2025-06-11 11:07:05,341 - automation_service - INFO - Cleaning up automation service...
2025-06-11 11:07:05,341 - core.enhanced_staging_automation - INFO - Cleaning up Enhanced Staging Automation Engine
2025-06-11 11:07:05,341 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 11:07:05,341 - __main__ - INFO - System shutdown complete
2025-06-11 11:07:54,653 - __main__ - INFO - Configuration loaded successfully
2025-06-11 11:07:54,901 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-11 11:07:54,901 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 11:07:54,901 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 11:07:54,901 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 11:07:54,957 - core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 11:07:55,226 - core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 11:07:55,226 - core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 11:07:55,226 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 11:07:56,299 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:07:56,424 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:07:56,562 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 11:07:56,562 - core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 11:07:56,562 - core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 11:07:56,562 - core.browser_manager - WARNING - WebDriver creation attempt 1 failed: [WinError 193] %1 is not a valid Win32 application
2025-06-11 11:07:56,670 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-11 11:07:56,671 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 11:07:59,574 - core.browser_manager - INFO - Creating WebDriver (attempt 2/3)
2025-06-11 11:07:59,574 - core.browser_manager - WARNING - WebDriver creation attempt 2 failed: [WinError 193] %1 is not a valid Win32 application
2025-06-11 11:08:02,575 - core.browser_manager - INFO - Creating WebDriver (attempt 3/3)
2025-06-11 11:08:02,576 - core.browser_manager - WARNING - WebDriver creation attempt 3 failed: [WinError 193] %1 is not a valid Win32 application
2025-06-11 11:08:02,578 - core.browser_manager - ERROR - Failed to create WebDriver after 3 attempts
2025-06-11 11:08:02,578 - core.browser_manager - ERROR - Failed to create Chrome WebDriver: [WinError 193] %1 is not a valid Win32 application
2025-06-11 11:08:02,579 - core.browser_manager - ERROR - 🔧 SOLUTION: ChromeDriver architecture mismatch detected
2025-06-11 11:08:02,579 - core.browser_manager - ERROR -    Try: 1. Clear WebDriver cache: rm -rf ~/.wdm
2025-06-11 11:08:02,580 - core.browser_manager - ERROR -         2. Restart the application
2025-06-11 11:08:02,580 - core.browser_manager - ERROR -         3. Check Chrome browser version compatibility
2025-06-11 11:08:02,581 - core.persistent_browser_manager - ERROR - Failed to create WebDriver: [WinError 193] %1 is not a valid Win32 application
2025-06-11 11:08:02,581 - core.persistent_browser_manager - ERROR - ❌ Failed to initialize persistent browser session: [WinError 193] %1 is not a valid Win32 application
2025-06-11 11:08:02,581 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 11:08:02,583 - core.enhanced_staging_automation - ERROR - ❌ Failed to initialize automation engine: Failed to initialize persistent browser manager
2025-06-11 11:08:02,583 - automation_service - ERROR - ❌ Pre-initialization failed: Failed to initialize persistent browser manager
2025-06-11 11:08:52,427 - automation_service - INFO - Cleaning up automation service...
2025-06-11 11:08:52,427 - core.enhanced_staging_automation - INFO - Cleaning up Enhanced Staging Automation Engine
2025-06-11 11:08:52,427 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 11:08:52,428 - __main__ - INFO - System shutdown complete
2025-06-11 11:11:21,990 - __main__ - INFO - Configuration loaded successfully
2025-06-11 11:11:22,250 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-11 11:11:22,266 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 11:11:22,266 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 11:11:22,266 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 11:11:22,308 - core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 11:11:22,473 - core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 11:11:22,473 - core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 11:11:22,475 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 11:11:23,519 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:11:23,632 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:11:23,801 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 11:11:23,801 - core.browser_manager - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 11:11:23,801 - core.browser_manager - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 11:11:23,801 - core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 11:11:23,801 - core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 11:11:24,023 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-11 11:11:24,023 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 11:11:25,001 - core.browser_manager - INFO - ✅ WebDriver created and responsive
2025-06-11 11:11:25,078 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-11 11:11:25,098 - core.persistent_browser_manager - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-11 11:11:28,341 - core.persistent_browser_manager - INFO - WebDriver created and page loaded successfully
2025-06-11 11:11:28,341 - core.persistent_browser_manager - INFO - Performing initial login...
2025-06-11 11:11:39,695 - core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 11:11:41,725 - core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 11:11:41,725 - core.persistent_browser_manager - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-11 11:11:41,734 - core.persistent_browser_manager - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:11:43,443 - core.persistent_browser_manager - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:11:43,443 - core.persistent_browser_manager - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-11 11:11:51,494 - core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-11 11:11:51,494 - core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-11 11:11:51,494 - core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-11 11:11:51,509 - core.enhanced_staging_automation - INFO - ✅ Enhanced Staging Automation Engine initialized successfully
2025-06-11 11:11:51,509 - automation_service - ERROR - ❌ Failed to pre-initialize automation engine
2025-06-11 11:14:06,279 - automation_service - INFO - Cleaning up automation service...
2025-06-11 11:14:06,279 - core.enhanced_staging_automation - INFO - Cleaning up Enhanced Staging Automation Engine
2025-06-11 11:14:06,279 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 11:14:10,383 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E5BD6CEF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/d67ce38ded1792ac53a9249fd00b612a
2025-06-11 11:14:29,693 - __main__ - INFO - Configuration loaded successfully
2025-06-11 11:14:29,989 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-11 11:14:29,989 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 11:14:29,989 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 11:14:29,989 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 11:14:30,117 - core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 11:14:30,392 - core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 11:14:30,392 - core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 11:14:30,395 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 11:14:31,472 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:14:31,604 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:14:31,724 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-11 11:14:31,724 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 11:14:31,757 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 11:14:31,757 - core.browser_manager - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 11:14:31,757 - core.browser_manager - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 11:14:31,757 - core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 11:14:31,757 - core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 11:14:32,991 - core.browser_manager - INFO - ✅ WebDriver created and responsive
2025-06-11 11:14:33,083 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-11 11:14:33,091 - core.persistent_browser_manager - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-11 11:14:37,016 - core.persistent_browser_manager - INFO - WebDriver created and page loaded successfully
2025-06-11 11:14:37,017 - core.persistent_browser_manager - INFO - Performing initial login...
2025-06-11 11:14:46,401 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:14:46] "GET / HTTP/1.1" 200 -
2025-06-11 11:14:46,938 - core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 11:14:47,328 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 11:14:47,330 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 11:14:48,951 - core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 11:14:48,951 - core.persistent_browser_manager - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-11 11:14:48,952 - core.persistent_browser_manager - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:14:51,178 - core.persistent_browser_manager - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:14:51,178 - core.persistent_browser_manager - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-11 11:14:52,646 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 11:14:52,647 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:14:52] "GET /api/employees HTTP/1.1" 200 -
2025-06-11 11:14:52,979 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 11:14:52,981 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:14:52] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-11 11:14:59,200 - core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-11 11:14:59,201 - core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-11 11:14:59,202 - core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-11 11:14:59,207 - core.enhanced_staging_automation - INFO - ✅ Enhanced Staging Automation Engine initialized successfully
2025-06-11 11:14:59,208 - automation_service - ERROR - ❌ Failed to pre-initialize automation engine
2025-06-11 11:14:59,446 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:14:59] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-11 11:15:08,454 - data_interface.app - INFO - Processing 1 selected records
2025-06-11 11:15:08,462 - automation_service - WARNING - ⚠️ Automation engine not yet ready, will wait for initialization...
2025-06-11 11:15:30,155 - automation_service - INFO - Cleaning up automation service...
2025-06-11 11:15:30,155 - core.enhanced_staging_automation - INFO - Cleaning up Enhanced Staging Automation Engine
2025-06-11 11:15:30,156 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 11:21:49,136 - __main__ - INFO - Configuration loaded successfully
2025-06-11 11:21:49,394 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-11 11:21:49,395 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 11:21:49,396 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 11:21:49,396 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 11:21:49,454 - core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 11:21:49,769 - core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 11:21:49,770 - core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 11:21:49,772 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 11:21:50,905 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:21:51,064 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:21:51,160 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-11 11:21:51,160 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 11:21:51,229 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 11:21:51,230 - core.browser_manager - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 11:21:51,231 - core.browser_manager - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 11:21:51,232 - core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 11:21:51,232 - core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 11:21:52,637 - core.browser_manager - INFO - ✅ WebDriver created and responsive
2025-06-11 11:21:52,755 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-11 11:21:52,769 - core.persistent_browser_manager - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-11 11:21:57,267 - core.persistent_browser_manager - INFO - WebDriver created and page loaded successfully
2025-06-11 11:21:57,267 - core.persistent_browser_manager - INFO - Performing initial login...
2025-06-11 11:22:09,371 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:22:09] "GET / HTTP/1.1" 200 -
2025-06-11 11:22:09,663 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 11:22:09,664 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 11:22:09,972 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:22:09] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-11 11:22:12,365 - core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 11:22:14,381 - core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 11:22:14,381 - core.persistent_browser_manager - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-11 11:22:14,382 - core.persistent_browser_manager - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:22:14,720 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 11:22:14,721 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:22:14] "GET /api/employees HTTP/1.1" 200 -
2025-06-11 11:22:15,307 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 11:22:15,309 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:22:15] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-11 11:22:16,094 - core.persistent_browser_manager - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:22:16,094 - core.persistent_browser_manager - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-11 11:22:16,102 - core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-11 11:22:16,103 - core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-11 11:22:16,104 - core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-11 11:22:16,111 - core.enhanced_staging_automation - INFO - ✅ Enhanced Staging Automation Engine initialized successfully
2025-06-11 11:22:16,111 - automation_service - INFO - ✅ Automation engine pre-initialized successfully
2025-06-11 11:22:27,931 - data_interface.app - INFO - Processing 1 selected records
2025-06-11 11:22:27,935 - automation_service - INFO - 🏃 Starting fast automation job auto_20250611_112227
2025-06-11 11:22:27,935 - automation_service - INFO - 🚀 Started automation job auto_20250611_112227 for 1 records (using pre-initialized engine)
2025-06-11 11:22:27,936 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:22:27] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-11 11:22:27,938 - automation_service - INFO - Created 1 mock staging records
2025-06-11 11:22:27,956 - automation_service - INFO - 📋 Staging Data Summary for Job auto_20250611_112227:
2025-06-11 11:22:27,958 - automation_service - INFO - Record 1: Employee Test User - 2025-06-10 - (OC7190) BOILER OPERATION / STN-BLR (STATION BOILE...
2025-06-11 11:22:27,960 - automation_service - INFO - ⚡ Processing 1 staging records with pre-initialized engine
2025-06-11 11:22:27,973 - core.enhanced_staging_automation - INFO - 🤖 Starting automation process for 1 staging records
2025-06-11 11:22:27,982 - core.enhanced_staging_automation - INFO - Processing record 1/1: Employee Test User (c1e595b4-d498-4320-bce3-8d0f0cf52060)
2025-06-11 11:22:27,989 - core.enhanced_staging_automation - INFO - Processing record c1e595b4-d498-4320-bce3-8d0f0cf52060 (attempt 1/3)
2025-06-11 11:22:27,994 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-11 11:22:28,270 - core.enhanced_staging_automation - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:22:28,271 - core.persistent_browser_manager - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:22:28,328 - core.persistent_browser_manager - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:22:29,949 - data_interface.app - INFO - Using cached staging data
2025-06-11 11:22:29,949 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:22:29] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-11 11:22:31,080 - core.persistent_browser_manager - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:22:31,080 - core.persistent_browser_manager - INFO - ✅ SUCCESS: Reached task register page!
2025-06-11 11:22:31,087 - core.enhanced_staging_automation - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:22:31,088 - core.enhanced_staging_automation - INFO - 🔄 Waiting for task register form to be ready...
2025-06-11 11:22:48,175 - core.enhanced_staging_automation - ERROR - Failed to navigate to task register: Task register form not ready within timeout
2025-06-11 11:22:48,175 - core.enhanced_staging_automation - WARNING - ⚠️ Attempt 1 failed for record c1e595b4-d498-4320-bce3-8d0f0cf52060: Task register form not ready within timeout
2025-06-11 11:22:50,179 - core.enhanced_staging_automation - INFO - Attempting to recover page state...
2025-06-11 11:22:53,830 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-11 11:22:53,832 - core.enhanced_staging_automation - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:22:53,832 - core.persistent_browser_manager - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:22:53,856 - core.persistent_browser_manager - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:22:56,876 - core.persistent_browser_manager - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:22:56,876 - core.persistent_browser_manager - INFO - ✅ SUCCESS: Reached task register page!
2025-06-11 11:22:56,876 - core.enhanced_staging_automation - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:22:56,876 - core.enhanced_staging_automation - INFO - 🔄 Waiting for task register form to be ready...
2025-06-11 11:23:13,919 - core.enhanced_staging_automation - ERROR - Failed to navigate to task register: Task register form not ready within timeout
2025-06-11 11:23:13,920 - core.enhanced_staging_automation - WARNING - Page state recovery failed: Task register form not ready within timeout
2025-06-11 11:23:13,921 - core.enhanced_staging_automation - INFO - Processing record c1e595b4-d498-4320-bce3-8d0f0cf52060 (attempt 2/3)
2025-06-11 11:23:13,922 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-11 11:23:13,939 - core.enhanced_staging_automation - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:23:13,940 - core.persistent_browser_manager - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:23:13,966 - core.persistent_browser_manager - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:23:16,732 - core.persistent_browser_manager - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:23:16,732 - core.persistent_browser_manager - INFO - ✅ SUCCESS: Reached task register page!
2025-06-11 11:23:16,732 - core.enhanced_staging_automation - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:23:16,732 - core.enhanced_staging_automation - INFO - 🔄 Waiting for task register form to be ready...
2025-06-11 11:23:33,837 - core.enhanced_staging_automation - ERROR - Failed to navigate to task register: Task register form not ready within timeout
2025-06-11 11:23:33,837 - core.enhanced_staging_automation - WARNING - ⚠️ Attempt 2 failed for record c1e595b4-d498-4320-bce3-8d0f0cf52060: Task register form not ready within timeout
2025-06-11 11:23:35,843 - core.enhanced_staging_automation - INFO - Attempting to recover page state...
2025-06-11 11:23:39,539 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-11 11:23:39,545 - core.enhanced_staging_automation - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:23:39,545 - core.persistent_browser_manager - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:23:39,563 - core.persistent_browser_manager - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:23:42,276 - core.persistent_browser_manager - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:23:42,276 - core.persistent_browser_manager - INFO - ✅ SUCCESS: Reached task register page!
2025-06-11 11:23:42,276 - core.enhanced_staging_automation - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:23:42,276 - core.enhanced_staging_automation - INFO - 🔄 Waiting for task register form to be ready...
2025-06-11 11:23:59,351 - core.enhanced_staging_automation - ERROR - Failed to navigate to task register: Task register form not ready within timeout
2025-06-11 11:23:59,351 - core.enhanced_staging_automation - WARNING - Page state recovery failed: Task register form not ready within timeout
2025-06-11 11:23:59,351 - core.enhanced_staging_automation - INFO - Processing record c1e595b4-d498-4320-bce3-8d0f0cf52060 (attempt 3/3)
2025-06-11 11:23:59,351 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-11 11:23:59,351 - core.enhanced_staging_automation - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:23:59,351 - core.persistent_browser_manager - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:23:59,363 - core.persistent_browser_manager - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:24:02,200 - core.persistent_browser_manager - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:24:02,200 - core.persistent_browser_manager - INFO - ✅ SUCCESS: Reached task register page!
2025-06-11 11:24:02,200 - core.enhanced_staging_automation - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:24:02,200 - core.enhanced_staging_automation - INFO - 🔄 Waiting for task register form to be ready...
2025-06-11 11:24:19,283 - core.enhanced_staging_automation - ERROR - Failed to navigate to task register: Task register form not ready within timeout
2025-06-11 11:24:19,283 - core.enhanced_staging_automation - WARNING - ⚠️ Attempt 3 failed for record c1e595b4-d498-4320-bce3-8d0f0cf52060: Task register form not ready within timeout
2025-06-11 11:24:19,283 - core.enhanced_staging_automation - ERROR - ❌ Failed to process record c1e595b4-d498-4320-bce3-8d0f0cf52060 after 3 attempts: Task register form not ready within timeout
2025-06-11 11:24:19,283 - core.enhanced_staging_automation - INFO - ✅ Automation completed: 0 successful, 1 failed
2025-06-11 11:24:19,283 - automation_service - INFO - ✅ Job auto_20250611_112227 completed: 0 successful, 1 failed
2025-06-11 11:25:55,904 - automation_service - INFO - Cleaning up automation service...
2025-06-11 11:25:55,906 - core.enhanced_staging_automation - INFO - Cleaning up Enhanced Staging Automation Engine
2025-06-11 11:25:55,906 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 11:25:59,974 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002639D78BC80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8cb4af45a83c3210b1f499150fa41f8f
2025-06-11 11:26:04,050 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002639D5BF170>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8cb4af45a83c3210b1f499150fa41f8f
2025-06-11 11:31:11,017 - __main__ - INFO - Configuration loaded successfully
2025-06-11 11:31:11,384 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-11 11:31:11,384 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 11:31:11,384 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 11:31:11,384 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 11:31:11,548 - core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 11:31:11,845 - core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 11:31:11,845 - core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 11:31:11,845 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 11:31:13,032 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:31:13,050 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-11 11:31:13,051 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 11:31:13,210 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:31:13,379 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 11:31:13,379 - core.browser_manager - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 11:31:13,379 - core.browser_manager - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 11:31:13,379 - core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 11:31:13,379 - core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 11:31:15,173 - core.browser_manager - INFO - ✅ WebDriver created and responsive
2025-06-11 11:31:15,273 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-11 11:31:15,286 - core.persistent_browser_manager - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-11 11:31:18,109 - core.persistent_browser_manager - INFO - WebDriver created and page loaded successfully
2025-06-11 11:31:18,109 - core.persistent_browser_manager - INFO - Performing initial login...
2025-06-11 11:31:20,210 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:31:20] "GET / HTTP/1.1" 200 -
2025-06-11 11:31:20,300 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:31:20] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-11 11:31:20,662 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 11:31:20,972 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 11:31:26,242 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 11:31:26,243 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:31:26] "GET /api/employees HTTP/1.1" 200 -
2025-06-11 11:31:26,714 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 11:31:26,716 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:31:26] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-11 11:31:32,267 - core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 11:31:33,013 - data_interface.app - INFO - Processing 1 selected records
2025-06-11 11:31:33,016 - automation_service - WARNING - ⚠️ Automation engine not yet ready, will wait for initialization...
2025-06-11 11:31:34,290 - core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 11:31:34,291 - core.persistent_browser_manager - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-11 11:31:34,292 - core.persistent_browser_manager - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:31:36,115 - core.persistent_browser_manager - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:31:36,116 - core.persistent_browser_manager - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-11 11:31:36,126 - core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-11 11:31:36,127 - core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-11 11:31:36,127 - core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-11 11:31:36,135 - core.enhanced_staging_automation - INFO - ✅ Enhanced Staging Automation Engine initialized successfully
2025-06-11 11:31:36,135 - automation_service - INFO - ✅ Automation engine pre-initialized successfully
2025-06-11 11:31:37,018 - automation_service - INFO - 🏃 Starting fast automation job auto_20250611_113137
2025-06-11 11:31:37,018 - automation_service - INFO - 🚀 Started automation job auto_20250611_113137 for 1 records (using pre-initialized engine)
2025-06-11 11:31:37,020 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:31:37] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-11 11:31:37,021 - automation_service - INFO - Created 1 mock staging records
2025-06-11 11:31:37,027 - automation_service - INFO - 📋 Staging Data Summary for Job auto_20250611_113137:
2025-06-11 11:31:37,029 - automation_service - INFO - Record 1: Employee 78890366-777b-4661-a813-9bc83f2749b0 - 2025-06-10 - (OC7190) BOILER OPERATION / STN-BLR (STATION BOILE...
2025-06-11 11:31:37,029 - automation_service - INFO - ⚡ Processing 1 staging records with pre-initialized engine
2025-06-11 11:31:37,030 - core.enhanced_staging_automation - INFO - 🤖 Starting automation process for 1 staging records
2025-06-11 11:31:37,031 - core.enhanced_staging_automation - INFO - Processing record 1/1: Employee 78890366-777b-4661-a813-9bc83f2749b0 (78890366-777b-4661-a813-9bc83f2749b0)
2025-06-11 11:31:37,033 - core.enhanced_staging_automation - INFO - Processing record 78890366-777b-4661-a813-9bc83f2749b0 (attempt 1/3)
2025-06-11 11:31:37,035 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-11 11:31:37,043 - core.enhanced_staging_automation - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:31:37,043 - core.enhanced_staging_automation - INFO - ✅ Already on task register page - skipping navigation
2025-06-11 11:31:37,044 - core.enhanced_staging_automation - INFO - 🔄 Waiting for task register form to be ready...
2025-06-11 11:31:37,045 - core.enhanced_staging_automation - INFO - 🔍 Starting enhanced form readiness detection...
2025-06-11 11:31:37,097 - core.enhanced_staging_automation - INFO - ✅ Found date_field_1 (MainContent_txtTrxDate): displayed=True, enabled=True
2025-06-11 11:31:39,039 - data_interface.app - INFO - Using cached staging data
2025-06-11 11:31:39,041 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:31:39] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-11 11:31:43,672 - data_interface.app - INFO - Processing 1 selected records
2025-06-11 11:31:43,673 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:31:43] "[31m[1mPOST /api/process-selected HTTP/1.1[0m" 409 -
2025-06-11 11:31:45,117 - core.enhanced_staging_automation - WARNING - ❌ Missing date_field_2 (MainContent_txtDate): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_txtDate"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:31:53,134 - core.enhanced_staging_automation - WARNING - ❌ Missing employee_field (MainContent_txtEmployee): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_txtEmployee"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:32:01,192 - core.enhanced_staging_automation - WARNING - ❌ Missing autocomplete_1 (MainContent_acChargeJob1_txtInput): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_acChargeJob1_txtInput"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:32:09,259 - core.enhanced_staging_automation - WARNING - ❌ Missing autocomplete_2 (MainContent_acChargeJob2_txtInput): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_acChargeJob2_txtInput"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:32:09,259 - core.enhanced_staging_automation - INFO - 🎯 Using primary date field: MainContent_txtTrxDate
2025-06-11 11:32:09,259 - core.enhanced_staging_automation - INFO - 📝 Date field ID set to: MainContent_txtTrxDate
2025-06-11 11:32:09,259 - core.enhanced_staging_automation - INFO - 📊 Form readiness summary: 1/3 key elements found
2025-06-11 11:32:09,259 - core.enhanced_staging_automation - WARNING - ⚠️ Insufficient elements found, waiting for page to fully load...
2025-06-11 11:32:09,319 - core.enhanced_staging_automation - INFO - ✅ Date field became available after waiting
2025-06-11 11:32:09,320 - core.enhanced_staging_automation - INFO - ✅ Successfully navigated to task register and form is ready
2025-06-11 11:32:09,320 - core.enhanced_staging_automation - INFO - Filling date field (attempt 1) using ID: MainContent_txtTrxDate
2025-06-11 11:32:10,894 - core.enhanced_staging_automation - WARNING - Stale element on date field attempt 1: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd62c01]
	(No symbol) [0x0xd61ce0]
	(No symbol) [0x0xd583d2]
	(No symbol) [0x0xd568d1]
	(No symbol) [0x0xd59c4a]
	(No symbol) [0x0xd59cc7]
	(No symbol) [0x0xd9a51a]
	(No symbol) [0x0xd9a5a1]
	(No symbol) [0x0xd91fd4]
	(No symbol) [0x0xdbe57c]
	(No symbol) [0x0xd8eed4]
	(No symbol) [0x0xdbe7f4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:32:11,906 - core.enhanced_staging_automation - INFO - Filling date field (attempt 2) using ID: MainContent_txtTrxDate
2025-06-11 11:32:12,633 - core.enhanced_staging_automation - WARNING - Stale element on date field attempt 2: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd62c01]
	(No symbol) [0x0xd61ce0]
	(No symbol) [0x0xd583d2]
	(No symbol) [0x0xd568d1]
	(No symbol) [0x0xd59c4a]
	(No symbol) [0x0xd59cc7]
	(No symbol) [0x0xd9a51a]
	(No symbol) [0x0xd9a5a1]
	(No symbol) [0x0xd91fd4]
	(No symbol) [0x0xdbe57c]
	(No symbol) [0x0xd8eed4]
	(No symbol) [0x0xdbe7f4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:32:13,649 - core.enhanced_staging_automation - INFO - Filling date field (attempt 3) using ID: MainContent_txtTrxDate
2025-06-11 11:32:16,026 - core.enhanced_staging_automation - WARNING - Stale element on date field attempt 3: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd62c01]
	(No symbol) [0x0xd61ce0]
	(No symbol) [0x0xd583d2]
	(No symbol) [0x0xd568d1]
	(No symbol) [0x0xd59c4a]
	(No symbol) [0x0xd59cc7]
	(No symbol) [0x0xd9a51a]
	(No symbol) [0x0xd9a5a1]
	(No symbol) [0x0xd91fd4]
	(No symbol) [0x0xdbe57c]
	(No symbol) [0x0xd8eed4]
	(No symbol) [0x0xdbe7f4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:32:16,027 - core.enhanced_staging_automation - ERROR - Failed to fill form: Failed to fill date field after 3 attempts
2025-06-11 11:32:16,028 - core.enhanced_staging_automation - WARNING - ⚠️ Attempt 1 failed for record 78890366-777b-4661-a813-9bc83f2749b0: Failed to fill date field after 3 attempts
2025-06-11 11:32:18,034 - core.enhanced_staging_automation - INFO - Attempting to recover page state...
2025-06-11 11:32:21,376 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-11 11:32:21,384 - core.enhanced_staging_automation - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:32:21,384 - core.enhanced_staging_automation - INFO - ✅ Already on task register page - skipping navigation
2025-06-11 11:32:21,385 - core.enhanced_staging_automation - INFO - 🔄 Waiting for task register form to be ready...
2025-06-11 11:32:21,385 - core.enhanced_staging_automation - INFO - 🔍 Starting enhanced form readiness detection...
2025-06-11 11:32:21,424 - core.enhanced_staging_automation - INFO - ✅ Found date_field_1 (MainContent_txtTrxDate): displayed=True, enabled=True
2025-06-11 11:32:23,835 - core.enhanced_staging_automation - WARNING - ❌ Missing date_field_2 (MainContent_txtDate): ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))
2025-06-11 11:32:27,905 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD3A5580>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1cdcf02bf23f4e59b7b15500fa6fe375/element
2025-06-11 11:32:31,978 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD69FCB0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1cdcf02bf23f4e59b7b15500fa6fe375/element
2025-06-11 11:32:36,035 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD69F7A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1cdcf02bf23f4e59b7b15500fa6fe375/element
2025-06-11 11:32:40,099 - core.enhanced_staging_automation - WARNING - ❌ Missing employee_field (MainContent_txtEmployee): HTTPConnectionPool(host='localhost', port=50021): Max retries exceeded with url: /session/1cdcf02bf23f4e59b7b15500fa6fe375/element (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD69FE60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-11 11:32:44,154 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD6A01D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1cdcf02bf23f4e59b7b15500fa6fe375/element
2025-06-11 11:32:48,185 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD6A03B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1cdcf02bf23f4e59b7b15500fa6fe375/element
2025-06-11 11:32:52,241 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD6A06E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1cdcf02bf23f4e59b7b15500fa6fe375/element
2025-06-11 11:32:56,304 - core.enhanced_staging_automation - WARNING - ❌ Missing autocomplete_1 (MainContent_acChargeJob1_txtInput): HTTPConnectionPool(host='localhost', port=50021): Max retries exceeded with url: /session/1cdcf02bf23f4e59b7b15500fa6fe375/element (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FB8D21B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-11 11:33:00,344 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD6A0B60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1cdcf02bf23f4e59b7b15500fa6fe375/element
2025-06-11 11:33:04,423 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD6A0DA0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1cdcf02bf23f4e59b7b15500fa6fe375/element
2025-06-11 11:33:08,470 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD6A0FE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1cdcf02bf23f4e59b7b15500fa6fe375/element
2025-06-11 11:33:12,534 - core.enhanced_staging_automation - WARNING - ❌ Missing autocomplete_2 (MainContent_acChargeJob2_txtInput): HTTPConnectionPool(host='localhost', port=50021): Max retries exceeded with url: /session/1cdcf02bf23f4e59b7b15500fa6fe375/element (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD6A1220>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-11 11:33:12,534 - core.enhanced_staging_automation - INFO - 🎯 Using primary date field: MainContent_txtTrxDate
2025-06-11 11:33:12,534 - core.enhanced_staging_automation - INFO - 📝 Date field ID set to: MainContent_txtTrxDate
2025-06-11 11:33:12,534 - core.enhanced_staging_automation - INFO - 📊 Form readiness summary: 1/3 key elements found
2025-06-11 11:33:12,534 - core.enhanced_staging_automation - WARNING - ⚠️ Insufficient elements found, waiting for page to fully load...
2025-06-11 11:33:12,647 - automation_service - INFO - Cleaning up automation service...
2025-06-11 11:33:12,648 - automation_service - INFO - Job auto_20250611_113137 cancelled
2025-06-11 11:33:12,648 - core.enhanced_staging_automation - INFO - Cleaning up Enhanced Staging Automation Engine
2025-06-11 11:33:12,648 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 11:33:16,579 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD3A70E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1cdcf02bf23f4e59b7b15500fa6fe375/elements
2025-06-11 11:33:16,697 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD3C19D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1cdcf02bf23f4e59b7b15500fa6fe375
2025-06-11 11:33:20,626 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD69E300>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1cdcf02bf23f4e59b7b15500fa6fe375/elements
2025-06-11 11:33:20,758 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD69FE60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1cdcf02bf23f4e59b7b15500fa6fe375
2025-06-11 11:33:24,692 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD69E6C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1cdcf02bf23f4e59b7b15500fa6fe375/elements
2025-06-11 11:33:24,835 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD69DAC0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1cdcf02bf23f4e59b7b15500fa6fe375
2025-06-11 11:33:28,758 - core.enhanced_staging_automation - ERROR - ❌ Enhanced form readiness check failed: HTTPConnectionPool(host='localhost', port=50021): Max retries exceeded with url: /session/1cdcf02bf23f4e59b7b15500fa6fe375/elements (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD69E630>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-11 11:33:28,759 - core.enhanced_staging_automation - WARNING - ⚠️ Using fallback strategy - assuming form is ready
2025-06-11 11:33:28,759 - core.enhanced_staging_automation - INFO - ✅ Successfully navigated to task register and form is ready
2025-06-11 11:33:28,759 - core.enhanced_staging_automation - INFO - Processing record 78890366-777b-4661-a813-9bc83f2749b0 (attempt 2/3)
2025-06-11 11:33:28,760 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-11 11:33:32,820 - urllib3.connectionpool - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-06-11 11:33:32,820 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C8FD6A09E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1cdcf02bf23f4e59b7b15500fa6fe375/url
2025-06-11 11:33:32,992 - core.persistent_browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-11 11:33:32,993 - __main__ - INFO - System shutdown complete
2025-06-11 11:34:06,475 - __main__ - INFO - Configuration loaded successfully
2025-06-11 11:34:06,785 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-11 11:34:06,787 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 11:34:06,788 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 11:34:06,789 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 11:34:06,938 - core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 11:34:07,180 - core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 11:34:07,180 - core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 11:34:07,182 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 11:34:08,492 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:34:08,511 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-11 11:34:08,512 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 11:34:08,612 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:34:08,719 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 11:34:08,719 - core.browser_manager - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 11:34:08,720 - core.browser_manager - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 11:34:08,720 - core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 11:34:08,721 - core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 11:34:10,056 - core.browser_manager - INFO - ✅ WebDriver created and responsive
2025-06-11 11:34:10,438 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-11 11:34:10,445 - core.persistent_browser_manager - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-11 11:34:13,134 - core.persistent_browser_manager - INFO - WebDriver created and page loaded successfully
2025-06-11 11:34:13,134 - core.persistent_browser_manager - INFO - Performing initial login...
2025-06-11 11:34:26,573 - core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 11:34:28,591 - core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 11:34:28,591 - core.persistent_browser_manager - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-11 11:34:28,592 - core.persistent_browser_manager - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:34:30,309 - core.persistent_browser_manager - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:34:30,310 - core.persistent_browser_manager - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-11 11:34:30,319 - core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-11 11:34:30,319 - core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-11 11:34:30,321 - core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-11 11:34:30,326 - core.enhanced_staging_automation - INFO - ✅ Enhanced Staging Automation Engine initialized successfully
2025-06-11 11:34:30,327 - automation_service - INFO - ✅ Automation engine pre-initialized successfully
2025-06-11 11:34:31,285 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:34:31] "GET / HTTP/1.1" 200 -
2025-06-11 11:34:31,357 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:34:31] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-11 11:34:31,823 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 11:34:32,130 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 11:34:36,937 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 11:34:36,940 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:34:36] "GET /api/employees HTTP/1.1" 200 -
2025-06-11 11:34:37,021 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 11:34:37,022 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:34:37] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-11 11:34:41,259 - data_interface.app - INFO - Processing 1 selected records
2025-06-11 11:34:41,261 - automation_service - INFO - 🏃 Starting fast automation job auto_20250611_113441
2025-06-11 11:34:41,262 - automation_service - INFO - 🚀 Started automation job auto_20250611_113441 for 1 records (using pre-initialized engine)
2025-06-11 11:34:41,263 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:34:41] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-11 11:34:41,264 - automation_service - INFO - Created 1 mock staging records
2025-06-11 11:34:41,272 - automation_service - INFO - 📋 Staging Data Summary for Job auto_20250611_113441:
2025-06-11 11:34:41,272 - automation_service - INFO - Record 1: Employee Test User - 2025-06-10 - (OC7190) BOILER OPERATION / STN-BLR (STATION BOILE...
2025-06-11 11:34:41,272 - automation_service - INFO - ⚡ Processing 1 staging records with pre-initialized engine
2025-06-11 11:34:41,273 - core.enhanced_staging_automation - INFO - 🤖 Starting automation process for 1 staging records
2025-06-11 11:34:41,276 - core.enhanced_staging_automation - INFO - Processing record 1/1: Employee Test User (c1e595b4-d498-4320-bce3-8d0f0cf52060)
2025-06-11 11:34:41,279 - core.enhanced_staging_automation - INFO - Processing record c1e595b4-d498-4320-bce3-8d0f0cf52060 (attempt 1/3)
2025-06-11 11:34:41,279 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-11 11:34:41,287 - core.enhanced_staging_automation - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:34:41,288 - core.enhanced_staging_automation - INFO - ✅ Already on task register page - skipping navigation
2025-06-11 11:34:41,289 - core.enhanced_staging_automation - INFO - 🔄 Waiting for task register form to be ready...
2025-06-11 11:34:41,289 - core.enhanced_staging_automation - INFO - 🔍 Starting enhanced form readiness detection...
2025-06-11 11:34:41,363 - core.enhanced_staging_automation - INFO - ✅ Found date_field_1 (MainContent_txtTrxDate): displayed=True, enabled=True
2025-06-11 11:34:43,283 - data_interface.app - INFO - Using cached staging data
2025-06-11 11:34:43,286 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:34:43] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-11 11:34:49,419 - core.enhanced_staging_automation - WARNING - ❌ Missing date_field_2 (MainContent_txtDate): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_txtDate"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:34:57,448 - core.enhanced_staging_automation - WARNING - ❌ Missing employee_field (MainContent_txtEmployee): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_txtEmployee"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:35:05,449 - core.enhanced_staging_automation - WARNING - ❌ Missing autocomplete_1 (MainContent_acChargeJob1_txtInput): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_acChargeJob1_txtInput"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:35:13,477 - core.enhanced_staging_automation - WARNING - ❌ Missing autocomplete_2 (MainContent_acChargeJob2_txtInput): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_acChargeJob2_txtInput"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:35:13,477 - core.enhanced_staging_automation - INFO - 🎯 Using primary date field: MainContent_txtTrxDate
2025-06-11 11:35:13,477 - core.enhanced_staging_automation - INFO - 📝 Date field ID set to: MainContent_txtTrxDate
2025-06-11 11:35:13,477 - core.enhanced_staging_automation - INFO - 📊 Form readiness summary: 1/3 key elements found
2025-06-11 11:35:13,477 - core.enhanced_staging_automation - WARNING - ⚠️ Insufficient elements found, waiting for page to fully load...
2025-06-11 11:35:13,521 - core.enhanced_staging_automation - INFO - ✅ Date field became available after waiting
2025-06-11 11:35:13,523 - core.enhanced_staging_automation - INFO - ✅ Successfully navigated to task register and form is ready
2025-06-11 11:35:13,524 - core.enhanced_staging_automation - INFO - Filling date field (attempt 1) using ID: MainContent_txtTrxDate
2025-06-11 11:35:14,065 - core.enhanced_staging_automation - WARNING - Stale element on date field attempt 1: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd62c01]
	(No symbol) [0x0xd61ce0]
	(No symbol) [0x0xd583d2]
	(No symbol) [0x0xd568d1]
	(No symbol) [0x0xd59c4a]
	(No symbol) [0x0xd59cc7]
	(No symbol) [0x0xd9a51a]
	(No symbol) [0x0xd9a5a1]
	(No symbol) [0x0xd91fd4]
	(No symbol) [0x0xdbe57c]
	(No symbol) [0x0xd8eed4]
	(No symbol) [0x0xdbe7f4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:35:15,086 - core.enhanced_staging_automation - INFO - Filling date field (attempt 2) using ID: MainContent_txtTrxDate
2025-06-11 11:35:15,643 - core.enhanced_staging_automation - WARNING - Stale element on date field attempt 2: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd62c01]
	(No symbol) [0x0xd61ce0]
	(No symbol) [0x0xd583d2]
	(No symbol) [0x0xd568d1]
	(No symbol) [0x0xd59c4a]
	(No symbol) [0x0xd59cc7]
	(No symbol) [0x0xd9a51a]
	(No symbol) [0x0xd9a5a1]
	(No symbol) [0x0xd91fd4]
	(No symbol) [0x0xdbe57c]
	(No symbol) [0x0xd8eed4]
	(No symbol) [0x0xdbe7f4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:35:16,651 - core.enhanced_staging_automation - INFO - Filling date field (attempt 3) using ID: MainContent_txtTrxDate
2025-06-11 11:35:17,375 - core.enhanced_staging_automation - WARNING - Stale element on date field attempt 3: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd62c01]
	(No symbol) [0x0xd61ce0]
	(No symbol) [0x0xd583d2]
	(No symbol) [0x0xd568d1]
	(No symbol) [0x0xd59c4a]
	(No symbol) [0x0xd59cc7]
	(No symbol) [0x0xd9a51a]
	(No symbol) [0x0xd9a5a1]
	(No symbol) [0x0xd91fd4]
	(No symbol) [0x0xdbe57c]
	(No symbol) [0x0xd8eed4]
	(No symbol) [0x0xdbe7f4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:35:17,375 - core.enhanced_staging_automation - ERROR - Failed to fill form: Failed to fill date field after 3 attempts
2025-06-11 11:35:17,375 - core.enhanced_staging_automation - WARNING - ⚠️ Attempt 1 failed for record c1e595b4-d498-4320-bce3-8d0f0cf52060: Failed to fill date field after 3 attempts
2025-06-11 11:35:19,377 - core.enhanced_staging_automation - INFO - Attempting to recover page state...
2025-06-11 11:35:22,811 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-11 11:35:22,813 - core.enhanced_staging_automation - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:35:22,813 - core.enhanced_staging_automation - INFO - ✅ Already on task register page - skipping navigation
2025-06-11 11:35:22,813 - core.enhanced_staging_automation - INFO - 🔄 Waiting for task register form to be ready...
2025-06-11 11:35:22,813 - core.enhanced_staging_automation - INFO - 🔍 Starting enhanced form readiness detection...
2025-06-11 11:35:22,862 - core.enhanced_staging_automation - INFO - ✅ Found date_field_1 (MainContent_txtTrxDate): displayed=True, enabled=True
2025-06-11 11:35:30,914 - core.enhanced_staging_automation - WARNING - ❌ Missing date_field_2 (MainContent_txtDate): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_txtDate"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:35:38,923 - core.enhanced_staging_automation - WARNING - ❌ Missing employee_field (MainContent_txtEmployee): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_txtEmployee"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:35:46,937 - core.enhanced_staging_automation - WARNING - ❌ Missing autocomplete_1 (MainContent_acChargeJob1_txtInput): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_acChargeJob1_txtInput"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:35:54,996 - core.enhanced_staging_automation - WARNING - ❌ Missing autocomplete_2 (MainContent_acChargeJob2_txtInput): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_acChargeJob2_txtInput"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:35:54,996 - core.enhanced_staging_automation - INFO - 🎯 Using primary date field: MainContent_txtTrxDate
2025-06-11 11:35:54,996 - core.enhanced_staging_automation - INFO - 📝 Date field ID set to: MainContent_txtTrxDate
2025-06-11 11:35:54,996 - core.enhanced_staging_automation - INFO - 📊 Form readiness summary: 1/3 key elements found
2025-06-11 11:35:54,996 - core.enhanced_staging_automation - WARNING - ⚠️ Insufficient elements found, waiting for page to fully load...
2025-06-11 11:35:55,029 - core.enhanced_staging_automation - INFO - ✅ Date field became available after waiting
2025-06-11 11:35:55,029 - core.enhanced_staging_automation - INFO - ✅ Successfully navigated to task register and form is ready
2025-06-11 11:35:55,029 - core.enhanced_staging_automation - INFO - Processing record c1e595b4-d498-4320-bce3-8d0f0cf52060 (attempt 2/3)
2025-06-11 11:35:55,029 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-11 11:35:55,029 - core.enhanced_staging_automation - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:35:55,029 - core.enhanced_staging_automation - INFO - ✅ Already on task register page - skipping navigation
2025-06-11 11:35:55,029 - core.enhanced_staging_automation - INFO - 🔄 Waiting for task register form to be ready...
2025-06-11 11:35:55,029 - core.enhanced_staging_automation - INFO - 🔍 Starting enhanced form readiness detection...
2025-06-11 11:35:55,206 - core.enhanced_staging_automation - INFO - ✅ Found date_field_1 (MainContent_txtTrxDate): displayed=True, enabled=True
2025-06-11 11:36:03,213 - core.enhanced_staging_automation - WARNING - ❌ Missing date_field_2 (MainContent_txtDate): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_txtDate"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:36:11,261 - core.enhanced_staging_automation - WARNING - ❌ Missing employee_field (MainContent_txtEmployee): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_txtEmployee"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:36:19,318 - core.enhanced_staging_automation - WARNING - ❌ Missing autocomplete_1 (MainContent_acChargeJob1_txtInput): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_acChargeJob1_txtInput"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:36:27,343 - core.enhanced_staging_automation - WARNING - ❌ Missing autocomplete_2 (MainContent_acChargeJob2_txtInput): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_acChargeJob2_txtInput"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:36:27,343 - core.enhanced_staging_automation - INFO - 🎯 Using primary date field: MainContent_txtTrxDate
2025-06-11 11:36:27,343 - core.enhanced_staging_automation - INFO - 📝 Date field ID set to: MainContent_txtTrxDate
2025-06-11 11:36:27,343 - core.enhanced_staging_automation - INFO - 📊 Form readiness summary: 1/3 key elements found
2025-06-11 11:36:27,343 - core.enhanced_staging_automation - WARNING - ⚠️ Insufficient elements found, waiting for page to fully load...
2025-06-11 11:36:27,374 - core.enhanced_staging_automation - INFO - ✅ Date field became available after waiting
2025-06-11 11:36:27,390 - core.enhanced_staging_automation - INFO - ✅ Successfully navigated to task register and form is ready
2025-06-11 11:36:27,390 - core.enhanced_staging_automation - INFO - Filling date field (attempt 1) using ID: MainContent_txtTrxDate
2025-06-11 11:36:28,095 - core.enhanced_staging_automation - WARNING - Stale element on date field attempt 1: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd62c01]
	(No symbol) [0x0xd61ce0]
	(No symbol) [0x0xd583d2]
	(No symbol) [0x0xd568d1]
	(No symbol) [0x0xd59c4a]
	(No symbol) [0x0xd59cc7]
	(No symbol) [0x0xd9a51a]
	(No symbol) [0x0xd9a5a1]
	(No symbol) [0x0xd91fd4]
	(No symbol) [0x0xdbe57c]
	(No symbol) [0x0xd8eed4]
	(No symbol) [0x0xdbe7f4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:36:29,101 - core.enhanced_staging_automation - INFO - Filling date field (attempt 2) using ID: MainContent_txtTrxDate
2025-06-11 11:36:29,626 - core.enhanced_staging_automation - WARNING - Stale element on date field attempt 2: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd62c01]
	(No symbol) [0x0xd61ce0]
	(No symbol) [0x0xd583d2]
	(No symbol) [0x0xd568d1]
	(No symbol) [0x0xd59c4a]
	(No symbol) [0x0xd59cc7]
	(No symbol) [0x0xd9a51a]
	(No symbol) [0x0xd9a5a1]
	(No symbol) [0x0xd91fd4]
	(No symbol) [0x0xdbe57c]
	(No symbol) [0x0xd8eed4]
	(No symbol) [0x0xdbe7f4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:36:30,631 - core.enhanced_staging_automation - INFO - Filling date field (attempt 3) using ID: MainContent_txtTrxDate
2025-06-11 11:36:31,057 - core.enhanced_staging_automation - WARNING - Stale element on date field attempt 3: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd62c01]
	(No symbol) [0x0xd61ce0]
	(No symbol) [0x0xd583d2]
	(No symbol) [0x0xd568d1]
	(No symbol) [0x0xd59c4a]
	(No symbol) [0x0xd59cc7]
	(No symbol) [0x0xd9a51a]
	(No symbol) [0x0xd9a5a1]
	(No symbol) [0x0xd91fd4]
	(No symbol) [0x0xdbe57c]
	(No symbol) [0x0xd8eed4]
	(No symbol) [0x0xdbe7f4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:36:31,057 - core.enhanced_staging_automation - ERROR - Failed to fill form: Failed to fill date field after 3 attempts
2025-06-11 11:36:31,057 - core.enhanced_staging_automation - WARNING - ⚠️ Attempt 2 failed for record c1e595b4-d498-4320-bce3-8d0f0cf52060: Failed to fill date field after 3 attempts
2025-06-11 11:36:33,061 - core.enhanced_staging_automation - INFO - Attempting to recover page state...
2025-06-11 11:36:36,475 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-11 11:36:36,475 - core.enhanced_staging_automation - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:36:36,475 - core.enhanced_staging_automation - INFO - ✅ Already on task register page - skipping navigation
2025-06-11 11:36:36,475 - core.enhanced_staging_automation - INFO - 🔄 Waiting for task register form to be ready...
2025-06-11 11:36:36,475 - core.enhanced_staging_automation - INFO - 🔍 Starting enhanced form readiness detection...
2025-06-11 11:36:36,506 - core.enhanced_staging_automation - INFO - ✅ Found date_field_1 (MainContent_txtTrxDate): displayed=True, enabled=True
2025-06-11 11:36:44,580 - core.enhanced_staging_automation - WARNING - ❌ Missing date_field_2 (MainContent_txtDate): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_txtDate"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:36:52,596 - core.enhanced_staging_automation - WARNING - ❌ Missing employee_field (MainContent_txtEmployee): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_txtEmployee"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:37:00,631 - core.enhanced_staging_automation - WARNING - ❌ Missing autocomplete_1 (MainContent_acChargeJob1_txtInput): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_acChargeJob1_txtInput"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:37:08,645 - core.enhanced_staging_automation - WARNING - ❌ Missing autocomplete_2 (MainContent_acChargeJob2_txtInput): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_acChargeJob2_txtInput"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:37:08,645 - core.enhanced_staging_automation - INFO - 🎯 Using primary date field: MainContent_txtTrxDate
2025-06-11 11:37:08,645 - core.enhanced_staging_automation - INFO - 📝 Date field ID set to: MainContent_txtTrxDate
2025-06-11 11:37:08,645 - core.enhanced_staging_automation - INFO - 📊 Form readiness summary: 1/3 key elements found
2025-06-11 11:37:08,645 - core.enhanced_staging_automation - WARNING - ⚠️ Insufficient elements found, waiting for page to fully load...
2025-06-11 11:37:08,679 - core.enhanced_staging_automation - INFO - ✅ Date field became available after waiting
2025-06-11 11:37:08,679 - core.enhanced_staging_automation - INFO - ✅ Successfully navigated to task register and form is ready
2025-06-11 11:37:08,679 - core.enhanced_staging_automation - INFO - Processing record c1e595b4-d498-4320-bce3-8d0f0cf52060 (attempt 3/3)
2025-06-11 11:37:08,679 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-11 11:37:08,696 - core.enhanced_staging_automation - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:37:08,696 - core.enhanced_staging_automation - INFO - ✅ Already on task register page - skipping navigation
2025-06-11 11:37:08,697 - core.enhanced_staging_automation - INFO - 🔄 Waiting for task register form to be ready...
2025-06-11 11:37:08,697 - core.enhanced_staging_automation - INFO - 🔍 Starting enhanced form readiness detection...
2025-06-11 11:37:08,734 - core.enhanced_staging_automation - INFO - ✅ Found date_field_1 (MainContent_txtTrxDate): displayed=True, enabled=True
2025-06-11 11:37:16,781 - core.enhanced_staging_automation - WARNING - ❌ Missing date_field_2 (MainContent_txtDate): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_txtDate"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:37:24,827 - core.enhanced_staging_automation - WARNING - ❌ Missing employee_field (MainContent_txtEmployee): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_txtEmployee"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:37:32,846 - core.enhanced_staging_automation - WARNING - ❌ Missing autocomplete_1 (MainContent_acChargeJob1_txtInput): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_acChargeJob1_txtInput"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:37:37,350 - data_interface.app - INFO - Processing 1 selected records
2025-06-11 11:37:37,351 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:37:37] "[31m[1mPOST /api/process-selected HTTP/1.1[0m" 409 -
2025-06-11 11:37:40,913 - core.enhanced_staging_automation - WARNING - ❌ Missing autocomplete_2 (MainContent_acChargeJob2_txtInput): Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="MainContent_acChargeJob2_txtInput"]"}
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd9987e]
	(No symbol) [0x0xd99c1b]
	(No symbol) [0x0xde2212]
	(No symbol) [0x0xdbe5c4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:37:40,914 - core.enhanced_staging_automation - INFO - 🎯 Using primary date field: MainContent_txtTrxDate
2025-06-11 11:37:40,915 - core.enhanced_staging_automation - INFO - 📝 Date field ID set to: MainContent_txtTrxDate
2025-06-11 11:37:40,915 - core.enhanced_staging_automation - INFO - 📊 Form readiness summary: 1/3 key elements found
2025-06-11 11:37:40,916 - core.enhanced_staging_automation - WARNING - ⚠️ Insufficient elements found, waiting for page to fully load...
2025-06-11 11:37:40,957 - core.enhanced_staging_automation - INFO - ✅ Date field became available after waiting
2025-06-11 11:37:40,958 - core.enhanced_staging_automation - INFO - ✅ Successfully navigated to task register and form is ready
2025-06-11 11:37:40,958 - core.enhanced_staging_automation - INFO - Filling date field (attempt 1) using ID: MainContent_txtTrxDate
2025-06-11 11:37:41,493 - core.enhanced_staging_automation - WARNING - Stale element on date field attempt 1: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd62c01]
	(No symbol) [0x0xd61ce0]
	(No symbol) [0x0xd583d2]
	(No symbol) [0x0xd568d1]
	(No symbol) [0x0xd59c4a]
	(No symbol) [0x0xd59cc7]
	(No symbol) [0x0xd9a51a]
	(No symbol) [0x0xd9a5a1]
	(No symbol) [0x0xd91fd4]
	(No symbol) [0x0xdbe57c]
	(No symbol) [0x0xd8eed4]
	(No symbol) [0x0xdbe7f4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:37:42,506 - core.enhanced_staging_automation - INFO - Filling date field (attempt 2) using ID: MainContent_txtTrxDate
2025-06-11 11:37:43,005 - core.enhanced_staging_automation - WARNING - Stale element on date field attempt 2: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd62c01]
	(No symbol) [0x0xd61ce0]
	(No symbol) [0x0xd583d2]
	(No symbol) [0x0xd568d1]
	(No symbol) [0x0xd59c4a]
	(No symbol) [0x0xd59cc7]
	(No symbol) [0x0xd9a51a]
	(No symbol) [0x0xd9a5a1]
	(No symbol) [0x0xd91fd4]
	(No symbol) [0x0xdbe57c]
	(No symbol) [0x0xd8eed4]
	(No symbol) [0x0xdbe7f4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:37:44,013 - core.enhanced_staging_automation - INFO - Filling date field (attempt 3) using ID: MainContent_txtTrxDate
2025-06-11 11:37:44,482 - core.enhanced_staging_automation - WARNING - Stale element on date field attempt 3: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd51113]
	(No symbol) [0x0xd62c01]
	(No symbol) [0x0xd61ce0]
	(No symbol) [0x0xd583d2]
	(No symbol) [0x0xd568d1]
	(No symbol) [0x0xd59c4a]
	(No symbol) [0x0xd59cc7]
	(No symbol) [0x0xd9a51a]
	(No symbol) [0x0xd9a5a1]
	(No symbol) [0x0xd91fd4]
	(No symbol) [0x0xdbe57c]
	(No symbol) [0x0xd8eed4]
	(No symbol) [0x0xdbe7f4]
	(No symbol) [0x0xddfa4a]
	(No symbol) [0x0xdbe376]
	(No symbol) [0x0xd8d6e0]
	(No symbol) [0x0xd8e544]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	GetHandleVerifier [0x0xf2b6e8+95912]
	GetHandleVerifier [0x0xf2b890+96336]
	GetHandleVerifier [0x0xf1666a+9770]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:37:44,484 - core.enhanced_staging_automation - ERROR - Failed to fill form: Failed to fill date field after 3 attempts
2025-06-11 11:37:44,485 - core.enhanced_staging_automation - WARNING - ⚠️ Attempt 3 failed for record c1e595b4-d498-4320-bce3-8d0f0cf52060: Failed to fill date field after 3 attempts
2025-06-11 11:37:44,485 - core.enhanced_staging_automation - ERROR - ❌ Failed to process record c1e595b4-d498-4320-bce3-8d0f0cf52060 after 3 attempts: Failed to fill date field after 3 attempts
2025-06-11 11:37:44,488 - core.enhanced_staging_automation - INFO - ✅ Automation completed: 0 successful, 1 failed
2025-06-11 11:37:44,489 - automation_service - INFO - ✅ Job auto_20250611_113441 completed: 0 successful, 1 failed
2025-06-11 11:42:53,600 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 11:42:53] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-11 11:45:55,816 - automation_service - INFO - Cleaning up automation service...
2025-06-11 11:45:55,816 - core.enhanced_staging_automation - INFO - Cleaning up Enhanced Staging Automation Engine
2025-06-11 11:45:55,817 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 11:45:59,867 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000026C448C0E30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a606fd41398b0625df707fc54f45631c
2025-06-11 11:46:03,929 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000026C448C4230>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a606fd41398b0625df707fc54f45631c
2025-06-11 11:46:08,004 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000026C448C5DC0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a606fd41398b0625df707fc54f45631c
2025-06-11 11:46:16,113 - core.persistent_browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-11 11:46:16,115 - __main__ - INFO - System shutdown complete
2025-06-11 11:55:10,112 - __main__ - INFO - Configuration loaded successfully
2025-06-11 11:55:10,636 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-11 11:55:10,639 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 11:55:10,639 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 11:55:10,639 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 11:55:10,790 - core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 11:55:10,951 - core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 11:55:10,953 - core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 11:55:10,955 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 11:55:12,150 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-11 11:55:12,150 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 11:55:12,961 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:55:13,262 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:55:13,847 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 11:55:13,847 - core.browser_manager - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 11:55:13,848 - core.browser_manager - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 11:55:13,849 - core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 11:55:13,849 - core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 11:55:15,506 - core.browser_manager - INFO - ✅ WebDriver created and responsive
2025-06-11 11:55:15,663 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-11 11:55:15,682 - core.persistent_browser_manager - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-11 11:55:19,574 - core.persistent_browser_manager - INFO - WebDriver created and page loaded successfully
2025-06-11 11:55:19,574 - core.persistent_browser_manager - INFO - Performing initial login...
2025-06-11 11:55:26,545 - core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 11:55:31,473 - core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 11:55:31,475 - core.persistent_browser_manager - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-11 11:55:31,476 - core.persistent_browser_manager - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 11:55:32,893 - core.persistent_browser_manager - ERROR - Error handling location setting page: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0xf23783+63299]
	GetHandleVerifier [0x0xf237c4+63364]
	(No symbol) [0x0xd50f70]
	(No symbol) [0x0xd8c9a8]
	(No symbol) [0x0xdbe436]
	(No symbol) [0x0xdb9f05]
	(No symbol) [0x0xdb9486]
	(No symbol) [0x0xd23a05]
	(No symbol) [0x0xd23f5e]
	(No symbol) [0x0xd243ed]
	GetHandleVerifier [0x0x117e073+2531379]
	GetHandleVerifier [0x0x1179372+2511666]
	GetHandleVerifier [0x0xf49efa+220858]
	GetHandleVerifier [0x0xf3a548+156936]
	GetHandleVerifier [0x0xf40c7d+183357]
	(No symbol) [0x0xd236d0]
	(No symbol) [0x0xd22edd]
	GetHandleVerifier [0x0x12b7f1c+3817180]
	BaseThreadInitThunk [0x0x76535d49+25]
	RtlInitializeExceptionChain [0x0x77aad09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77aad021+561]

2025-06-11 11:55:32,896 - core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-11 11:55:32,897 - core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-11 11:55:32,897 - core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-11 11:55:32,901 - core.enhanced_staging_automation - ERROR - ❌ Failed to initialize automation engine: Failed to get WebDriver from persistent manager
2025-06-11 11:55:32,901 - automation_service - ERROR - ❌ Failed to pre-initialize automation engine
2025-06-11 11:55:50,829 - automation_service - INFO - Cleaning up automation service...
2025-06-11 11:55:50,830 - core.enhanced_staging_automation - INFO - Cleaning up Enhanced Staging Automation Engine
2025-06-11 11:55:50,830 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 12:17:23,199 - __main__ - INFO - Configuration loaded successfully
2025-06-11 12:17:23,710 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-11 12:17:23,715 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 12:17:23,715 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 12:17:23,715 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 12:17:23,917 - core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 12:17:24,091 - core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 12:17:24,091 - core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 12:17:24,097 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 12:17:25,249 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-11 12:17:25,249 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 12:17:25,867 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 12:17:26,179 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 12:17:26,347 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 12:17:26,349 - core.browser_manager - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 12:17:26,349 - core.browser_manager - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 12:17:26,349 - core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32\chromedriver.exe
2025-06-11 12:17:26,349 - core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 12:17:29,318 - core.browser_manager - INFO - ✅ WebDriver created and responsive
2025-06-11 12:17:29,601 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-11 12:17:29,612 - core.persistent_browser_manager - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-11 12:17:33,238 - core.persistent_browser_manager - INFO - WebDriver created and page loaded successfully
2025-06-11 12:17:33,238 - core.persistent_browser_manager - INFO - Performing initial login...
2025-06-11 12:17:42,794 - core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 12:17:47,832 - core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-11 12:17:47,832 - core.persistent_browser_manager - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-11 12:17:47,833 - core.persistent_browser_manager - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:17:49,867 - core.persistent_browser_manager - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 12:17:49,868 - core.persistent_browser_manager - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-11 12:17:49,879 - core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-11 12:17:49,880 - core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-11 12:17:49,881 - core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-11 12:17:49,889 - core.enhanced_staging_automation - INFO - ✅ Enhanced Staging Automation Engine initialized successfully
2025-06-11 12:17:49,890 - automation_service - INFO - ✅ Automation engine pre-initialized successfully
2025-06-11 12:17:59,415 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 12:17:59] "GET / HTTP/1.1" 200 -
2025-06-11 12:17:59,690 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 12:17:59] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-11 12:18:00,325 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 12:18:00,635 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 12:18:04,403 - data_interface.app - ERROR - Error fetching staging data: HTTPConnectionPool(host='localhost', port=5173): Max retries exceeded with url: /api/staging/data (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B32C50D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-11 12:18:04,403 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 12:18:04] "[35m[1mGET /api/employees HTTP/1.1[0m" 500 -
2025-06-11 12:18:04,685 - data_interface.app - ERROR - Error fetching staging data: HTTPConnectionPool(host='localhost', port=5173): Max retries exceeded with url: /api/staging/data?status=staged&limit=50 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B32183E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-11 12:18:04,685 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 12:18:04] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-11 12:18:26,840 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 12:18:26] "GET / HTTP/1.1" 200 -
2025-06-11 12:18:26,943 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 12:18:27,101 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 12:18:31,009 - data_interface.app - ERROR - Error fetching staging data: HTTPConnectionPool(host='localhost', port=5173): Max retries exceeded with url: /api/staging/data (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B34F8CB0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-11 12:18:31,010 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 12:18:31] "[35m[1mGET /api/employees HTTP/1.1[0m" 500 -
2025-06-11 12:18:31,181 - data_interface.app - ERROR - Error fetching staging data: HTTPConnectionPool(host='localhost', port=5173): Max retries exceeded with url: /api/staging/data?status=staged&limit=50 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B34F9FA0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-11 12:18:31,182 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 12:18:31] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-11 12:18:42,658 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 12:18:42] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-11 12:23:56,130 - automation_service - INFO - Cleaning up automation service...
2025-06-11 12:23:56,130 - core.enhanced_staging_automation - INFO - Cleaning up Enhanced Staging Automation Engine
2025-06-11 12:23:56,130 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-21 09:15:19,532 - __main__ - INFO - Configuration loaded successfully
2025-06-21 09:15:21,561 - __main__ - ERROR - System error: unexpected indent (enhanced_staging_automation.py, line 281)
2025-06-21 09:15:21,581 - __main__ - WARNING - Cleanup warning: unexpected indent (enhanced_staging_automation.py, line 281)
2025-06-21 09:15:21,581 - __main__ - INFO - System shutdown complete
2025-06-21 09:15:36,145 - __main__ - INFO - Configuration loaded successfully
2025-06-21 09:15:38,165 - __main__ - ERROR - System error: unexpected indent (enhanced_staging_automation.py, line 281)
2025-06-21 09:15:38,198 - __main__ - WARNING - Cleanup warning: unexpected indent (enhanced_staging_automation.py, line 281)
2025-06-21 09:15:38,198 - __main__ - INFO - System shutdown complete
2025-06-21 09:36:57,330 - __main__ - INFO - Configuration loaded successfully
2025-06-21 09:36:59,398 - __main__ - ERROR - System error: unexpected indent (enhanced_staging_automation.py, line 281)
2025-06-21 09:36:59,443 - __main__ - WARNING - Cleanup warning: unexpected indent (enhanced_staging_automation.py, line 281)
2025-06-21 09:36:59,443 - __main__ - INFO - System shutdown complete
2025-06-21 09:38:40,877 - __main__ - INFO - Configuration loaded successfully
2025-06-21 09:39:17,002 - __main__ - ERROR - System error: expected 'except' or 'finally' block (enhanced_staging_automation.py, line 425)
2025-06-21 09:39:17,051 - __main__ - WARNING - Cleanup warning: expected 'except' or 'finally' block (enhanced_staging_automation.py, line 425)
2025-06-21 09:39:17,051 - __main__ - INFO - System shutdown complete
2025-06-21 09:39:23,687 - __main__ - INFO - Configuration loaded successfully
2025-06-21 09:39:25,750 - __main__ - ERROR - System error: expected 'except' or 'finally' block (enhanced_staging_automation.py, line 425)
2025-06-21 09:39:25,805 - __main__ - WARNING - Cleanup warning: expected 'except' or 'finally' block (enhanced_staging_automation.py, line 425)
2025-06-21 09:39:25,807 - __main__ - INFO - System shutdown complete
2025-06-21 09:41:11,313 - __main__ - INFO - Configuration loaded successfully
2025-06-21 09:41:13,381 - __main__ - ERROR - System error: expected an indented block after 'for' statement on line 427 (enhanced_staging_automation.py, line 428)
2025-06-21 09:41:13,436 - __main__ - WARNING - Cleanup warning: expected an indented block after 'for' statement on line 427 (enhanced_staging_automation.py, line 428)
2025-06-21 09:41:13,439 - __main__ - INFO - System shutdown complete
2025-06-21 09:47:41,397 - __main__ - INFO - Configuration loaded successfully
2025-06-21 09:47:43,478 - __main__ - ERROR - System error: unexpected indent (enhanced_staging_automation.py, line 281)
2025-06-21 09:47:43,520 - __main__ - WARNING - Cleanup warning: unexpected indent (enhanced_staging_automation.py, line 281)
2025-06-21 09:47:43,523 - __main__ - INFO - System shutdown complete
2025-06-21 09:47:59,744 - __main__ - INFO - Configuration loaded successfully
2025-06-21 09:48:01,824 - __main__ - ERROR - System error: expected 'except' or 'finally' block (enhanced_staging_automation.py, line 425)
2025-06-21 09:48:01,892 - __main__ - WARNING - Cleanup warning: expected 'except' or 'finally' block (enhanced_staging_automation.py, line 425)
2025-06-21 09:48:01,896 - __main__ - INFO - System shutdown complete
2025-06-21 09:48:38,707 - __main__ - INFO - Configuration loaded successfully
2025-06-21 09:48:40,757 - __main__ - ERROR - System error: expected an indented block after 'for' statement on line 426 (enhanced_staging_automation.py, line 427)
2025-06-21 09:48:40,791 - __main__ - WARNING - Cleanup warning: expected an indented block after 'for' statement on line 426 (enhanced_staging_automation.py, line 427)
2025-06-21 09:48:40,795 - __main__ - INFO - System shutdown complete
2025-06-21 09:48:55,627 - __main__ - INFO - Configuration loaded successfully
2025-06-21 09:48:57,658 - __main__ - ERROR - System error: expected an indented block after 'for' statement on line 426 (enhanced_staging_automation.py, line 427)
2025-06-21 09:48:57,691 - __main__ - WARNING - Cleanup warning: expected an indented block after 'for' statement on line 426 (enhanced_staging_automation.py, line 427)
2025-06-21 09:48:57,691 - __main__ - INFO - System shutdown complete
