#!/usr/bin/env python3
"""
User-Controlled Automation System - Enhanced Version
Uses EXACT same logic from test_real_api_data.py but with user-selected data input
"""

import sys
import json
import logging
import asyncio
import threading
import time
import webbrowser
import requests
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Import the EXACT same processor from test_real_api_data.py
# Fix: import from test directory with correct path
sys.path.insert(0, str(Path(__file__).parent / "test"))
from test_real_api_data import RealAPIDataProcessor


class UserControlledAutomationSystem:
    """
    User-controlled automation using EXACT same logic from test_real_api_data.py
    Only difference: data source is user-selected records instead of all records
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.api_url = "http://localhost:5173/api/staging/data"
        self.web_server_thread = None
        
        # Use the EXACT same processor from test_real_api_data.py
        self.processor = RealAPIDataProcessor()
        self.is_browser_ready = False
        self.automation_mode = 'testing'  # Default mode
        
    async def initialize_browser_system(self) -> bool:
        """Initialize browser using exact same logic from test_real_api_data.py"""
        try:
            print("🚀 Initializing browser system using proven logic...")
            print(f"🔧 Initial automation mode: {self.automation_mode}")
            
            # Use exact same initialization from test_real_api_data.py
            success = await self.processor.initialize_browser()
            
            if success:
                self.is_browser_ready = True
                print("✅ Browser positioned at task register page and ready!")
                driver = self.processor.browser_manager.get_driver()
                print(f"📍 Current URL: {driver.current_url}")
                print(f"🔧 Mode will be applied during automation process")
                return True
            else:
                print("❌ Failed to initialize browser")
                return False
                
        except Exception as e:
            print(f"❌ Browser initialization failed: {e}")
            self.logger.error(f"Browser initialization error: {e}")
            return False
    
    async def fetch_staging_data(self) -> List[Dict]:
        """Fetch all staging data from API (same as processor but exposed)"""
        return await self.processor.fetch_real_api_data()
    
    def display_selected_records(self, selected_indices: List[int], all_records: List[Dict]):
        """Display selected records with detailed information"""
        try:
            print("\n" + "="*80)
            print("📋 SELECTED RECORDS FOR AUTOMATION")
            print("="*80)
            print(f"🎯 Total Selected: {len(selected_indices)} records")
            print(f"📊 Available Records: {len(all_records)} total")
            print("="*80)
            
            for i, index in enumerate(selected_indices, 1):
                if 0 <= index < len(all_records):
                    record = all_records[index]
                    
                    print(f"\n🔹 RECORD {i}/{len(selected_indices)} (Array Index: {index})")
                    print(f"🆔 Record ID: {record.get('id', 'N/A')}")
                    print(f"👤 Employee Name: {record.get('employee_name', 'N/A')}")
                    print(f"🏷️ Employee ID: {record.get('employee_id', 'N/A')}")
                    print(f"📅 Date: {record.get('date', 'N/A')}")
                    print(f"⏰ Regular Hours: {record.get('regular_hours', 0)}")
                    print(f"⏰ Overtime Hours: {record.get('overtime_hours', 0)}")
                    print(f"⏰ Total Hours: {record.get('total_hours', 0)}")
                    print(f"🔧 Raw Charge Job: {record.get('raw_charge_job', 'N/A')}")
                    
                    # Use exact same parsing from test_real_api_data.py
                    charge_components = self.processor.parse_raw_charge_job(record.get('raw_charge_job', ''))
                    if charge_components and len(charge_components) >= 4:
                        print(f"📋 Parsed Charge Job Components ({len(charge_components)} parts):")
                        for j, component in enumerate(charge_components):
                            component_type = ""
                            if j == 0:
                                component_type = " (Task Code)"
                            elif j == 1:
                                component_type = " (Location/Station)"
                            elif j == 2:
                                component_type = " (Sub-Location/Machine)"
                            elif j == 3:
                                component_type = " (Type/Expense)"
                            print(f"   [{j}]: {component}{component_type}")
                    else:
                        print(f"⚠️ Could not properly parse charge job (got {len(charge_components) if charge_components else 0} components)")
                    
                    print(f"🏭 Task Code (Direct): {record.get('task_code', 'N/A')}")
                    print(f"📍 Station Code (Direct): {record.get('station_code', 'N/A')}")
                    print(f"🔧 Machine Code (Direct): {record.get('machine_code', 'N/A')}")
                    print(f"💰 Expense Code (Direct): {record.get('expense_code', 'N/A')}")
                    print(f"📝 Status: {record.get('status', 'staged')}")
                    print(f"🗒️ Notes: {record.get('notes', 'N/A')}")
                    
                    # Use exact same overtime logic from test_real_api_data.py
                    overtime_entries = self.processor.create_overtime_entries(record)
                    if len(overtime_entries) > 1:
                        print(f"🔄 Automation Impact: Will create {len(overtime_entries)} entries")
                        for entry_idx, entry in enumerate(overtime_entries):
                            print(f"   Entry {entry_idx+1}: {entry.get('transaction_type', 'Normal')} - {entry.get('hours', 0)}h")
                    else:
                        print(f"🔄 Automation Impact: Will create 1 entry (Normal: {record.get('regular_hours', 0)}h)")
                    
                    print("-" * 80)
                else:
                    print(f"❌ INVALID INDEX: {index} (not in range 0-{len(all_records)-1})")
            
            print("="*80)
            print("🚀 Ready to start automation process using test_real_api_data.py logic!")
            print("="*80)
            
        except Exception as e:
            print(f"❌ Error displaying selected records: {e}")
            self.logger.error(f"Error displaying records: {e}")
            import traceback
            print(f"📋 Stack trace: {traceback.format_exc()}")
    
    async def process_selected_records(self, selected_indices: List[int]) -> bool:
        """Process user-selected records using EXACT same logic from test_real_api_data.py"""
        try:
            if not self.is_browser_ready:
                print("❌ Browser system not ready")
                return False
            
            # Fetch all staging data using exact same method
            print("🌐 Fetching staging data using proven method...")
            all_records = await self.processor.fetch_real_api_data()
            
            if not all_records:
                print("❌ No staging data available")
                return False
            
            # Get selected records based on indices
            selected_records = []
            for index in selected_indices:
                if 0 <= index < len(all_records):
                    selected_records.append(all_records[index])
                else:
                    print(f"⚠️ Invalid index {index}, skipping")
            
            if not selected_records:
                print("❌ No valid records selected")
                return False
            
            # Display selected records in console (this is what user wanted)
            self.display_selected_records(selected_indices, all_records)
            
            # Create entries using EXACT same logic from test_real_api_data.py
            all_entries = []
            for record in selected_records:
                entries = self.processor.create_overtime_entries(record)
                all_entries.extend(entries)
            
            print(f"\n📊 AUTOMATION SUMMARY:")
            print(f"📋 Selected Records: {len(selected_records)}")
            print(f"🔄 Total Entries to Process: {len(all_entries)}")
            
            # Log overtime breakdown using same logic
            normal_entries = [e for e in all_entries if e.get('transaction_type') == 'Normal']
            overtime_entries = [e for e in all_entries if e.get('transaction_type') == 'Overtime']
            print(f"📝 Normal Entries: {len(normal_entries)}")
            print(f"⏰ Overtime Entries: {len(overtime_entries)}")
            print("\n🚀 Starting automation process using proven test_real_api_data.py flow...")
            
            # Get driver using same method
            driver = self.processor.browser_manager.get_driver()
            if not driver:
                print("❌ WebDriver not available")
                return False
            
            # Process each entry using EXACT same logic from test_real_api_data.py
            successful_entries = 0
            failed_entries = 0
            
            for i, entry in enumerate(all_entries, 1):
                print(f"\n{'='*70}")
                print(f"🎯 Processing Entry {i}/{len(all_entries)}")
                print(f"👤 Employee: {entry.get('employee_name', 'Unknown')}")
                print(f"📅 Date: {entry.get('date', 'Unknown')}")
                print(f"🔘 Type: {entry.get('transaction_type', 'Normal')} - ⏰ Hours: {entry.get('hours', 0)}")
                print(f"{'='*70}")
                
                # Use ENHANCED process_single_record method with smart autocomplete and conditional hours
                success = await self.process_single_record_enhanced(driver, entry, i)
                
                if success:
                    successful_entries += 1
                    print(f"✅ Entry {i} completed successfully")
                else:
                    failed_entries += 1
                    print(f"❌ Entry {i} failed to process")
                
                # Same wait timing as test_real_api_data.py
                if i < len(all_entries):
                    print(f"⏳ Waiting 3 seconds before next entry...")
                    await asyncio.sleep(3)
            
            # Final summary using same format
            success_rate = (successful_entries / len(all_entries)) * 100 if all_entries else 0
            
            print(f"\n🎯 AUTOMATION COMPLETE!")
            print(f"📊 Selected Records: {len(selected_records)}")
            print(f"📊 Total Entries: {len(all_entries)}")
            print(f"✅ Successful: {successful_entries}/{len(all_entries)}")
            print(f"❌ Failed: {failed_entries}/{len(all_entries)}")
            print(f"📈 Success Rate: {success_rate:.1f}%")
            
            return successful_entries > 0
            
        except Exception as e:
            print(f"❌ Error processing selected records: {e}")
            self.logger.error(f"Processing error: {e}")
            import traceback
            print(f"📋 Stack trace: {traceback.format_exc()}")
            return False
    
    def start_web_interface(self):
        """Start Flask web interface for user selection"""
        try:
            from flask import Flask, render_template, jsonify, request
            
            app = Flask(__name__, template_folder='src/data_interface/templates')
            
            @app.route('/')
            def index():
                return render_template('index.html')
            
            @app.route('/api/staging/data')
            def get_staging_data():
                try:
                    # Use synchronous request for Flask compatibility
                    response = requests.get(self.api_url, timeout=30)
                    response.raise_for_status()
                    
                    response_data = response.json()
                    
                    if isinstance(response_data, dict) and 'data' in response_data:
                        data = response_data['data']
                        return jsonify({'data': data})
                    else:
                        return jsonify({'error': 'Unexpected API response structure'}), 500
                        
                except Exception as e:
                    self.logger.error(f"Error fetching staging data: {e}")
                    return jsonify({'error': str(e)}), 500
            
            @app.route('/api/employees')
            def get_employees():
                try:
                    # Get unique employee names from staging data
                    response = requests.get(self.api_url, timeout=30)
                    response.raise_for_status()
                    
                    response_data = response.json()
                    
                    if isinstance(response_data, dict) and 'data' in response_data:
                        data = response_data['data']
                        employees = list(set([record.get('employee_name', '') for record in data if record.get('employee_name')]))
                        employees.sort()
                        return jsonify(employees)
                    else:
                        return jsonify([])
                        
                except Exception as e:
                    self.logger.error(f"Error fetching employees: {e}")
                    return jsonify([])
            
            @app.route('/api/process-selected', methods=['POST'])
            def process_selected():
                try:
                    data = request.get_json()
                    selected_indices = data.get('selected_indices', [])
                    automation_mode = data.get('automation_mode', 'testing')  # Default to testing
                    
                    if not selected_indices:
                        return jsonify({'error': 'No records selected'}), 400
                    
                    # Set automation mode
                    self.automation_mode = automation_mode
                    
                    print(f"\n🎯 USER SELECTION RECEIVED:")
                    print(f"📋 Selected indices: {selected_indices}")
                    print(f"📊 Total selected: {len(selected_indices)} records")
                    print(f"🔧 Automation mode: {automation_mode.upper()}")
                    
                    if automation_mode == 'testing':
                        print(f"📅 Testing Mode: Document date akan dikurangi 1 bulan")
                        print(f"🔗 Testing Mode: Menggunakan port 8004")
                    else:
                        print(f"📅 Real Mode: Document date menggunakan tanggal sekarang")
                        print(f"🔗 Real Mode: Menggunakan port 8003")
                    
                    # Create a new event loop for the async function in a thread
                    def run_automation():
                        try:
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            loop.run_until_complete(self.process_selected_records(selected_indices))
                        except Exception as e:
                            print(f"❌ Automation thread error: {e}")
                            self.logger.error(f"Automation thread error: {e}")
                        finally:
                            loop.close()
                    
                    automation_thread = threading.Thread(target=run_automation, daemon=True)
                    automation_thread.start()
                    
                    return jsonify({
                        'success': True,
                        'message': f'Processing {len(selected_indices)} selected records using {automation_mode} mode',
                        'selected_count': len(selected_indices),
                        'automation_mode': automation_mode
                    })
                    
                except Exception as e:
                    self.logger.error(f"Process selected error: {e}")
                    return jsonify({'error': str(e)}), 500
            
            def run_flask():
                app.run(host='0.0.0.0', port=5000, debug=False, threaded=True, use_reloader=False)
            
            self.web_server_thread = threading.Thread(target=run_flask, daemon=True)
            self.web_server_thread.start()
            
            time.sleep(2)
            print("✅ Web interface started at http://localhost:5000")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start web interface: {e}")
            self.logger.error(f"Web interface error: {e}")
            return False
    
    async def cleanup(self):
        """Cleanup resources using same method as test_real_api_data.py"""
        try:
            await self.processor.cleanup()
        except Exception as e:
            self.logger.error(f"Cleanup error: {e}")

    async def smart_autocomplete_input(self, driver, field, target_value: str, field_name: str = "field") -> bool:
        """
        Smart autocomplete input: type partially, wait for options, select first if only one remains
        """
        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.common.keys import Keys
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            
            max_attempts = 3
            
            for attempt in range(max_attempts):
                try:
                    # Clear field first
                    field.clear()
                    await asyncio.sleep(0.5)
                    
                    # Type character by character and check options
                    typed_chars = ""
                    for i, char in enumerate(target_value):
                        typed_chars += char
                        field.send_keys(char)
                        await asyncio.sleep(0.3)  # Wait for autocomplete to respond
                        
                        # Check if autocomplete dropdown appeared
                        try:
                            # Look for autocomplete dropdown options
                            dropdown_options = driver.find_elements(By.CSS_SELECTOR, ".ui-autocomplete .ui-menu-item")
                            
                            if dropdown_options:
                                visible_options = [opt for opt in dropdown_options if opt.is_displayed()]
                                
                                if len(visible_options) == 1:
                                    # Only one option left, select it
                                    self.logger.info(f"✅ {field_name}: Found single option after typing '{typed_chars}', selecting first option")
                                    print(f"✅ {field_name}: Single option found, selecting...")
                                    
                                    # Press Down Arrow to highlight first option, then Enter
                                    field.send_keys(Keys.ARROW_DOWN)
                                    await asyncio.sleep(0.5)
                                    field.send_keys(Keys.ENTER)
                                    await asyncio.sleep(1)
                                    return True
                                    
                                elif len(visible_options) > 1 and i >= 2:  # Continue typing if more than 1 option and we've typed at least 3 chars
                                    continue
                                    
                            # If no dropdown appeared yet and we're near the end, just finish typing
                            elif i >= len(target_value) * 0.7:  # 70% of the text typed
                                continue
                                
                        except Exception as e:
                            # Continue typing if dropdown check failed
                            continue
                    
                    # If we've typed everything and no single option was found, use fallback
                    self.logger.info(f"⚠️ {field_name}: No single option found, using fallback method")
                    print(f"⚠️ {field_name}: Using fallback selection...")
                    
                    # Fallback: Arrow down + Enter
                    field.send_keys(Keys.ARROW_DOWN)
                    await asyncio.sleep(0.8)
                    field.send_keys(Keys.ENTER)
                    await asyncio.sleep(1.5)
                    
                    self.logger.info(f"✅ {field_name}: Completed using fallback method")
                    return True
                    
                except Exception as e:
                    self.logger.warning(f"⚠️ {field_name} attempt {attempt + 1} failed: {e}")
                    if attempt < max_attempts - 1:
                        await asyncio.sleep(1)
                        # Re-find field in case of stale element
                        try:
                            autocomplete_fields = driver.find_elements(By.CSS_SELECTOR, ".ui-autocomplete-input")
                            if autocomplete_fields:
                                field = autocomplete_fields[0]  # Adjust index as needed
                        except:
                            pass
                        continue
                    else:
                        return False
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Smart autocomplete for {field_name} failed: {e}")
            return False

    def calculate_document_date_by_mode(self, attendance_date_str: str, mode: str = 'testing') -> str:
        """
        Calculate document date based on automation mode:
        - Testing mode: Current date with month -1 (same day, previous month)
        - Real mode: Current date (no changes)
        """
        try:
            from datetime import datetime, timedelta
            from dateutil.relativedelta import relativedelta
            
            if mode == 'testing':
                # Testing mode: Tanggal sekarang dengan bulan dikurangi 1
                current_date = datetime.now()
                doc_date = current_date - relativedelta(months=1)
                formatted_date = doc_date.strftime("%d/%m/%Y")
                
                self.logger.info(f"📅 Testing mode: Current date {current_date.strftime('%d/%m/%Y')} → Document date {formatted_date} (same day, -1 month)")
                print(f"📅 Testing Mode: Document date tanggal sekarang bulan -1 → {formatted_date}")
                return formatted_date
                
            else:
                # Real mode: Gunakan tanggal sekarang tanpa pengurangan
                current_date = datetime.now()
                formatted_date = current_date.strftime("%d/%m/%Y")
                
                self.logger.info(f"📅 Real mode: Using current date as document date → {formatted_date}")
                print(f"📅 Real Mode: Document date menggunakan tanggal sekarang → {formatted_date}")
                return formatted_date
                
        except Exception as e:
            self.logger.error(f"❌ Error calculating document date: {e}")
            # Fallback to current date
            return datetime.now().strftime("%d/%m/%Y")

    def calculate_transaction_date_by_mode(self, original_date_str: str, mode: str = 'testing') -> str:
        """
        Calculate transaction date based on automation mode:
        - Testing mode: Original attendance date - 1 month (same month as document date)
        - Real mode: Use original attendance date as-is
        """
        try:
            from datetime import datetime
            from dateutil.relativedelta import relativedelta
            
            # Parse original date
            original_date = None
            if '/' in original_date_str and len(original_date_str.split('/')) == 3:
                try:
                    original_date = datetime.strptime(original_date_str, "%d/%m/%Y")
                except:
                    # Try alternative format
                    original_date = datetime.strptime(original_date_str, "%Y-%m-%d")
            else:
                original_date = datetime.strptime(original_date_str, "%Y-%m-%d")
            
            if mode == 'testing':
                # Testing mode: Kurangi 1 bulan dari tanggal original absen
                trx_date = original_date - relativedelta(months=1)
                formatted_date = trx_date.strftime("%d/%m/%Y")
                
                self.logger.info(f"📅 Testing mode: Original attendance date {original_date.strftime('%d/%m/%Y')} → Transaction date {formatted_date} (-1 month)")
                print(f"📅 Testing Mode: Transaction date dari absen dikurangi 1 bulan → {formatted_date}")
                return formatted_date
                
            else:
                # Real mode: Gunakan tanggal original dari data absen
                formatted_date = original_date.strftime("%d/%m/%Y")
                
                self.logger.info(f"📅 Real mode: Using original attendance date as transaction date → {formatted_date}")
                print(f"📅 Real Mode: Transaction date sesuai tanggal absen → {formatted_date}")
                return formatted_date
                
        except Exception as e:
            self.logger.error(f"❌ Error calculating transaction date: {e}")
            # Fallback to original format
            try:
                if '/' in original_date_str:
                    return original_date_str
                else:
                    # Convert YYYY-MM-DD to DD/MM/YYYY
                    date_obj = datetime.strptime(original_date_str, "%Y-%m-%d")
                    return date_obj.strftime("%d/%m/%Y")
            except:
                return original_date_str

    def get_task_register_url_by_mode(self, mode: str = 'testing') -> str:
        """
        Get task register URL based on automation mode:
        - Testing mode: Port 8004
        - Real mode: Port 8003
        """
        if mode == 'testing':
            url = 'http://millwarep3.rebinmas.com:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx'
            self.logger.info(f"🔗 Testing mode: Using port 8004 → {url}")
            print(f"🔗 Testing Mode: Port 8004 (Testing Database)")
        else:
            url = 'http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx'
            self.logger.info(f"🔗 Real mode: Using port 8003 → {url}")
            print(f"🔗 Real Mode: Port 8003 (Real Database)")
        
        return url

    def calculate_working_hours(self, record: Dict, transaction_type: str) -> float:
        """
        Calculate working hours based on business rules:
        - Regular: 7 hours (weekdays) or 5 hours (Saturday) if regular_hours > 0
        - Overtime: exact hours from API
        """
        try:
            from datetime import datetime
            
            # For overtime entries, always use exact API data
            if transaction_type == 'Overtime':
                overtime_hours = record.get('overtime_hours', 0)
                self.logger.info(f"⏰ Overtime hours from API: {overtime_hours}")
                return float(overtime_hours)
            
            # For regular entries, apply business logic
            regular_hours = record.get('regular_hours', 0)
            
            if regular_hours > 0:
                # Parse date to determine day of week
                date_str = record.get('date', '')
                try:
                    if '/' in date_str and len(date_str.split('/')) == 3:
                        date_obj = datetime.strptime(date_str, "%d/%m/%Y")
                    else:
                        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                    
                    # Check if Saturday (weekday 5 = Saturday)
                    is_saturday = date_obj.weekday() == 5
                    
                    if is_saturday:
                        calculated_hours = 5.0
                        self.logger.info(f"📅 Saturday detected: Using 5 hours for regular entry")
                    else:
                        calculated_hours = 7.0
                        self.logger.info(f"📅 Weekday detected: Using 7 hours for regular entry")
                    
                    print(f"⏰ Working Hours Logic: Date={date_str}, Day={'Saturday' if is_saturday else 'Weekday'}, Hours={calculated_hours}")
                    return calculated_hours
                    
                except Exception as e:
                    self.logger.warning(f"⚠️ Date parsing failed: {e}, using API hours")
                    return float(regular_hours)
            else:
                # If regular_hours is 0, use API data
                self.logger.info(f"⚠️ Regular hours is 0, using API data: {regular_hours}")
                return float(regular_hours)
                
        except Exception as e:
            self.logger.error(f"❌ Working hours calculation failed: {e}")
            # Fallback to API data
            if transaction_type == 'Overtime':
                return float(record.get('overtime_hours', 0))
            else:
                return float(record.get('regular_hours', 0))

    async def process_single_record_enhanced(self, driver, record: Dict, record_index: int) -> bool:
        """Enhanced single record processing with smart autocomplete and conditional working hours"""
        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.common.keys import Keys
            from selenium.common.exceptions import InvalidSessionIdException
            from calendar import monthrange
            
            employee_name = record.get('employee_name', '')
            date_value = record.get('date', '')
            raw_charge_job = record.get('raw_charge_job', '')
            transaction_type = record.get('transaction_type', 'Normal')
            
            # Calculate working hours using new business logic
            calculated_hours = self.calculate_working_hours(record, transaction_type)
            
            self.logger.info(f"🎯 Processing record {record_index}: {employee_name}")
            self.logger.info(f"📅 Date: {date_value}")
            self.logger.info(f"🔘 Transaction type: {transaction_type}")
            self.logger.info(f"⏰ Calculated hours: {calculated_hours}")
            
            # Step 0: Fill document date field using mode-based calculation
            try:
                formatted_doc_date = self.calculate_document_date_by_mode(date_value, self.automation_mode)
                self.logger.info(f"📅 Step 0: Filling document date ({self.automation_mode} mode): {formatted_doc_date}")
                
                script = f"""
                    var docDateField = document.getElementById('MainContent_txtDocDate');
                    if (docDateField) {{
                        docDateField.value = '{formatted_doc_date}';
                        docDateField.dispatchEvent(new Event('change', {{bubbles: true}}));
                        docDateField.dispatchEvent(new Event('blur', {{bubbles: true}}));
                        if (typeof SetTrxDate === 'function') {{
                            SetTrxDate();
                        }}
                        return true;
                    }}
                    return false;
                """
                
                result = driver.execute_script(script)
                if result:
                    self.logger.info(f"✅ Document date filled: {formatted_doc_date}")
                    await asyncio.sleep(1.5)
                else:
                    self.logger.warning("⚠️ Document date field not found")
                    
            except Exception as e:
                self.logger.warning(f"⚠️ Document date error: {e}")
            
            # Step 1: Fill transaction date field using mode-based calculation
            formatted_trx_date = self.calculate_transaction_date_by_mode(date_value, self.automation_mode)
            self.logger.info(f"📅 Step 1: Filling transaction date ({self.automation_mode} mode): {formatted_trx_date}")
            
            script = f"""
                var dateField = document.getElementById('MainContent_txtTrxDate');
                if (dateField) {{
                    dateField.value = '{formatted_trx_date}';
                    dateField.dispatchEvent(new Event('change', {{bubbles: true}}));
                    return true;
                }}
                return false;
            """
            
            result = driver.execute_script(script)
            if result:
                self.logger.info(f"✅ Transaction date filled: {formatted_trx_date}")
                date_field = driver.find_element(By.ID, "MainContent_txtTrxDate")
                date_field.send_keys(Keys.ENTER)
                await asyncio.sleep(2)
            else:
                self.logger.error("❌ Transaction date field not found")
                return False
            
            # Step 2: Fill employee field using SMART AUTOCOMPLETE
            self.logger.info(f"👤 Step 2: Filling employee using smart autocomplete: {employee_name}")
            
            autocomplete_fields = driver.find_elements(By.CSS_SELECTOR, ".ui-autocomplete-input")
            if len(autocomplete_fields) > 0:
                employee_field = autocomplete_fields[0]
                success = await self.smart_autocomplete_input(driver, employee_field, employee_name, "Employee")
                if success:
                    self.logger.info(f"✅ Employee filled using smart autocomplete: {employee_name}")
                    await asyncio.sleep(2)
                else:
                    self.logger.error("❌ Smart autocomplete failed for employee")
                    return False
            else:
                self.logger.error("❌ Employee field not found")
                return False
            
            # Step 3: Select transaction type (same as before)
            self.logger.info(f"🔘 Step 3: Selecting transaction type: {transaction_type}")
            success = await self.processor.select_transaction_type(driver, transaction_type)
            if not success:
                self.logger.error(f"❌ Failed to select transaction type: {transaction_type}")
                return False
            
            # Step 4: Fill charge job components using SMART AUTOCOMPLETE
            charge_components = self.processor.parse_raw_charge_job(raw_charge_job)
            if charge_components:
                self.logger.info(f"🔧 Step 4: Filling {len(charge_components)} charge job components using smart autocomplete")
                success = await self.fill_charge_job_smart_autocomplete(driver, charge_components)
                if not success:
                    return False
            
            # Step 5: Fill hours field using CALCULATED hours (not API hours)
            self.logger.info(f"⏰ Step 5: Filling calculated hours: {calculated_hours}")
            success = await self.processor.fill_hours_field(driver, calculated_hours)
            if not success:
                self.logger.error(f"❌ Failed to fill calculated hours: {calculated_hours}")
                return False
            
            # Step 6: Click Add button (same as before)
            add_selectors = [
                "input[value='Add']",
                "button[id*='Add']", 
                "input[id*='Add']",
                "input[id*='btn'][id*='Add']",
                "button[value='Add']"
            ]
            
            add_button = None
            for selector in add_selectors:
                try:
                    add_button = driver.find_element(By.CSS_SELECTOR, selector)
                    if add_button.is_displayed() and add_button.is_enabled():
                        break
                except:
                    continue
            
            if add_button:
                add_button.click()
                self.logger.info("✅ Add button clicked - Record saved!")
                await asyncio.sleep(3)
                return True
            else:
                self.logger.error("❌ Add button not found")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Record processing error: {e}")
            return False

    async def fill_charge_job_smart_autocomplete(self, driver, charge_components: List[str]) -> bool:
        """Fill charge job components using smart autocomplete method"""
        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.common.keys import Keys
            from selenium.common.exceptions import StaleElementReferenceException
            
            autocomplete_fields = driver.find_elements(By.CSS_SELECTOR, ".ui-autocomplete-input")
            
            for i, component in enumerate(charge_components):
                field_index = i + 1  # Skip employee field
                
                if field_index >= len(autocomplete_fields):
                    self.logger.warning(f"⚠️ No more fields for component {i}: {component}")
                    break
                
                field_name = f"Charge Component {i+1}"
                max_retries = 3
                retry_count = 0
                success = False
                
                while retry_count < max_retries and not success:
                    try:
                        # Refresh field list in case of stale elements
                        autocomplete_fields = driver.find_elements(By.CSS_SELECTOR, ".ui-autocomplete-input")
                        
                        if field_index >= len(autocomplete_fields):
                            break
                        
                        field = autocomplete_fields[field_index]
                        
                        if not field.is_displayed() or not field.is_enabled():
                            break
                        
                        # Use smart autocomplete instead of typing everything
                        success = await self.smart_autocomplete_input(driver, field, component, field_name)
                        
                        if success:
                            self.logger.info(f"✅ {field_name} filled using smart autocomplete: {component}")
                            await asyncio.sleep(1)
                            # Refresh field list for next iteration
                            autocomplete_fields = driver.find_elements(By.CSS_SELECTOR, ".ui-autocomplete-input")
                        
                    except StaleElementReferenceException:
                        retry_count += 1
                        if retry_count < max_retries:
                            await asyncio.sleep(2)
                        else:
                            return False
                    except Exception as e:
                        self.logger.error(f"❌ Error filling {field_name}: {e}")
                        return False
                
                if not success:
                    self.logger.error(f"❌ Failed to fill {field_name}: {component}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Smart autocomplete charge job filling failed: {e}")
            return False


def setup_logging():
    """Setup logging configuration (same as test_real_api_data.py)"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('user_controlled_automation.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )


async def main():
    """Main function implementing user-controlled automation workflow"""
    try:
        setup_logging()
        
        print("\n" + "="*80)
        print("🚀 USER-CONTROLLED AUTOMATION SYSTEM - USING test_real_api_data.py LOGIC")
        print("="*80)
        print("This system uses EXACT same proven automation flow:")
        print()
        print("1. 🔧 INITIALIZATION PHASE:")
        print("   - Uses same browser initialization as test_real_api_data.py")
        print("   - WebDriver positioned at task register page")
        print()
        print("2. 👤 USER SELECTION PHASE:")
        print("   - Web interface shows all available staging records")
        print("   - User selects specific records for processing")
        print("   - Selected records displayed in console with full details")
        print()
        print("3. 🤖 AUTOMATION EXECUTION PHASE:")
        print("   - Uses EXACT same RealAPIDataProcessor logic")
        print("   - Same field mapping and form filling sequence")
        print("   - Same overtime handling and error management")
        print("   - Only difference: processes selected records instead of all")
        print()
        print("="*80)
        print("⏳ Starting initialization using proven logic...")
        print("="*80)
        
        # Initialize system
        system = UserControlledAutomationSystem()
        
        # Initialize browser using exact same method as test_real_api_data.py
        print("🔧 Initializing browser system using test_real_api_data.py method...")
        if not await system.initialize_browser_system():
            print("❌ Failed to initialize browser system")
            return
        
        # Start web interface
        print("🌐 Starting web interface...")
        if not system.start_web_interface():
            print("❌ Failed to start web interface")
            return
        
        print("\n" + "="*80)
        print("✅ SYSTEM READY - USING PROVEN test_real_api_data.py LOGIC")
        print("="*80)
        print("🌐 WebDriver positioned at task register page")
        print("📱 Web interface: http://localhost:5000")
        print("👤 Select records and click 'Process Selected Records'")
        print("🔍 Selected records will be displayed in console")
        print("🚀 Automation uses exact same flow as test_real_api_data.py")
        print("="*80)
        
        # Open web browser
        try:
            webbrowser.open('http://localhost:5000')
            print("🌐 Web browser opened automatically")
        except:
            print("⚠️ Please manually open: http://localhost:5000")
        
        print("\n⏳ Waiting for user selection...")
        print("   (Press Ctrl+C to exit)")
        
        # Keep system running
        try:
            while True:
                await asyncio.sleep(5)
                
        except KeyboardInterrupt:
            print("\n🛑 User requested shutdown")
        
    except Exception as e:
        print(f"❌ System error: {e}")
        logging.exception("System error occurred")
    finally:
        print("\n🧹 Cleaning up...")
        try:
            if 'system' in locals():
                await system.cleanup()
            print("✅ Cleanup completed")
        except Exception as e:
            print(f"⚠️ Cleanup error: {e}")


if __name__ == "__main__":
    print("🚀 Starting User-Controlled Automation System using test_real_api_data.py logic...")
    asyncio.run(main()) 