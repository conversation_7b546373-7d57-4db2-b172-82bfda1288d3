#!/usr/bin/env python3
"""
Test Enhanced User-Controlled Automation System
Tests smart autocomplete input and conditional working hours logic
"""

import sys
import asyncio
import logging
from pathlib import Path
from datetime import datetime, timedelta

# Setup paths
sys.path.insert(0, str(Path(__file__).parent))

# Import our enhanced system
from run_user_controlled_automation import UserControlledAutomationSystem


class TestEnhancedAutomation:
    """Test cases for enhanced automation features"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.system = UserControlledAutomationSystem()
        
    def test_working_hours_calculation(self):
        """Test conditional working hours calculation logic"""
        print("\n🧪 TESTING WORKING HOURS CALCULATION LOGIC")
        print("="*60)
        
        # Test cases with different scenarios
        test_cases = [
            # Regular entry on weekday
            {
                'name': 'Regular Weekday Entry',
                'record': {
                    'employee_name': 'Test Employee',
                    'date': '2024-01-15',  # Monday
                    'regular_hours': 8,
                    'overtime_hours': 0
                },
                'transaction_type': 'Normal',
                'expected_hours': 7.0,
                'expected_reason': 'Weekday regular entry should be 7 hours'
            },
            # Regular entry on Saturday
            {
                'name': 'Regular Saturday Entry',
                'record': {
                    'employee_name': 'Test Employee',
                    'date': '2024-01-13',  # Saturday
                    'regular_hours': 5,
                    'overtime_hours': 0
                },
                'transaction_type': 'Normal',
                'expected_hours': 5.0,
                'expected_reason': 'Saturday regular entry should be 5 hours'
            },
            # Overtime entry (should use exact API data)
            {
                'name': 'Overtime Entry',
                'record': {
                    'employee_name': 'Test Employee',
                    'date': '2024-01-15',  # Monday
                    'regular_hours': 8,
                    'overtime_hours': 3
                },
                'transaction_type': 'Overtime',
                'expected_hours': 3.0,
                'expected_reason': 'Overtime entry should use exact API hours'
            },
            # Regular entry with 0 hours (edge case)
            {
                'name': 'Zero Regular Hours',
                'record': {
                    'employee_name': 'Test Employee',
                    'date': '2024-01-15',  # Monday
                    'regular_hours': 0,
                    'overtime_hours': 0
                },
                'transaction_type': 'Normal',
                'expected_hours': 0.0,
                'expected_reason': 'Zero regular hours should use API data'
            }
        ]
        
        passed_tests = 0
        total_tests = len(test_cases)
        
        for test_case in test_cases:
            try:
                calculated_hours = self.system.calculate_working_hours(
                    test_case['record'], 
                    test_case['transaction_type']
                )
                
                if calculated_hours == test_case['expected_hours']:
                    print(f"✅ {test_case['name']}: {calculated_hours}h (PASS)")
                    print(f"   📝 {test_case['expected_reason']}")
                    passed_tests += 1
                else:
                    print(f"❌ {test_case['name']}: Expected {test_case['expected_hours']}h, got {calculated_hours}h (FAIL)")
                    print(f"   📝 {test_case['expected_reason']}")
                
            except Exception as e:
                print(f"❌ {test_case['name']}: Exception occurred - {e}")
        
        print(f"\n📊 Working Hours Tests: {passed_tests}/{total_tests} passed")
        print(f"📈 Success Rate: {(passed_tests/total_tests*100):.1f}%")
        
        return passed_tests == total_tests
    
    def test_date_parsing_logic(self):
        """Test date parsing for Saturday detection"""
        print("\n🧪 TESTING DATE PARSING FOR SATURDAY DETECTION")
        print("="*50)
        
        test_dates = [
            ('2024-01-13', True, 'Saturday'),      # Saturday
            ('2024-01-14', False, 'Sunday'),       # Sunday
            ('2024-01-15', False, 'Monday'),       # Monday
            ('2024-01-16', False, 'Tuesday'),      # Tuesday
            ('2024-01-17', False, 'Wednesday'),    # Wednesday
            ('2024-01-18', False, 'Thursday'),     # Thursday
            ('2024-01-19', False, 'Friday'),       # Friday
            ('13/01/2024', True, 'Saturday DD/MM/YYYY format'),  # Different format
        ]
        
        passed_tests = 0
        total_tests = len(test_dates)
        
        for date_str, expected_saturday, day_name in test_dates:
            try:
                # Create test record
                test_record = {
                    'date': date_str,
                    'regular_hours': 8,
                    'overtime_hours': 0
                }
                
                # Calculate hours (this will trigger date parsing)
                calculated_hours = self.system.calculate_working_hours(test_record, 'Normal')
                
                # Check result
                is_saturday_detected = (calculated_hours == 5.0)
                
                if is_saturday_detected == expected_saturday:
                    status = "✅ PASS"
                    passed_tests += 1
                else:
                    status = "❌ FAIL"
                
                print(f"{status} {date_str} ({day_name}): {calculated_hours}h")
                
            except Exception as e:
                print(f"❌ {date_str}: Exception - {e}")
        
        print(f"\n📊 Date Parsing Tests: {passed_tests}/{total_tests} passed")
        print(f"📈 Success Rate: {(passed_tests/total_tests*100):.1f}%")
        
        return passed_tests == total_tests
    
    async def test_system_initialization(self):
        """Test system initialization without actually connecting to browser"""
        print("\n🧪 TESTING SYSTEM INITIALIZATION")
        print("="*40)
        
        try:
            # Test that the system can be created
            test_system = UserControlledAutomationSystem()
            print("✅ System object created successfully")
            
            # Test that processor is available
            if hasattr(test_system, 'processor') and test_system.processor:
                print("✅ RealAPIDataProcessor is available")
            else:
                print("❌ RealAPIDataProcessor not available")
                return False
            
            # Test that new methods are available
            required_methods = [
                'smart_autocomplete_input',
                'calculate_working_hours', 
                'process_single_record_enhanced',
                'fill_charge_job_smart_autocomplete'
            ]
            
            missing_methods = []
            for method in required_methods:
                if hasattr(test_system, method):
                    print(f"✅ Method '{method}' is available")
                else:
                    print(f"❌ Method '{method}' is missing")
                    missing_methods.append(method)
            
            if missing_methods:
                print(f"❌ Missing methods: {missing_methods}")
                return False
            
            print("✅ All required methods are available")
            return True
            
        except Exception as e:
            print(f"❌ System initialization test failed: {e}")
            return False
    
    def test_sample_data_processing(self):
        """Test data processing logic with sample data"""
        print("\n🧪 TESTING SAMPLE DATA PROCESSING LOGIC")
        print("="*50)
        
        # Sample staging data similar to what would come from API
        sample_records = [
            {
                'id': 'test_001',
                'employee_name': 'JOHN DOE',
                'employee_id': 'EMP001',
                'date': '2024-01-15',  # Monday
                'regular_hours': 8,
                'overtime_hours': 2,
                'total_hours': 10,
                'raw_charge_job': '(ABC123) / PRODUCTION / MACHINE-01 / MAINTENANCE',
                'task_code': 'ABC123',
                'station_code': 'PRODUCTION',
                'machine_code': 'MACHINE-01',
                'expense_code': 'MAINTENANCE',
                'status': 'staged'
            },
            {
                'id': 'test_002',
                'employee_name': 'JANE SMITH',
                'employee_id': 'EMP002',
                'date': '2024-01-13',  # Saturday
                'regular_hours': 5,
                'overtime_hours': 0,
                'total_hours': 5,
                'raw_charge_job': '(XYZ789) / ASSEMBLY / LINE-02 / OPERATION',
                'task_code': 'XYZ789',
                'station_code': 'ASSEMBLY',
                'machine_code': 'LINE-02',
                'expense_code': 'OPERATION',
                'status': 'staged'
            }
        ]
        
        try:
            # Test charge job parsing
            print("🔧 Testing charge job parsing:")
            for record in sample_records:
                charge_components = self.system.processor.parse_raw_charge_job(record['raw_charge_job'])
                print(f"   📋 {record['employee_name']}: {len(charge_components)} components parsed")
                if charge_components:
                    for i, component in enumerate(charge_components):
                        print(f"      [{i}]: {component}")
            
            # Test overtime entry creation
            print("\n🔄 Testing overtime entry creation:")
            for record in sample_records:
                entries = self.system.processor.create_overtime_entries(record)
                print(f"   👤 {record['employee_name']}: {len(entries)} entries created")
                for entry in entries:
                    transaction_type = entry.get('transaction_type', 'Normal')
                    original_hours = entry.get('hours', 0)
                    calculated_hours = self.system.calculate_working_hours(entry, transaction_type)
                    print(f"      🔘 {transaction_type}: {original_hours}h → {calculated_hours}h (calculated)")
            
            print("✅ Sample data processing test completed")
            return True
            
        except Exception as e:
            print(f"❌ Sample data processing test failed: {e}")
            return False
    
    async def run_all_tests(self):
        """Run all test cases"""
        print("\n" + "="*80)
        print("🧪 ENHANCED USER-CONTROLLED AUTOMATION SYSTEM TESTS")
        print("="*80)
        print("Testing smart autocomplete input and conditional working hours")
        print("="*80)
        
        test_results = []
        
        # Test 1: System Initialization
        result1 = await self.test_system_initialization()
        test_results.append(("System Initialization", result1))
        
        # Test 2: Working Hours Calculation
        result2 = self.test_working_hours_calculation()
        test_results.append(("Working Hours Calculation", result2))
        
        # Test 3: Date Parsing Logic
        result3 = self.test_date_parsing_logic()
        test_results.append(("Date Parsing Logic", result3))
        
        # Test 4: Sample Data Processing
        result4 = self.test_sample_data_processing()
        test_results.append(("Sample Data Processing", result4))
        
        # Summary
        passed_tests = sum(1 for _, result in test_results if result)
        total_tests = len(test_results)
        
        print("\n" + "="*80)
        print("📊 TEST RESULTS SUMMARY")
        print("="*80)
        
        for test_name, result in test_results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
        
        print(f"\n📈 Overall Results:")
        print(f"✅ Tests Passed: {passed_tests}/{total_tests}")
        print(f"📈 Success Rate: {(passed_tests/total_tests*100):.1f}%")
        
        if passed_tests == total_tests:
            print("🎉 ALL TESTS PASSED! Enhanced system is ready for production use.")
        else:
            print("⚠️ Some tests failed. Please review the implementation.")
        
        print("="*80)
        
        return passed_tests == total_tests


def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('test_enhanced_automation.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )


async def main():
    """Main test function"""
    setup_logging()
    
    print("🚀 Starting Enhanced User-Controlled Automation Tests...")
    
    tester = TestEnhancedAutomation()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 All tests completed successfully!")
        print("🚀 Enhanced system ready for use with:")
        print("   • Smart autocomplete input (type partially, select when 1 option)")
        print("   • Conditional working hours (7h weekdays, 5h Saturday)")
        print("   • Exact overtime hours from API")
        print("   • Proven automation flow from test_real_api_data.py")
    else:
        print("\n⚠️ Some tests failed. Please check the implementation.")
    
    return success


if __name__ == "__main__":
    asyncio.run(main()) 