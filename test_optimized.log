2025-06-11 08:10:03,320 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 08:10:03,321 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 08:10:03,322 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 08:10:03,322 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 08:10:05,246 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 08:10:06,665 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 08:10:07,857 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/chromedriver.exe] found in cache
2025-06-11 08:10:09,685 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-11 08:10:09,685 - core.persistent_browser_manager - INFO - Navigating to login page: http://millwarep3:8004/
2025-06-11 08:10:39,696 - core.persistent_browser_manager - ERROR - Failed to create WebDriver: Message: timeout: Timed out receiving message from renderer: 28.758
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0x1073783+63299]
	GetHandleVerifier [0x0x10737c4+63364]
	(No symbol) [0x0xea1113]
	(No symbol) [0x0xe91beb]
	(No symbol) [0x0xe91921]
	(No symbol) [0x0xe8f8c4]
	(No symbol) [0x0xe9038d]
	(No symbol) [0x0xe9cb99]
	(No symbol) [0x0xeae265]
	(No symbol) [0x0xeb3c96]
	(No symbol) [0x0xe909cd]
	(No symbol) [0x0xeadfc9]
	(No symbol) [0x0xf2fd65]
	(No symbol) [0x0xf0e376]
	(No symbol) [0x0xedd6e0]
	(No symbol) [0x0xede544]
	GetHandleVerifier [0x0x12ce073+2531379]
	GetHandleVerifier [0x0x12c9372+2511666]
	GetHandleVerifier [0x0x1099efa+220858]
	GetHandleVerifier [0x0x108a548+156936]
	GetHandleVerifier [0x0x1090c7d+183357]
	GetHandleVerifier [0x0x107b6e8+95912]
	GetHandleVerifier [0x0x107b890+96336]
	GetHandleVerifier [0x0x106666a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-11 08:10:39,697 - core.persistent_browser_manager - ERROR - ❌ Failed to initialize persistent browser session: Message: timeout: Timed out receiving message from renderer: 28.758
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0x1073783+63299]
	GetHandleVerifier [0x0x10737c4+63364]
	(No symbol) [0x0xea1113]
	(No symbol) [0x0xe91beb]
	(No symbol) [0x0xe91921]
	(No symbol) [0x0xe8f8c4]
	(No symbol) [0x0xe9038d]
	(No symbol) [0x0xe9cb99]
	(No symbol) [0x0xeae265]
	(No symbol) [0x0xeb3c96]
	(No symbol) [0x0xe909cd]
	(No symbol) [0x0xeadfc9]
	(No symbol) [0x0xf2fd65]
	(No symbol) [0x0xf0e376]
	(No symbol) [0x0xedd6e0]
	(No symbol) [0x0xede544]
	GetHandleVerifier [0x0x12ce073+2531379]
	GetHandleVerifier [0x0x12c9372+2511666]
	GetHandleVerifier [0x0x1099efa+220858]
	GetHandleVerifier [0x0x108a548+156936]
	GetHandleVerifier [0x0x1090c7d+183357]
	GetHandleVerifier [0x0x107b6e8+95912]
	GetHandleVerifier [0x0x107b890+96336]
	GetHandleVerifier [0x0x106666a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-11 08:10:39,699 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 08:10:41,950 - core.persistent_browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-11 08:10:41,951 - core.enhanced_staging_automation - ERROR - ❌ Failed to initialize automation engine: Failed to initialize persistent browser manager
2025-06-11 08:10:41,956 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-11 08:10:41,960 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 08:10:41,961 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 08:10:41,962 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 08:10:41,963 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 08:10:43,524 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 08:10:45,713 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 08:10:46,596 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/chromedriver.exe] found in cache
2025-06-11 08:10:48,036 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-11 08:10:48,037 - core.persistent_browser_manager - INFO - Navigating to login page: http://millwarep3:8004/
2025-06-11 11:07:21,381 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 11:07:21,390 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 11:07:21,391 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 11:07:21,449 - core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 11:07:21,640 - core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 11:07:21,640 - core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 11:07:21,640 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 11:07:22,646 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:07:22,758 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:07:22,875 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 11:07:22,875 - core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 11:07:22,875 - core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 11:07:22,892 - core.browser_manager - WARNING - WebDriver creation attempt 1 failed: [WinError 193] %1 is not a valid Win32 application
2025-06-11 11:07:25,893 - core.browser_manager - INFO - Creating WebDriver (attempt 2/3)
2025-06-11 11:07:25,894 - core.browser_manager - WARNING - WebDriver creation attempt 2 failed: [WinError 193] %1 is not a valid Win32 application
2025-06-11 11:07:28,896 - core.browser_manager - INFO - Creating WebDriver (attempt 3/3)
2025-06-11 11:07:28,896 - core.browser_manager - WARNING - WebDriver creation attempt 3 failed: [WinError 193] %1 is not a valid Win32 application
2025-06-11 11:07:28,896 - core.browser_manager - ERROR - Failed to create WebDriver after 3 attempts
2025-06-11 11:07:28,896 - core.browser_manager - ERROR - Failed to create Chrome WebDriver: [WinError 193] %1 is not a valid Win32 application
2025-06-11 11:07:28,896 - core.browser_manager - ERROR - 🔧 SOLUTION: ChromeDriver architecture mismatch detected
2025-06-11 11:07:28,896 - core.browser_manager - ERROR -    Try: 1. Clear WebDriver cache: rm -rf ~/.wdm
2025-06-11 11:07:28,896 - core.browser_manager - ERROR -         2. Restart the application
2025-06-11 11:07:28,896 - core.browser_manager - ERROR -         3. Check Chrome browser version compatibility
2025-06-11 11:07:28,896 - core.persistent_browser_manager - ERROR - Failed to create WebDriver: [WinError 193] %1 is not a valid Win32 application
2025-06-11 11:07:28,896 - core.persistent_browser_manager - ERROR - ❌ Failed to initialize persistent browser session: [WinError 193] %1 is not a valid Win32 application
2025-06-11 11:07:28,896 - core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-11 11:07:28,896 - core.enhanced_staging_automation - ERROR - ❌ Failed to initialize automation engine: Failed to initialize persistent browser manager
2025-06-11 11:07:28,901 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-11 11:07:28,903 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-11 11:07:28,903 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-11 11:07:28,904 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-11 11:07:28,905 - core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-11 11:07:29,224 - core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-11 11:07:29,224 - core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-11 11:07:29,225 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 11:07:30,340 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:07:30,469 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 11:07:30,618 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-11 11:07:30,618 - core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-11 11:07:30,619 - core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-11 11:07:30,619 - core.browser_manager - WARNING - WebDriver creation attempt 1 failed: [WinError 193] %1 is not a valid Win32 application
