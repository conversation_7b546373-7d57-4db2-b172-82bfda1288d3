#!/usr/bin/env python3
"""
Test script for automatic filling functionality only
Tests the form filling after successfully reaching task register page
"""

import asyncio
import logging
import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.enhanced_staging_automation import EnhancedStagingAutomationEngine, StagingRecord


def setup_logging():
    """Setup basic logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def load_config():
    """Load test configuration"""
    return {
        "browser": {
            "headless": False,
            "window_size": [1280, 720],
            "disable_notifications": True
        },
        "automation": {
            "implicit_wait": 8,
            "page_load_timeout": 15,
            "script_timeout": 15,
            "max_retries": 3
        },
        "credentials": {
            "username": "adm075",
            "password": "adm075"
        },
        "urls": {
            "login": "http://millwarep3:8004/",
            "taskRegister": "http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx"
        }
    }


def create_test_record():
    """Create a test staging record"""
    return StagingRecord(
        id="test_001",
        employee_name="Admin Test",
        employee_id="adm075",
        date="2025-06-11",
        task_code="TEST",
        station_code="STA001",
        raw_charge_job="(2209002) PR-MILL-PROD / LINE 1 / (000098) OPERASIONAL / (000007) PRODUKSI",
        status="PENDING",
        hours=7.0,
        unit=1.0
    )


async def test_form_filling():
    """Test only the form filling functionality"""
    print("\n" + "="*70)
    print("🧪 TESTING AUTOMATIC FORM FILLING")
    print("="*70)
    
    config = load_config()
    
    try:
        print("1️⃣ Creating automation engine...")
        engine = EnhancedStagingAutomationEngine(config)
        
        print("2️⃣ Initializing engine (this will login and navigate to task register)...")
        success = await engine.initialize()
        
        if not success:
            print("❌ Engine initialization failed")
            return False
        
        print("✅ Engine initialized successfully")
        
        print("\n3️⃣ Testing form filling with test record...")
        test_record = create_test_record()
        
        print(f"📋 Test Record Details:")
        print(f"   Employee: {test_record.employee_name}")
        print(f"   Date: {test_record.date}")
        print(f"   Charge Job: {test_record.raw_charge_job}")
        
        # Test the form filling directly
        try:
            # Make sure we're on task register page
            print("\n4️⃣ Ensuring we're on task register page...")
            await engine._navigate_to_task_register_robust()
            
            print("5️⃣ Testing form filling...")
            await engine._fill_form_robust(test_record)
            
            print("✅ FORM FILLING SUCCESSFUL!")
            print("6️⃣ Testing form submission...")
            
            await engine._submit_form_robust()
            print("✅ FORM SUBMISSION SUCCESSFUL!")
            
            print("7️⃣ Verifying submission...")
            await engine._verify_submission()
            print("✅ SUBMISSION VERIFICATION SUCCESSFUL!")
            
            return True
            
        except Exception as e:
            print(f"❌ Form filling/submission failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_charge_job_parsing():
    """Test charge job parsing specifically"""
    print("\n" + "="*70)
    print("🧪 TESTING CHARGE JOB PARSING")
    print("="*70)
    
    engine = EnhancedStagingAutomationEngine(load_config())
    
    test_cases = [
        "(2209002) PR-MILL-PROD / LINE 1 / (000098) OPERASIONAL / (000007) PRODUKSI",
        "(2209001) PR-MILL-QC / LINE 2 / (000099) QUALITY / (000008) INSPEKSI",
        "(2209003) PR-MILL-MAINT / WORKSHOP / (000100) MAINTENANCE / (000009) REPAIR"
    ]
    
    for i, raw_charge_job in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ Testing: {raw_charge_job}")
        
        parts = engine.parse_charge_job(raw_charge_job)
        
        if len(parts) >= 4:
            print(f"✅ Parsed successfully:")
            print(f"   Task Code: {parts[0]}")
            print(f"   Location: {parts[1]}")
            print(f"   Sub Location: {parts[2]}")
            print(f"   Type: {parts[3]}")
        else:
            print(f"❌ Parsing failed - got {len(parts)} parts: {parts}")
    
    return True


async def main():
    """Main test function"""
    print("🧪 AUTOMATIC FORM FILLING TEST")
    print("=" * 80)
    
    setup_logging()
    
    # Test 1: Charge job parsing
    parsing_test_passed = await test_charge_job_parsing()
    
    # Test 2: Full form filling
    print("\n" + "⏳" + "="*60)
    print("⏳ STARTING FULL FORM FILLING TEST...")
    print("   This will login, navigate to task register, and fill the form")
    print("   Watch the browser window to see the filling process")
    print("="*62)
    
    filling_test_passed = await test_form_filling()
    
    # Summary
    print("\n" + "="*70)
    print("📋 TEST SUMMARY")
    print("="*70)
    print(f"Charge Job Parsing: {'✅ PASSED' if parsing_test_passed else '❌ FAILED'}")
    print(f"Form Filling: {'✅ PASSED' if filling_test_passed else '❌ FAILED'}")
    
    if parsing_test_passed and filling_test_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Charge job parsing is working correctly")
        print("✅ Form filling and submission completed successfully")
        print("✅ The automatic filling system is functional")
        
        print("\n📝 What this means:")
        print("  • The system can correctly parse charge job strings")
        print("  • Form fields are being filled properly")
        print("  • Form submission is working")
        print("  • The full automation pipeline should work in the main system")
        
    else:
        print("\n❌ SOME TESTS FAILED")
        if not parsing_test_passed:
            print("  ❌ Charge job parsing needs fixing")
        if not filling_test_passed:
            print("  ❌ Form filling/submission has issues")
            print("  💡 This is likely the reason automation is not working")
    
    # Keep browser open for manual inspection
    print("\n🔍 BROWSER WINDOW LEFT OPEN FOR INSPECTION")
    print("   Check the current state of the form")
    print("   Press ENTER to close and cleanup...")
    input()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        import traceback
        traceback.print_exc() 