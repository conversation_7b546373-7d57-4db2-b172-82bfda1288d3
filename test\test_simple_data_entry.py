#!/usr/bin/env python3
"""
SIMPLE DATA ENTRY TEST

This test focuses on entering the first staging record data into the form,
using the actual HTML structure and working around stale element issues.
"""

import asyncio
import requests
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import StaleElementReferenceException
import time

def get_staging_data():
    """Get the first staging record from the API"""
    try:
        print("🔍 Fetching staging data from localhost:5173/api/staging/data...")
        response = requests.get("http://localhost:5173/api/staging/data", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data and len(data) > 0:
                first_record = data[0]
                print(f"✅ Found {len(data)} staging records")
                print(f"📝 First record: ID={first_record.get('id', 'N/A')}")
                print(f"   Employee: {first_record.get('employee_name', 'N/A')}")
                print(f"   Date: {first_record.get('date', 'N/A')}")
                print(f"   Raw Job: {first_record.get('raw_charge_job', 'N/A')[:60]}...")
                return first_record
            else:
                print("❌ No staging data found")
                return None
        else:
            print(f"❌ Failed to fetch staging data: HTTP {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error fetching staging data: {e}")
        return None

def create_test_driver():
    """Create a Chrome driver for testing"""
    try:
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        service = Service(ChromeDriverManager().install())
        
        options = webdriver.ChromeOptions()
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        driver = webdriver.Chrome(service=service, options=options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return driver
    except Exception as e:
        print(f"❌ Failed to create driver: {e}")
        return None

def navigate_to_form(driver):
    """Navigate to the task register form"""
    try:
        print("🚀 Navigating to millware system...")
        driver.get("http://millwarep3:8004/")
        
        # Wait for login page
        time.sleep(3)
        
        # Login (assuming ADM075/ADM075)
        username_field = driver.find_element(By.ID, "MainContent_txtUserName")
        password_field = driver.find_element(By.ID, "MainContent_txtPassword")
        
        username_field.clear()
        username_field.send_keys("adm075")
        
        password_field.clear()
        password_field.send_keys("adm075")
        
        login_button = driver.find_element(By.ID, "MainContent_btnLogin")
        login_button.click()
        
        print("✅ Login submitted, waiting for response...")
        time.sleep(5)
        
        # Handle any popups
        try:
            popup_button = driver.find_element(By.ID, "MainContent_btnOkay")
            popup_button.click()
            print("✅ Popup dismissed")
            time.sleep(2)
        except:
            print("ℹ️ No popup found")
        
        # Check current URL and navigate to task register if needed
        current_url = driver.current_url
        print(f"📍 Current URL: {current_url}")
        
        if "frmPrTrxTaskRegisterDet.aspx" not in current_url:
            print("🔄 Navigating to task register...")
            driver.get("http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx")
            time.sleep(3)
        
        print("✅ Successfully navigated to task register form")
        return True
        
    except Exception as e:
        print(f"❌ Navigation failed: {e}")
        return False

def fill_date_field_simple(driver, date_value):
    """Fill date field using JavaScript to avoid stale element issues"""
    try:
        print(f"📅 Filling date field: {date_value}")
        
        # Convert date format YYYY-MM-DD to DD/MM/YYYY
        if '-' in date_value:
            year, month, day = date_value.split('-')
            formatted_date = f"{day}/{month}/{year}"
        else:
            formatted_date = date_value
        
        print(f"📅 Formatted date: {formatted_date}")
        
        # Use JavaScript to set the date value directly
        script = f"""
        var dateField = document.getElementById('MainContent_txtTrxDate');
        if (dateField) {{
            dateField.value = '{formatted_date}';
            dateField.dispatchEvent(new Event('change', {{ bubbles: true }}));
            return 'SUCCESS';
        }} else {{
            return 'FIELD_NOT_FOUND';
        }}
        """
        
        result = driver.execute_script(script)
        
        if result == 'SUCCESS':
            print("✅ Date field filled successfully via JavaScript")
            return True
        else:
            print(f"❌ Date field filling failed: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Date field error: {e}")
        return False

def fill_autocomplete_field(driver, field_index, value, field_name):
    """Fill autocomplete field by index"""
    try:
        print(f"🔧 Filling {field_name} (index {field_index}): {value}")
        
        # Wait a moment for any dynamic loading
        time.sleep(1)
        
        # Find all autocomplete fields
        autocomplete_fields = driver.find_elements(
            By.CSS_SELECTOR, 
            "input.ui-autocomplete-input.ui-widget.ui-widget-content"
        )
        
        # Filter displayed and enabled fields
        available_fields = []
        for field in autocomplete_fields:
            try:
                if field.is_displayed() and field.is_enabled():
                    available_fields.append(field)
            except:
                continue
        
        print(f"📊 Found {len(available_fields)} available autocomplete fields")
        
        if field_index < len(available_fields):
            target_field = available_fields[field_index]
            
            # Use JavaScript to fill the field
            script = f"""
            arguments[0].value = '{value}';
            arguments[0].dispatchEvent(new Event('input', {{ bubbles: true }}));
            arguments[0].dispatchEvent(new Event('change', {{ bubbles: true }}));
            return 'SUCCESS';
            """
            
            result = driver.execute_script(script, target_field)
            
            if result == 'SUCCESS':
                print(f"✅ {field_name} filled successfully")
                time.sleep(1.5)  # Wait for autocomplete/dynamic loading
                return True
            else:
                print(f"❌ {field_name} JavaScript filling failed")
                return False
        else:
            print(f"⚠️ {field_name} field index {field_index} not available")
            return False
            
    except Exception as e:
        print(f"❌ {field_name} filling error: {e}")
        return False

def parse_charge_job(raw_charge_job):
    """Parse raw charge job into components"""
    try:
        # Remove parentheses content and split by " / "
        parts = raw_charge_job.split(' / ')
        
        if len(parts) >= 4:
            # Extract the code from parentheses in first part
            task_code = parts[0].split(')')[0].replace('(', '').strip()
            
            # Station code from second part
            station_code = parts[1].split('(')[0].strip()
            
            # Machine code from third part  
            machine_code = parts[2].split('(')[0].strip()
            
            # Expense code from fourth part
            expense_code = parts[3].split('(')[0].strip()
            
            return [task_code, station_code, machine_code, expense_code]
        else:
            print(f"⚠️ Unexpected charge job format: {raw_charge_job}")
            return []
            
    except Exception as e:
        print(f"❌ Charge job parsing error: {e}")
        return []

def submit_form(driver):
    """Submit the form using the Add button"""
    try:
        print("📤 Submitting form...")
        
        # Find and click the Add button
        add_button = driver.find_element(By.ID, "MainContent_btnAdd")
        if add_button.is_displayed() and add_button.is_enabled():
            add_button.click()
            print("✅ Add button clicked")
            time.sleep(3)  # Wait for submission processing
            return True
        else:
            print("❌ Add button not available")
            return False
            
    except Exception as e:
        print(f"❌ Form submission error: {e}")
        return False

def main():
    """Main test execution"""
    print("🧪 SIMPLE DATA ENTRY TEST")
    print("=" * 50)
    
    # Get staging data
    staging_data = get_staging_data()
    if not staging_data:
        print("❌ No staging data available for testing")
        return False
    
    # Create driver
    driver = create_test_driver()
    if not driver:
        print("❌ Failed to create WebDriver")
        return False
    
    try:
        # Navigate to form
        if not navigate_to_form(driver):
            print("❌ Failed to navigate to form")
            return False
        
        print("\n📝 FILLING FORM DATA")
        print("-" * 30)
        
        # Fill date field
        date_success = fill_date_field_simple(driver, staging_data.get('date', ''))
        
        # Fill employee field (index 0 - first autocomplete field)
        employee_success = fill_autocomplete_field(
            driver, 0, staging_data.get('employee_name', ''), "Employee"
        )
        
        # Parse and fill charge job fields
        charge_job_parts = parse_charge_job(staging_data.get('raw_charge_job', ''))
        
        if charge_job_parts:
            print(f"📊 Parsed charge job into {len(charge_job_parts)} parts:")
            for i, part in enumerate(charge_job_parts, 1):
                print(f"   {i}. {part}")
            
            # Fill task code (index 1 - second autocomplete field)
            task_success = fill_autocomplete_field(driver, 1, charge_job_parts[0], "Task Code")
            
            # Try to fill other fields (they may appear dynamically)
            station_success = fill_autocomplete_field(driver, 2, charge_job_parts[1], "Station Code")
            machine_success = fill_autocomplete_field(driver, 3, charge_job_parts[2], "Machine Code")
            expense_success = fill_autocomplete_field(driver, 4, charge_job_parts[3], "Expense Code")
        
        # Submit form
        submission_success = submit_form(driver)
        
        print("\n📋 RESULTS SUMMARY")
        print("-" * 30)
        print(f"📅 Date Field: {'✅ Success' if date_success else '❌ Failed'}")
        print(f"👤 Employee Field: {'✅ Success' if employee_success else '❌ Failed'}")
        print(f"🔧 Task Code: {'✅ Success' if 'task_success' in locals() and task_success else '❌ Failed'}")
        print(f"🏢 Station Code: {'✅ Success' if 'station_success' in locals() and station_success else '❌ Failed'}")
        print(f"🔩 Machine Code: {'✅ Success' if 'machine_success' in locals() and machine_success else '❌ Failed'}")
        print(f"💰 Expense Code: {'✅ Success' if 'expense_success' in locals() and expense_success else '❌ Failed'}")
        print(f"📤 Submission: {'✅ Success' if submission_success else '❌ Failed'}")
        
        print(f"\n📍 Final URL: {driver.current_url}")
        
        # Keep browser open for manual verification
        input("\n🔍 Press Enter to close browser and finish test...")
        
        return True
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return False
        
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 SIMPLE DATA ENTRY TEST COMPLETED")
    else:
        print("\n❌ SIMPLE DATA ENTRY TEST FAILED") 