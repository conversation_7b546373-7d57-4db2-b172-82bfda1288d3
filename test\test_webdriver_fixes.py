#!/usr/bin/env python3
"""
Test script to verify WebDriver automation fixes
Tests network connectivity, WebDriver creation, and automation workflow
"""

import sys
import json
import logging
import asyncio
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def setup_logging():
    """Setup logging for test"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('test_webdriver_fixes.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def load_test_config():
    """Load test configuration"""
    config = {
        "browser": {
            "headless": False,
            "window_size": [1280, 720],
            "disable_notifications": True,
            "event_delay": 0.5
        },
        "automation": {
            "implicit_wait": 10,
            "page_load_timeout": 30,
            "script_timeout": 30,
            "max_retries": 3
        },
        "credentials": {
            "username": "adm075",
            "password": "adm075"
        },
        "urls": {
            "login": "http://millwarep3:8004/",
            "taskRegister": "http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx"
        }
    }
    return config

def test_network_connectivity():
    """Test network connectivity to target server"""
    print("\n" + "="*60)
    print("🌐 TESTING NETWORK CONNECTIVITY")
    print("="*60)
    
    try:
        import socket
        from urllib.parse import urlparse
        
        # Test connectivity to millwarep3:8004
        hostname = "millwarep3"
        port = 8004
        
        print(f"Testing connection to {hostname}:{port}...")
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((hostname, port))
        sock.close()
        
        if result == 0:
            print(f"✅ Network connectivity to {hostname}:{port} - SUCCESS")
            return True
        else:
            print(f"❌ Network connectivity to {hostname}:{port} - FAILED (code: {result})")
            print(f"   Please check if {hostname} is accessible from this machine")
            return False
            
    except socket.gaierror as e:
        print(f"❌ DNS resolution failed for {hostname}: {e}")
        print(f"   Please check if {hostname} is in your hosts file or DNS")
        return False
    except Exception as e:
        print(f"❌ Network test failed: {e}")
        return False

def test_webdriver_creation():
    """Test WebDriver creation with enhanced error handling"""
    print("\n" + "="*60)
    print("🚗 TESTING WEBDRIVER CREATION")
    print("="*60)
    
    try:
        from core.browser_manager import BrowserManager
        
        config = load_test_config()
        browser_manager = BrowserManager(config.get('browser', {}))
        
        print("Creating WebDriver with enhanced error handling...")
        driver = browser_manager.create_driver()
        
        if driver:
            print("✅ WebDriver created successfully")
            
            # Test basic functionality
            print("Testing WebDriver responsiveness...")
            current_url = driver.current_url
            print(f"   Current URL: {current_url}")
            
            # Test navigation to a simple page
            print("Testing navigation...")
            driver.get("data:text/html,<html><body><h1>Test Page</h1></body></html>")
            time.sleep(1)
            
            title = driver.title
            print(f"   Page title: {title}")
            
            # Cleanup
            driver.quit()
            print("✅ WebDriver test completed successfully")
            return True
        else:
            print("❌ WebDriver creation failed")
            return False
            
    except Exception as e:
        print(f"❌ WebDriver creation test failed: {e}")
        return False

def test_persistent_browser_manager():
    """Test persistent browser manager initialization"""
    print("\n" + "="*60)
    print("🔄 TESTING PERSISTENT BROWSER MANAGER")
    print("="*60)
    
    try:
        from core.persistent_browser_manager import PersistentBrowserManager
        
        config = load_test_config()
        browser_manager = PersistentBrowserManager(config)
        
        print("Initializing persistent browser manager...")
        
        # Run async initialization
        async def test_init():
            success = await browser_manager.initialize()
            return success
        
        # Run the test
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            success = loop.run_until_complete(test_init())
            
            if success:
                print("✅ Persistent browser manager initialized successfully")
                
                # Test driver retrieval
                driver = browser_manager.get_driver()
                if driver:
                    print("✅ WebDriver retrieved successfully")
                    current_url = driver.current_url
                    print(f"   Current URL: {current_url}")
                else:
                    print("⚠️ WebDriver not available")
                
                # Cleanup
                await browser_manager.cleanup()
                print("✅ Cleanup completed")
                return True
            else:
                print("❌ Persistent browser manager initialization failed")
                return False
                
        finally:
            loop.close()
            
    except Exception as e:
        print(f"❌ Persistent browser manager test failed: {e}")
        return False

def test_automation_service():
    """Test automation service initialization"""
    print("\n" + "="*60)
    print("🤖 TESTING AUTOMATION SERVICE")
    print("="*60)
    
    try:
        from automation_service import get_automation_service
        
        config = load_test_config()
        
        print("Creating automation service...")
        automation_service = get_automation_service(config)
        
        if automation_service:
            print("✅ Automation service created successfully")
            
            # Wait for initialization
            print("Waiting for engine initialization...")
            max_wait = 30  # seconds
            start_time = time.time()
            
            while not automation_service.is_engine_initialized and (time.time() - start_time) < max_wait:
                elapsed = int(time.time() - start_time)
                print(f"   Waiting... ({elapsed}s/{max_wait}s)")
                time.sleep(2)
            
            if automation_service.is_engine_initialized:
                print("✅ Automation engine initialized successfully")
                
                # Test engine status
                status = automation_service.get_engine_status()
                print(f"   Engine status: {status}")
                
                return True
            else:
                print("⚠️ Automation engine initialization timeout")
                print("   This is expected if network connectivity failed")
                return False
        else:
            print("❌ Automation service creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Automation service test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 WEBDRIVER AUTOMATION FIXES TEST")
    print("="*60)
    
    setup_logging()
    
    # Run tests
    tests = [
        ("Network Connectivity", test_network_connectivity),
        ("WebDriver Creation", test_webdriver_creation),
        ("Persistent Browser Manager", test_persistent_browser_manager),
        ("Automation Service", test_automation_service)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running test: {test_name}")
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST RESULTS SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! WebDriver automation should work correctly.")
    else:
        print("⚠️ Some tests failed. Check the logs for details.")
        print("\nTroubleshooting tips:")
        if not results.get("Network Connectivity", False):
            print("- Check network connection to millwarep3:8004")
            print("- Add millwarep3 to your hosts file if needed")
        if not results.get("WebDriver Creation", False):
            print("- Update Chrome browser to latest version")
            print("- Clear WebDriver cache: rm -rf ~/.wdm")

if __name__ == "__main__":
    main()
