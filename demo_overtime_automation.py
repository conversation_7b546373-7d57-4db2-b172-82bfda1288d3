"""
Demo Overtime Automation System
Tests the new overtime handling functionality with sample data
"""

import asyncio
import logging
from src.core.api_data_automation import APIDataAutomation
from src.core.persistent_browser_manager import PersistentBrowserManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('overtime_automation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# Sample data with overtime hours
SAMPLE_OVERTIME_RECORDS = [
    {
        "check_in": "08:00",
        "check_out": "17:00",
        "created_at": "2025-06-10 04:43:03",
        "date": "2025-05-18",
        "day_of_week": "Sen",
        "employee_id": "PTRJ.241000001",
        "employee_name": "Ade Prasetya",
        "expense_code": "L (LABOUR)",
        "id": "394c7045-20cd-4e56-94e6-c4c168162177",
        "machine_code": "BLR00000 (LABOUR COST)",
        "notes": "Transferred from Monthly Grid - May 2025",
        "overtime_hours": 16.0,  # This should create 2 entries
        "raw_charge_job": "(OC7190) BOILER OPERATION / STN-BLR (STATION BOILER) / BLR00000 (LABOUR COST) / L (LABOUR)",
        "regular_hours": 7.0,
        "shift": "Regular",
        "source_record_id": "",
        "station_code": "STN-BLR (STATION BOILER)",
        "status": "staged",
        "task_code": "(OC7190) BOILER OPERATION",
        "total_hours": 23.0,
        "transfer_status": "success",
        "updated_at": "2025-06-10 04:43:03"
    },
    {
        "check_in": "08:00",
        "check_out": "17:00",
        "created_at": "2025-06-10 04:43:03",
        "date": "2025-05-19",
        "day_of_week": "Sel",
        "employee_id": "PTRJ.241000002",
        "employee_name": "Budi Santoso",
        "expense_code": "M (MATERIAL)",
        "id": "d2f696c5-e599-5431-cdf4-9e1f1dg63171",
        "machine_code": "TUR00001 (TURBINE COST)",
        "notes": "Transferred from Monthly Grid - May 2025",
        "overtime_hours": 0.0,  # This should create 1 entry (normal only)
        "raw_charge_job": "(OC7191) TURBINE MAINTENANCE / STN-TUR (STATION TURBINE) / TUR00001 (TURBINE COST) / M (MATERIAL)",
        "regular_hours": 8.0,
        "shift": "Regular",
        "source_record_id": "",
        "station_code": "STN-TUR (STATION TURBINE)",
        "status": "staged",
        "task_code": "(OC7191) TURBINE MAINTENANCE",
        "total_hours": 8.0,
        "transfer_status": "success",
        "updated_at": "2025-06-10 04:43:03"
    },
    {
        "check_in": "18:00",
        "check_out": "22:00",
        "created_at": "2025-06-10 04:43:03",
        "date": "2025-05-20",
        "day_of_week": "Rab", 
        "employee_id": "PTRJ.241000003",
        "employee_name": "Citra Dewi",
        "expense_code": "L (LABOUR)",
        "id": "a8b123c4-d567-8910-ef11-2b3c4d5e6f78",
        "machine_code": "LAB00000 (LABOUR COST)",
        "notes": "Overtime shift only",
        "overtime_hours": 4.0,  # Only overtime, no regular hours
        "raw_charge_job": "(OC7240) LABORATORY ANALYSIS / STN-LAB (STATION LABORATORY) / LAB00000 (LABOUR COST) / L (LABOUR)",
        "regular_hours": 0.0,
        "shift": "Night",
        "source_record_id": "",
        "station_code": "STN-LAB (STATION LABORATORY)",
        "status": "staged",
        "task_code": "(OC7240) LABORATORY ANALYSIS",
        "total_hours": 4.0,
        "transfer_status": "success",
        "updated_at": "2025-06-10 04:43:03"
    }
]

async def test_overtime_entries():
    """Test the create_overtime_entries function"""
    print("\n" + "="*60)
    print("TESTING OVERTIME ENTRIES CREATION")
    print("="*60)
    
    automation = APIDataAutomation()
    
    for i, record in enumerate(SAMPLE_OVERTIME_RECORDS, 1):
        print(f"\n--- Record {i}: {record['employee_name']} ---")
        print(f"Regular Hours: {record['regular_hours']}")
        print(f"Overtime Hours: {record['overtime_hours']}")
        print(f"Total Hours: {record['total_hours']}")
        
        entries = automation.create_overtime_entries(record)
        
        print(f"Created {len(entries)} entries:")
        for j, entry in enumerate(entries, 1):
            print(f"  Entry {j}: {entry['transaction_type']} - {entry['hours']} hours")

async def test_full_automation():
    """Test full automation with overtime handling"""
    print("\n" + "="*60)
    print("TESTING FULL OVERTIME AUTOMATION")
    print("="*60)
    
    # Initialize browser manager with config
    config = {
        'browser': {
            'headless': False,
            'timeout': 30
        }
    }
    browser_manager = PersistentBrowserManager(config)
    
    try:
        # Initialize browser
        if not await browser_manager.initialize():
            print("❌ Failed to initialize browser")
            return False
        
        print("✅ Browser initialized successfully")
        
        # Initialize automation
        automation = APIDataAutomation()
        automation.browser_manager = browser_manager
        
        # Override fetch_staging_data to use sample data
        async def mock_fetch_staging_data():
            return SAMPLE_OVERTIME_RECORDS
        
        automation.fetch_staging_data = mock_fetch_staging_data
        
        print("\n🚀 Starting overtime automation test...")
        
        # Run automation
        result = await automation.run_api_automation()
        
        if result:
            print("✅ Overtime automation test completed successfully!")
        else:
            print("❌ Overtime automation test failed!")
        
        return result
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    finally:
        await browser_manager.cleanup()

async def main():
    """Main demo function"""
    print("VENUS AUTOFILL - OVERTIME AUTOMATION DEMO")
    print("="*50)
    
    # Test 1: Overtime entries creation
    await test_overtime_entries()
    
    print("\n" + "="*60)
    choice = input("Do you want to test full automation? (y/n): ").lower().strip()
    
    if choice == 'y':
        # Test 2: Full automation
        success = await test_full_automation()
        
        if success:
            print("\n🎉 All tests passed!")
        else:
            print("\n⚠️ Some tests failed. Check logs for details.")
    else:
        print("\n✅ Overtime entries test completed!")

if __name__ == "__main__":
    asyncio.run(main()) 