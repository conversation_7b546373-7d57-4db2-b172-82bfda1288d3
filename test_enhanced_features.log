2025-06-23 00:52:27,390 - run_user_controlled_automation - INFO - 📅 Testing mode: Current date 23/06/2025 → Document date 23/05/2025 (-1 month)
2025-06-23 00:52:27,410 - run_user_controlled_automation - INFO - 📅 Real mode: Using current date as document date → 23/06/2025
2025-06-23 00:52:27,412 - run_user_controlled_automation - INFO - 🔗 Testing mode: Using port 8004 → http://millwarep3.rebinmas.com:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:52:27,414 - run_user_controlled_automation - INFO - 🔗 Real mode: Using port 8003 → http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:52:27,418 - run_user_controlled_automation - INFO - 📅 Weekday detected: Using 7 hours for regular entry
2025-06-23 00:52:27,421 - run_user_controlled_automation - INFO - ⏰ Overtime hours from API: 2
2025-06-23 00:52:27,425 - run_user_controlled_automation - INFO - 📅 Saturday detected: Using 5 hours for regular entry
2025-06-23 00:52:27,425 - run_user_controlled_automation - INFO - ⏰ Overtime hours from API: 0
2025-06-23 00:52:27,428 - run_user_controlled_automation - INFO - ⚠️ Regular hours is 0, using API data: 0
2025-06-23 00:52:27,430 - run_user_controlled_automation - INFO - ⏰ Overtime hours from API: 3
2025-06-23 00:52:27,432 - run_user_controlled_automation - INFO - 🔗 Real mode: Using port 8003 → http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:52:27,433 - run_user_controlled_automation - INFO - 📅 Real mode: Using current date as document date → 23/06/2025
2025-06-23 00:52:27,435 - run_user_controlled_automation - INFO - 🔗 Testing mode: Using port 8004 → http://millwarep3.rebinmas.com:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:52:27,435 - run_user_controlled_automation - INFO - 📅 Testing mode: Current date 23/06/2025 → Document date 23/05/2025 (-1 month)
2025-06-23 08:24:12,589 - run_user_controlled_automation - INFO - 📅 Testing mode: Attendance date 15/01/2024 → Document date 15/12/2023 (-1 month)
2025-06-23 08:24:12,589 - run_user_controlled_automation - INFO - 📅 Real mode: Using current date as document date → 23/06/2025
2025-06-23 08:24:12,605 - run_user_controlled_automation - INFO - 🔗 Testing mode: Using port 8004 → http://millwarep3.rebinmas.com:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:24:12,605 - run_user_controlled_automation - INFO - 🔗 Real mode: Using port 8003 → http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:24:12,610 - run_user_controlled_automation - INFO - 📅 Weekday detected: Using 7 hours for regular entry
2025-06-23 08:24:12,610 - run_user_controlled_automation - INFO - ⏰ Overtime hours from API: 2
2025-06-23 08:24:12,613 - run_user_controlled_automation - INFO - 📅 Saturday detected: Using 5 hours for regular entry
2025-06-23 08:24:12,613 - run_user_controlled_automation - INFO - ⏰ Overtime hours from API: 0
2025-06-23 08:24:12,615 - run_user_controlled_automation - INFO - ⚠️ Regular hours is 0, using API data: 0
2025-06-23 08:24:12,616 - run_user_controlled_automation - INFO - ⏰ Overtime hours from API: 3
2025-06-23 08:24:12,619 - run_user_controlled_automation - INFO - 🔗 Real mode: Using port 8003 → http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:24:12,620 - run_user_controlled_automation - INFO - 📅 Real mode: Using current date as document date → 23/06/2025
2025-06-23 08:24:12,620 - run_user_controlled_automation - INFO - 🔗 Testing mode: Using port 8004 → http://millwarep3.rebinmas.com:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:24:12,621 - run_user_controlled_automation - INFO - 📅 Testing mode: Attendance date 15/01/2024 → Document date 15/12/2023 (-1 month)
