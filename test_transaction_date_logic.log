2025-06-23 08:20:49,853 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 15/01/2024 → Transaction date 15/12/2023 (-1 month)
2025-06-23 08:20:49,862 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 15/01/2024 → Transaction date 15/12/2023 (-1 month)
2025-06-23 08:20:49,862 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 15/01/2024
2025-06-23 08:20:49,862 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 15/01/2024
2025-06-23 08:20:49,869 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 31/01/2024 → Transaction date 31/12/2023 (-1 month)
2025-06-23 08:20:49,869 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 31/01/2024
2025-06-23 08:20:49,869 - run_user_controlled_automation - INFO - 📅 Testing mode: Current date 23/06/2025 → Document date 23/05/2025 (-1 month)
2025-06-23 08:20:49,869 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 15/01/2024 → Transaction date 15/12/2023 (-1 month)
2025-06-23 08:20:49,878 - run_user_controlled_automation - INFO - 📅 Testing mode: Current date 23/06/2025 → Document date 23/05/2025 (-1 month)
2025-06-23 08:20:49,878 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 29/02/2024 → Transaction date 29/01/2024 (-1 month)
2025-06-23 08:20:49,878 - run_user_controlled_automation - INFO - 📅 Testing mode: Current date 23/06/2025 → Document date 23/05/2025 (-1 month)
2025-06-23 08:20:49,878 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 31/03/2024 → Transaction date 29/02/2024 (-1 month)
2025-06-23 08:20:49,878 - run_user_controlled_automation - INFO - 📅 Testing mode: Current date 23/06/2025 → Document date 23/05/2025 (-1 month)
2025-06-23 08:20:49,878 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 15/06/2024 → Transaction date 15/05/2024 (-1 month)
2025-06-23 08:20:49,886 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 15/01/2024
2025-06-23 08:20:49,886 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 15/01/2024
2025-06-23 08:20:49,889 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 25/12/2024
2025-06-23 08:20:49,889 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 29/02/2024
2025-06-23 08:20:49,893 - run_user_controlled_automation - ERROR - ❌ Error calculating transaction date: time data 'invalid-date' does not match format '%Y-%m-%d'
2025-06-23 08:20:49,894 - run_user_controlled_automation - ERROR - ❌ Error calculating transaction date: time data '' does not match format '%Y-%m-%d'
2025-06-23 08:20:49,894 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 01/01/2024 → Transaction date 01/12/2023 (-1 month)
2025-06-23 08:20:49,894 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 29/02/2024 → Transaction date 29/01/2024 (-1 month)
2025-06-23 08:22:39,656 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 15/01/2024 → Transaction date 15/12/2023 (-1 month)
2025-06-23 08:22:39,659 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 15/01/2024 → Transaction date 15/12/2023 (-1 month)
2025-06-23 08:22:39,662 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 15/01/2024
2025-06-23 08:22:39,664 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 15/01/2024
2025-06-23 08:22:39,666 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 31/01/2024 → Transaction date 31/12/2023 (-1 month)
2025-06-23 08:22:39,669 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 31/01/2024
2025-06-23 08:22:39,671 - run_user_controlled_automation - INFO - 📅 Testing mode: Attendance date 15/01/2024 → Document date 15/12/2023 (-1 month)
2025-06-23 08:22:39,672 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 15/01/2024 → Transaction date 15/12/2023 (-1 month)
2025-06-23 08:22:39,673 - run_user_controlled_automation - INFO - 📅 Testing mode: Attendance date 29/02/2024 → Document date 29/01/2024 (-1 month)
2025-06-23 08:22:39,675 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 29/02/2024 → Transaction date 29/01/2024 (-1 month)
2025-06-23 08:22:39,675 - run_user_controlled_automation - INFO - 📅 Testing mode: Attendance date 31/03/2024 → Document date 29/02/2024 (-1 month)
2025-06-23 08:22:39,675 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 31/03/2024 → Transaction date 29/02/2024 (-1 month)
2025-06-23 08:22:39,675 - run_user_controlled_automation - INFO - 📅 Testing mode: Attendance date 15/06/2024 → Document date 15/05/2024 (-1 month)
2025-06-23 08:22:39,680 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 15/06/2024 → Transaction date 15/05/2024 (-1 month)
2025-06-23 08:22:39,683 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 15/01/2024
2025-06-23 08:22:39,685 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 15/01/2024
2025-06-23 08:22:39,686 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 25/12/2024
2025-06-23 08:22:39,687 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 29/02/2024
2025-06-23 08:22:39,690 - run_user_controlled_automation - ERROR - ❌ Error calculating transaction date: time data 'invalid-date' does not match format '%Y-%m-%d'
2025-06-23 08:22:39,690 - run_user_controlled_automation - ERROR - ❌ Error calculating transaction date: time data '' does not match format '%Y-%m-%d'
2025-06-23 08:22:39,692 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 01/01/2024 → Transaction date 01/12/2023 (-1 month)
2025-06-23 08:22:39,692 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 29/02/2024 → Transaction date 29/01/2024 (-1 month)
2025-06-23 08:30:24,019 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 15/01/2024 → Transaction date 15/12/2023 (-1 month)
2025-06-23 08:30:24,028 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 15/01/2024 → Transaction date 15/12/2023 (-1 month)
2025-06-23 08:30:24,028 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 15/01/2024
2025-06-23 08:30:24,028 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 15/01/2024
2025-06-23 08:30:24,035 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 31/01/2024 → Transaction date 31/12/2023 (-1 month)
2025-06-23 08:30:24,035 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 31/01/2024
2025-06-23 08:30:24,039 - run_user_controlled_automation - INFO - 📅 Testing mode: Current date 23/06/2025 → Document date 23/05/2025 (same day, -1 month)
2025-06-23 08:30:24,039 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 15/01/2024 → Transaction date 15/12/2023 (-1 month)
2025-06-23 08:30:24,041 - run_user_controlled_automation - INFO - 📅 Testing mode: Current date 23/06/2025 → Document date 23/05/2025 (same day, -1 month)
2025-06-23 08:30:24,041 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 29/02/2024 → Transaction date 29/01/2024 (-1 month)
2025-06-23 08:30:24,043 - run_user_controlled_automation - INFO - 📅 Testing mode: Current date 23/06/2025 → Document date 23/05/2025 (same day, -1 month)
2025-06-23 08:30:24,043 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 31/03/2024 → Transaction date 29/02/2024 (-1 month)
2025-06-23 08:30:24,043 - run_user_controlled_automation - INFO - 📅 Testing mode: Current date 23/06/2025 → Document date 23/05/2025 (same day, -1 month)
2025-06-23 08:30:24,043 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 15/06/2024 → Transaction date 15/05/2024 (-1 month)
2025-06-23 08:30:24,043 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 15/01/2024
2025-06-23 08:30:24,043 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 15/01/2024
2025-06-23 08:30:24,043 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 25/12/2024
2025-06-23 08:30:24,043 - run_user_controlled_automation - INFO - 📅 Real mode: Using original attendance date as transaction date → 29/02/2024
2025-06-23 08:30:24,043 - run_user_controlled_automation - ERROR - ❌ Error calculating transaction date: time data 'invalid-date' does not match format '%Y-%m-%d'
2025-06-23 08:30:24,043 - run_user_controlled_automation - ERROR - ❌ Error calculating transaction date: time data '' does not match format '%Y-%m-%d'
2025-06-23 08:30:24,043 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 01/01/2024 → Transaction date 01/12/2023 (-1 month)
2025-06-23 08:30:24,043 - run_user_controlled_automation - INFO - 📅 Testing mode: Original attendance date 29/02/2024 → Transaction date 29/01/2024 (-1 month)
