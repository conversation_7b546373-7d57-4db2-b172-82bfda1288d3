2025-06-21 09:49:51,391 - root - ERROR - System error occurred
Traceback (most recent call last):
  File "C:\Gawean Rebinmas\Selenium Auto Fill\Selenium Auto Fill\run_user_controlled_automation.py", line 157, in main
    from automation_service import get_automation_service
  File "C:\Gawean Rebinmas\Selenium Auto Fill\Selenium Auto Fill\src\automation_service.py", line 15, in <module>
    from core.enhanced_staging_automation import EnhancedStagingAutomationEngine, AutomationResult
  File "C:\Gawean Rebinmas\Selenium Auto Fill\Selenium Auto Fill\src\core\enhanced_staging_automation.py", line 427
    try:
    ^^^
IndentationError: expected an indented block after 'for' statement on line 426
2025-06-21 09:52:27,157 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-21 09:52:27,163 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-21 09:52:27,164 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-21 09:52:27,164 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-21 09:52:27,440 - core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-21 09:52:27,693 - core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-21 09:52:27,693 - core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-21 09:52:27,711 - WDM - INFO - ====== WebDriver manager ======
2025-06-21 09:52:28,879 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-21 09:52:28,879 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-21 09:52:30,106 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-21 09:52:30,448 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-21 09:52:30,712 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-21 09:52:30,728 - core.browser_manager - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-21 09:52:30,733 - core.browser_manager - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-21 09:52:30,737 - core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-21 09:52:30,737 - core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-21 09:52:32,706 - core.browser_manager - INFO - ✅ WebDriver created and responsive
2025-06-21 09:52:33,046 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-21 09:52:33,058 - core.persistent_browser_manager - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-21 09:52:37,112 - core.persistent_browser_manager - INFO - WebDriver created and page loaded successfully
2025-06-21 09:52:37,112 - core.persistent_browser_manager - INFO - Performing initial login...
2025-06-21 09:52:49,993 - core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-21 09:52:52,004 - core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-21 09:52:52,004 - core.persistent_browser_manager - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-21 09:52:52,004 - core.persistent_browser_manager - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 09:52:54,663 - core.persistent_browser_manager - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 09:52:54,665 - core.persistent_browser_manager - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-21 09:52:54,693 - core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-21 09:52:54,695 - core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-21 09:52:54,697 - core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-21 09:52:54,718 - core.enhanced_staging_automation - INFO - ✅ Enhanced Staging Automation Engine initialized successfully
2025-06-21 09:52:54,763 - automation_service - INFO - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 09:52:54,766 - automation_service - INFO - Verifying form readiness...
2025-06-21 09:52:54,766 - core.enhanced_staging_automation - INFO - 🔍 Starting enhanced form readiness detection...
2025-06-21 09:52:54,917 - core.enhanced_staging_automation - INFO - ✅ Found date field (MainContent_txtTrxDate): ready for input
2025-06-21 09:52:55,073 - core.enhanced_staging_automation - INFO - ✅ Found autocomplete field 1: ready for input
2025-06-21 09:52:55,163 - core.enhanced_staging_automation - INFO - ✅ Found autocomplete field 2: ready for input
2025-06-21 09:52:55,163 - core.enhanced_staging_automation - INFO - 📊 Found 2 autocomplete fields ready
2025-06-21 09:52:55,163 - core.enhanced_staging_automation - INFO - 📊 Form readiness summary: 3 elements ready
2025-06-21 09:52:55,171 - core.enhanced_staging_automation - INFO - ✅ Form is ready for data entry
2025-06-21 09:52:56,177 - automation_service - INFO - ✅ Automation engine is in ready state
2025-06-21 09:52:56,177 - automation_service - INFO - ✅ Automation engine pre-initialized successfully
2025-06-21 09:52:57,804 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 09:52:57] "GET / HTTP/1.1" 200 -
2025-06-21 09:52:59,322 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-21 09:52:59,323 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-21 09:53:02,766 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 09:53:02] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-21 09:53:04,690 - data_interface.app - INFO - Successfully fetched 51 staging records
2025-06-21 09:53:04,694 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 09:53:04] "GET /api/employees HTTP/1.1" 200 -
2025-06-21 09:53:06,026 - data_interface.app - INFO - Successfully fetched 50 staging records
2025-06-21 09:53:06,032 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 09:53:06] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-21 09:54:36,598 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-21 09:54:36,608 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-21 09:54:36,608 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-21 09:54:36,608 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-21 09:54:36,714 - core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-21 09:54:36,898 - core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-21 09:54:36,898 - core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-21 09:54:36,903 - WDM - INFO - ====== WebDriver manager ======
2025-06-21 09:54:38,222 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-21 09:54:38,222 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-21 09:54:39,388 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-21 09:54:39,624 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-21 09:54:39,873 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-21 09:54:39,877 - core.browser_manager - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-21 09:54:39,878 - core.browser_manager - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-21 09:54:39,878 - core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-21 09:54:39,883 - core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-21 09:54:41,521 - core.browser_manager - INFO - ✅ WebDriver created and responsive
2025-06-21 09:54:41,595 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-21 09:54:41,607 - core.persistent_browser_manager - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-21 09:54:44,303 - core.persistent_browser_manager - INFO - WebDriver created and page loaded successfully
2025-06-21 09:54:44,303 - core.persistent_browser_manager - INFO - Performing initial login...
2025-06-21 09:54:58,976 - core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-21 09:55:00,989 - core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-21 09:55:00,989 - core.persistent_browser_manager - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-21 09:55:00,989 - core.persistent_browser_manager - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 09:55:03,164 - core.persistent_browser_manager - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 09:55:03,167 - core.persistent_browser_manager - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-21 09:55:03,195 - core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-21 09:55:03,195 - core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-21 09:55:03,195 - core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-21 09:55:03,220 - core.enhanced_staging_automation - INFO - ✅ Enhanced Staging Automation Engine initialized successfully
2025-06-21 09:55:03,269 - automation_service - INFO - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 09:55:03,269 - automation_service - INFO - Verifying form readiness...
2025-06-21 09:55:03,269 - core.enhanced_staging_automation - INFO - 🔍 Starting enhanced form readiness detection...
2025-06-21 09:55:03,432 - core.enhanced_staging_automation - INFO - ✅ Found date field (MainContent_txtTrxDate): ready for input
2025-06-21 09:55:03,558 - core.enhanced_staging_automation - INFO - ✅ Found autocomplete field 1: ready for input
2025-06-21 09:55:03,616 - core.enhanced_staging_automation - INFO - ✅ Found autocomplete field 2: ready for input
2025-06-21 09:55:03,616 - core.enhanced_staging_automation - INFO - 📊 Found 2 autocomplete fields ready
2025-06-21 09:55:03,616 - core.enhanced_staging_automation - INFO - 📊 Form readiness summary: 3 elements ready
2025-06-21 09:55:03,616 - core.enhanced_staging_automation - INFO - ✅ Form is ready for data entry
2025-06-21 09:55:04,629 - automation_service - INFO - ✅ Automation engine is in ready state
2025-06-21 09:55:04,631 - automation_service - INFO - ✅ Automation engine pre-initialized successfully
2025-06-21 09:55:07,025 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 09:55:07] "GET / HTTP/1.1" 200 -
2025-06-21 09:55:08,289 - data_interface.app - INFO - Using cached staging data
2025-06-21 09:55:08,295 - data_interface.app - INFO - Using cached staging data
2025-06-21 09:55:08,305 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 09:55:08] "GET /api/employees HTTP/1.1" 200 -
2025-06-21 09:55:08,308 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 09:55:08] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-21 09:55:44,449 - data_interface.app - INFO - Processing 1 selected records
2025-06-21 09:55:44,464 - automation_service - INFO - 🏃 Starting fast automation job auto_20250621_095544
2025-06-21 09:55:44,464 - automation_service - INFO - 🚀 Started automation job auto_20250621_095544 for 1 records (using pre-initialized engine)
2025-06-21 09:55:44,473 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 09:55:44] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-21 09:55:44,473 - automation_service - INFO - Created 1 mock staging records
2025-06-21 09:55:44,553 - automation_service - INFO - 📋 Staging Data Summary for Job auto_20250621_095544:
2025-06-21 09:55:44,557 - automation_service - INFO - Record 1: Employee 5b43e016-7793-4cea-a6a0-778bf09ae0c5 - 2025-06-10 - (OC7190) BOILER OPERATION / STN-BLR (STATION BOILE...
2025-06-21 09:55:44,562 - automation_service - INFO - ⚡ Processing 1 staging records with pre-initialized engine
2025-06-21 09:55:44,570 - core.enhanced_staging_automation - INFO - 🤖 Starting automation process for 1 staging records
2025-06-21 09:55:44,574 - core.enhanced_staging_automation - INFO - Processing record 1/1: Employee 5b43e016-7793-4cea-a6a0-778bf09ae0c5 (5b43e016-7793-4cea-a6a0-778bf09ae0c5)
2025-06-21 09:55:44,582 - core.enhanced_staging_automation - INFO - Processing record 5b43e016-7793-4cea-a6a0-778bf09ae0c5 (attempt 1/3)
2025-06-21 09:55:44,582 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-21 09:55:44,604 - core.enhanced_staging_automation - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 09:55:44,606 - core.enhanced_staging_automation - INFO - ✅ Already on task register page - skipping navigation
2025-06-21 09:55:44,608 - core.persistent_browser_manager - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 09:55:44,659 - core.persistent_browser_manager - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 09:55:46,521 - data_interface.app - INFO - Using cached staging data
2025-06-21 09:55:46,525 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 09:55:46] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-21 09:55:47,549 - core.persistent_browser_manager - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 09:55:47,549 - core.persistent_browser_manager - INFO - ✅ SUCCESS: Reached task register page!
2025-06-21 09:55:47,578 - core.enhanced_staging_automation - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 09:55:47,582 - core.enhanced_staging_automation - INFO - 🔄 Waiting for task register form to be ready...
2025-06-21 09:55:47,582 - core.enhanced_staging_automation - INFO - 🔍 Starting enhanced form readiness detection...
2025-06-21 09:55:47,713 - core.enhanced_staging_automation - INFO - ✅ Found date field (MainContent_txtTrxDate): ready for input
2025-06-21 09:55:47,846 - core.enhanced_staging_automation - INFO - ✅ Found autocomplete field 1: ready for input
2025-06-21 09:55:47,937 - core.enhanced_staging_automation - INFO - ✅ Found autocomplete field 2: ready for input
2025-06-21 09:55:47,937 - core.enhanced_staging_automation - INFO - 📊 Found 2 autocomplete fields ready
2025-06-21 09:55:47,937 - core.enhanced_staging_automation - INFO - 📊 Form readiness summary: 3 elements ready
2025-06-21 09:55:47,944 - core.enhanced_staging_automation - INFO - ✅ Form is ready for data entry
2025-06-21 09:55:48,953 - core.enhanced_staging_automation - INFO - ✅ Successfully navigated to task register and form is ready
2025-06-21 09:55:48,953 - core.enhanced_staging_automation - INFO - 🎯 Starting JAVASCRIPT date field filling
2025-06-21 09:55:48,956 - core.enhanced_staging_automation - INFO - 📅 Formatted date: 10/06/2025
2025-06-21 09:55:48,956 - core.enhanced_staging_automation - INFO - 📅 JavaScript date filling attempt 1/3
2025-06-21 09:55:48,971 - core.enhanced_staging_automation - INFO - ✅ Date field filled via JavaScript: 10/06/2025
2025-06-21 09:55:50,069 - core.enhanced_staging_automation - INFO - ✅ Date field value verified successfully
2025-06-21 09:55:50,077 - core.enhanced_staging_automation - INFO - 👤 Filling employee field with: Employee 5b43e016-7793-4cea-a6a0-778bf09ae0c5
2025-06-21 09:55:50,203 - core.enhanced_staging_automation - INFO - ✅ Found employee autocomplete field
2025-06-21 09:55:53,297 - core.enhanced_staging_automation - INFO - ✅ Employee field filled successfully
2025-06-21 09:55:53,297 - core.enhanced_staging_automation - INFO - 🔧 Filling charge job fields with 4 parts
2025-06-21 09:55:53,413 - core.enhanced_staging_automation - INFO - 📊 Found 2 available autocomplete fields
2025-06-21 09:55:53,413 - core.enhanced_staging_automation - INFO - 🔧 Filling Task Code (field 1): OC7190
2025-06-21 09:55:56,205 - core.enhanced_staging_automation - INFO - ✅ Task Code filled successfully
2025-06-21 09:55:56,214 - core.enhanced_staging_automation - WARNING - ⚠️ Field index 2 not available for Station Code
2025-06-21 09:55:56,218 - core.enhanced_staging_automation - WARNING - ⚠️ Field index 3 not available for Machine Code
2025-06-21 09:55:56,221 - core.enhanced_staging_automation - WARNING - ⚠️ Field index 4 not available for Expense Code
2025-06-21 09:55:56,247 - core.enhanced_staging_automation - INFO - ✅ All charge job fields processed
2025-06-21 09:55:56,264 - core.enhanced_staging_automation - INFO - 📤 Submitting form...
2025-06-21 09:55:56,591 - core.enhanced_staging_automation - INFO - ✅ Clicked Add button
2025-06-21 09:56:12,636 - core.enhanced_staging_automation - INFO - ✅ Successfully processed record 5b43e016-7793-4cea-a6a0-778bf09ae0c5 in 28.05s
2025-06-21 09:56:12,649 - core.enhanced_staging_automation - INFO - ✅ Automation completed: 1 successful, 0 failed
2025-06-21 09:56:12,651 - automation_service - INFO - ✅ Job auto_20250621_095544 completed: 1 successful, 0 failed
2025-06-21 10:00:00,196 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-21 10:00:00,200 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-21 10:00:00,200 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-21 10:00:00,201 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-21 10:00:00,449 - core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-21 10:00:00,536 - core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-21 10:00:00,536 - core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-21 10:00:00,540 - WDM - INFO - ====== WebDriver manager ======
2025-06-21 10:00:02,080 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-21 10:00:02,082 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-21 10:00:03,224 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-21 10:00:03,340 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-21 10:00:03,475 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-21 10:00:03,476 - core.browser_manager - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-21 10:00:03,477 - core.browser_manager - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-21 10:00:03,477 - core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-21 10:00:03,480 - core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-21 10:00:05,436 - core.browser_manager - INFO - ✅ WebDriver created and responsive
2025-06-21 10:00:05,552 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-21 10:00:05,575 - core.persistent_browser_manager - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-21 10:00:08,380 - core.persistent_browser_manager - INFO - WebDriver created and page loaded successfully
2025-06-21 10:00:08,380 - core.persistent_browser_manager - INFO - Performing initial login...
2025-06-21 10:00:14,384 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:00:14] "GET / HTTP/1.1" 200 -
2025-06-21 10:00:14,990 - core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-21 10:00:15,269 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-21 10:00:15,271 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-21 10:00:19,618 - core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-21 10:00:19,626 - core.persistent_browser_manager - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-21 10:00:19,630 - core.persistent_browser_manager - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 10:00:20,841 - data_interface.app - INFO - Successfully fetched 51 staging records
2025-06-21 10:00:20,850 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:00:20] "GET /api/employees HTTP/1.1" 200 -
2025-06-21 10:00:21,144 - data_interface.app - INFO - Successfully fetched 50 staging records
2025-06-21 10:00:21,152 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:00:21] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-21 10:00:21,882 - core.persistent_browser_manager - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 10:00:21,882 - core.persistent_browser_manager - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-21 10:00:21,898 - core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-21 10:00:21,898 - core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-21 10:00:21,898 - core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-21 10:00:21,912 - core.enhanced_staging_automation - INFO - ✅ Enhanced Staging Automation Engine initialized successfully
2025-06-21 10:00:21,936 - automation_service - INFO - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 10:00:21,936 - automation_service - INFO - Verifying form readiness...
2025-06-21 10:00:21,936 - core.enhanced_staging_automation - INFO - 🔍 Starting enhanced form readiness detection...
2025-06-21 10:00:22,026 - core.enhanced_staging_automation - INFO - ✅ Found date field (MainContent_txtTrxDate): ready for input
2025-06-21 10:00:22,115 - core.enhanced_staging_automation - INFO - ✅ Found autocomplete field 1: ready for input
2025-06-21 10:00:22,167 - core.enhanced_staging_automation - INFO - ✅ Found autocomplete field 2: ready for input
2025-06-21 10:00:22,170 - core.enhanced_staging_automation - INFO - 📊 Found 2 autocomplete fields ready
2025-06-21 10:00:22,173 - core.enhanced_staging_automation - INFO - 📊 Form readiness summary: 3 elements ready
2025-06-21 10:00:22,173 - core.enhanced_staging_automation - INFO - ✅ Form is ready for data entry
2025-06-21 10:00:23,183 - automation_service - INFO - ✅ Automation engine is in ready state
2025-06-21 10:00:23,185 - automation_service - INFO - ✅ Automation engine pre-initialized successfully
2025-06-21 10:00:24,921 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:00:24] "GET / HTTP/1.1" 200 -
2025-06-21 10:00:26,090 - data_interface.app - INFO - Using cached staging data
2025-06-21 10:00:26,090 - data_interface.app - INFO - Using cached staging data
2025-06-21 10:00:26,091 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:00:26] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-21 10:00:26,091 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:00:26] "GET /api/employees HTTP/1.1" 200 -
2025-06-21 10:00:40,461 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:00:40] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-21 10:01:35,159 - data_interface.app - INFO - Processing 1 selected records
2025-06-21 10:01:35,169 - automation_service - INFO - 🏃 Starting fast automation job auto_20250621_100135
2025-06-21 10:01:35,169 - automation_service - INFO - 🚀 Started automation job auto_20250621_100135 for 1 records (using pre-initialized engine)
2025-06-21 10:01:35,175 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:01:35] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-21 10:01:35,180 - automation_service - INFO - Created 1 mock staging records
2025-06-21 10:01:35,197 - automation_service - INFO - 📋 Staging Data Summary for Job auto_20250621_100135:
2025-06-21 10:01:35,198 - automation_service - INFO - Record 1: Employee 5b43e016-7793-4cea-a6a0-778bf09ae0c5 - 2025-06-10 - (OC7190) BOILER OPERATION / STN-BLR (STATION BOILE...
2025-06-21 10:01:35,198 - automation_service - INFO - ⚡ Processing 1 staging records with pre-initialized engine
2025-06-21 10:01:35,204 - core.enhanced_staging_automation - INFO - 🤖 Starting automation process for 1 staging records
2025-06-21 10:01:35,207 - core.enhanced_staging_automation - INFO - Processing record 1/1: Employee 5b43e016-7793-4cea-a6a0-778bf09ae0c5 (5b43e016-7793-4cea-a6a0-778bf09ae0c5)
2025-06-21 10:01:35,218 - core.enhanced_staging_automation - INFO - Processing record 5b43e016-7793-4cea-a6a0-778bf09ae0c5 (attempt 1/3)
2025-06-21 10:01:35,220 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-21 10:01:35,229 - core.enhanced_staging_automation - ERROR - Failed to navigate to task register: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=137.0.7151.104)
Stacktrace:
	GetHandleVerifier [0x0xe53b03+62899]
	GetHandleVerifier [0x0xe53b44+62964]
	(No symbol) [0x0xc810f3]
	(No symbol) [0x0xc708c0]
	(No symbol) [0x0xc8e87f]
	(No symbol) [0x0xcf514c]
	(No symbol) [0x0xd0f6a9]
	(No symbol) [0x0xcee306]
	(No symbol) [0x0xcbd670]
	(No symbol) [0x0xcbe4e4]
	GetHandleVerifier [0x0x10b4793+2556483]
	GetHandleVerifier [0x0x10afd02+2537394]
	GetHandleVerifier [0x0xe7a2fa+220586]
	GetHandleVerifier [0x0xe6aae8+157080]
	GetHandleVerifier [0x0xe7141d+184013]
	GetHandleVerifier [0x0xe5ba68+95512]
	GetHandleVerifier [0x0xe5bc10+95936]
	GetHandleVerifier [0x0xe46b5a+9738]
	BaseThreadInitThunk [0x0x77105d49+25]
	RtlInitializeExceptionChain [0x0x7773d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7773d021+561]

2025-06-21 10:01:35,235 - core.enhanced_staging_automation - WARNING - ⚠️ Attempt 1 failed for record 5b43e016-7793-4cea-a6a0-778bf09ae0c5: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=137.0.7151.104)
Stacktrace:
	GetHandleVerifier [0x0xe53b03+62899]
	GetHandleVerifier [0x0xe53b44+62964]
	(No symbol) [0x0xc810f3]
	(No symbol) [0x0xc708c0]
	(No symbol) [0x0xc8e87f]
	(No symbol) [0x0xcf514c]
	(No symbol) [0x0xd0f6a9]
	(No symbol) [0x0xcee306]
	(No symbol) [0x0xcbd670]
	(No symbol) [0x0xcbe4e4]
	GetHandleVerifier [0x0x10b4793+2556483]
	GetHandleVerifier [0x0x10afd02+2537394]
	GetHandleVerifier [0x0xe7a2fa+220586]
	GetHandleVerifier [0x0xe6aae8+157080]
	GetHandleVerifier [0x0xe7141d+184013]
	GetHandleVerifier [0x0xe5ba68+95512]
	GetHandleVerifier [0x0xe5bc10+95936]
	GetHandleVerifier [0x0xe46b5a+9738]
	BaseThreadInitThunk [0x0x77105d49+25]
	RtlInitializeExceptionChain [0x0x7773d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7773d021+561]

2025-06-21 10:01:37,247 - core.enhanced_staging_automation - INFO - Attempting to recover page state...
2025-06-21 10:01:37,249 - core.enhanced_staging_automation - WARNING - Page state recovery failed: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0xe53b03+62899]
	GetHandleVerifier [0x0xe53b44+62964]
	(No symbol) [0x0xc80f50]
	(No symbol) [0x0xcbc938]
	(No symbol) [0x0xcee3c6]
	(No symbol) [0x0xce9e72]
	(No symbol) [0x0xce9406]
	(No symbol) [0x0xc53a05]
	(No symbol) [0x0xc53f5e]
	(No symbol) [0x0xc543fd]
	GetHandleVerifier [0x0x10b4793+2556483]
	GetHandleVerifier [0x0x10afd02+2537394]
	GetHandleVerifier [0x0xe7a2fa+220586]
	GetHandleVerifier [0x0xe6aae8+157080]
	GetHandleVerifier [0x0xe7141d+184013]
	(No symbol) [0x0xc536d0]
	(No symbol) [0x0xc52f47]
	GetHandleVerifier [0x0x11eaa2c+3826908]
	BaseThreadInitThunk [0x0x77105d49+25]
	RtlInitializeExceptionChain [0x0x7773d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7773d021+561]

2025-06-21 10:01:37,249 - core.enhanced_staging_automation - INFO - Processing record 5b43e016-7793-4cea-a6a0-778bf09ae0c5 (attempt 2/3)
2025-06-21 10:01:37,256 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-21 10:01:37,258 - core.enhanced_staging_automation - ERROR - Failed to navigate to task register: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0xe53b03+62899]
	GetHandleVerifier [0x0xe53b44+62964]
	(No symbol) [0x0xc80f50]
	(No symbol) [0x0xcbc938]
	(No symbol) [0x0xcee3c6]
	(No symbol) [0x0xce9e72]
	(No symbol) [0x0xce9406]
	(No symbol) [0x0xc53a05]
	(No symbol) [0x0xc53f5e]
	(No symbol) [0x0xc543fd]
	GetHandleVerifier [0x0x10b4793+2556483]
	GetHandleVerifier [0x0x10afd02+2537394]
	GetHandleVerifier [0x0xe7a2fa+220586]
	GetHandleVerifier [0x0xe6aae8+157080]
	GetHandleVerifier [0x0xe7141d+184013]
	(No symbol) [0x0xc536d0]
	(No symbol) [0x0xc52f47]
	GetHandleVerifier [0x0x11eaa2c+3826908]
	BaseThreadInitThunk [0x0x77105d49+25]
	RtlInitializeExceptionChain [0x0x7773d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7773d021+561]

2025-06-21 10:01:37,265 - core.enhanced_staging_automation - WARNING - ⚠️ Attempt 2 failed for record 5b43e016-7793-4cea-a6a0-778bf09ae0c5: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0xe53b03+62899]
	GetHandleVerifier [0x0xe53b44+62964]
	(No symbol) [0x0xc80f50]
	(No symbol) [0x0xcbc938]
	(No symbol) [0x0xcee3c6]
	(No symbol) [0x0xce9e72]
	(No symbol) [0x0xce9406]
	(No symbol) [0x0xc53a05]
	(No symbol) [0x0xc53f5e]
	(No symbol) [0x0xc543fd]
	GetHandleVerifier [0x0x10b4793+2556483]
	GetHandleVerifier [0x0x10afd02+2537394]
	GetHandleVerifier [0x0xe7a2fa+220586]
	GetHandleVerifier [0x0xe6aae8+157080]
	GetHandleVerifier [0x0xe7141d+184013]
	(No symbol) [0x0xc536d0]
	(No symbol) [0x0xc52f47]
	GetHandleVerifier [0x0x11eaa2c+3826908]
	BaseThreadInitThunk [0x0x77105d49+25]
	RtlInitializeExceptionChain [0x0x7773d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7773d021+561]

2025-06-21 10:01:37,585 - data_interface.app - INFO - Using cached staging data
2025-06-21 10:01:37,588 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:01:37] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-21 10:01:39,279 - core.enhanced_staging_automation - INFO - Attempting to recover page state...
2025-06-21 10:01:39,279 - core.enhanced_staging_automation - WARNING - Page state recovery failed: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0xe53b03+62899]
	GetHandleVerifier [0x0xe53b44+62964]
	(No symbol) [0x0xc80f50]
	(No symbol) [0x0xcbc938]
	(No symbol) [0x0xcee3c6]
	(No symbol) [0x0xce9e72]
	(No symbol) [0x0xce9406]
	(No symbol) [0x0xc53a05]
	(No symbol) [0x0xc53f5e]
	(No symbol) [0x0xc543fd]
	GetHandleVerifier [0x0x10b4793+2556483]
	GetHandleVerifier [0x0x10afd02+2537394]
	GetHandleVerifier [0x0xe7a2fa+220586]
	GetHandleVerifier [0x0xe6aae8+157080]
	GetHandleVerifier [0x0xe7141d+184013]
	(No symbol) [0x0xc536d0]
	(No symbol) [0x0xc52f47]
	GetHandleVerifier [0x0x11eaa2c+3826908]
	BaseThreadInitThunk [0x0x77105d49+25]
	RtlInitializeExceptionChain [0x0x7773d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7773d021+561]

2025-06-21 10:01:39,279 - core.enhanced_staging_automation - INFO - Processing record 5b43e016-7793-4cea-a6a0-778bf09ae0c5 (attempt 3/3)
2025-06-21 10:01:39,279 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-21 10:01:39,279 - core.enhanced_staging_automation - ERROR - Failed to navigate to task register: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0xe53b03+62899]
	GetHandleVerifier [0x0xe53b44+62964]
	(No symbol) [0x0xc80f50]
	(No symbol) [0x0xcbc938]
	(No symbol) [0x0xcee3c6]
	(No symbol) [0x0xce9e72]
	(No symbol) [0x0xce9406]
	(No symbol) [0x0xc53a05]
	(No symbol) [0x0xc53f5e]
	(No symbol) [0x0xc543fd]
	GetHandleVerifier [0x0x10b4793+2556483]
	GetHandleVerifier [0x0x10afd02+2537394]
	GetHandleVerifier [0x0xe7a2fa+220586]
	GetHandleVerifier [0x0xe6aae8+157080]
	GetHandleVerifier [0x0xe7141d+184013]
	(No symbol) [0x0xc536d0]
	(No symbol) [0x0xc52f47]
	GetHandleVerifier [0x0x11eaa2c+3826908]
	BaseThreadInitThunk [0x0x77105d49+25]
	RtlInitializeExceptionChain [0x0x7773d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7773d021+561]

2025-06-21 10:01:39,279 - core.enhanced_staging_automation - WARNING - ⚠️ Attempt 3 failed for record 5b43e016-7793-4cea-a6a0-778bf09ae0c5: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0xe53b03+62899]
	GetHandleVerifier [0x0xe53b44+62964]
	(No symbol) [0x0xc80f50]
	(No symbol) [0x0xcbc938]
	(No symbol) [0x0xcee3c6]
	(No symbol) [0x0xce9e72]
	(No symbol) [0x0xce9406]
	(No symbol) [0x0xc53a05]
	(No symbol) [0x0xc53f5e]
	(No symbol) [0x0xc543fd]
	GetHandleVerifier [0x0x10b4793+2556483]
	GetHandleVerifier [0x0x10afd02+2537394]
	GetHandleVerifier [0x0xe7a2fa+220586]
	GetHandleVerifier [0x0xe6aae8+157080]
	GetHandleVerifier [0x0xe7141d+184013]
	(No symbol) [0x0xc536d0]
	(No symbol) [0x0xc52f47]
	GetHandleVerifier [0x0x11eaa2c+3826908]
	BaseThreadInitThunk [0x0x77105d49+25]
	RtlInitializeExceptionChain [0x0x7773d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7773d021+561]

2025-06-21 10:01:39,279 - core.enhanced_staging_automation - ERROR - ❌ Failed to process record 5b43e016-7793-4cea-a6a0-778bf09ae0c5 after 3 attempts: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0xe53b03+62899]
	GetHandleVerifier [0x0xe53b44+62964]
	(No symbol) [0x0xc80f50]
	(No symbol) [0x0xcbc938]
	(No symbol) [0x0xcee3c6]
	(No symbol) [0x0xce9e72]
	(No symbol) [0x0xce9406]
	(No symbol) [0x0xc53a05]
	(No symbol) [0x0xc53f5e]
	(No symbol) [0x0xc543fd]
	GetHandleVerifier [0x0x10b4793+2556483]
	GetHandleVerifier [0x0x10afd02+2537394]
	GetHandleVerifier [0x0xe7a2fa+220586]
	GetHandleVerifier [0x0xe6aae8+157080]
	GetHandleVerifier [0x0xe7141d+184013]
	(No symbol) [0x0xc536d0]
	(No symbol) [0x0xc52f47]
	GetHandleVerifier [0x0x11eaa2c+3826908]
	BaseThreadInitThunk [0x0x77105d49+25]
	RtlInitializeExceptionChain [0x0x7773d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7773d021+561]

2025-06-21 10:01:39,290 - core.enhanced_staging_automation - INFO - ✅ Automation completed: 0 successful, 1 failed
2025-06-21 10:01:39,290 - automation_service - INFO - ✅ Job auto_20250621_100135 completed: 0 successful, 1 failed
2025-06-21 10:16:31,919 - automation_service - INFO - 🚀 Starting pre-initialization of automation engine...
2025-06-21 10:16:31,929 - core.enhanced_staging_automation - INFO - Initializing Enhanced Staging Automation Engine
2025-06-21 10:16:31,931 - core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-21 10:16:31,931 - core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-21 10:16:32,383 - core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-21 10:16:32,561 - core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-21 10:16:32,561 - core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-21 10:16:32,588 - WDM - INFO - ====== WebDriver manager ======
2025-06-21 10:16:34,257 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-21 10:16:34,259 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-21 10:16:35,209 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-21 10:16:35,370 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-21 10:16:35,548 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-21 10:16:35,552 - core.browser_manager - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-21 10:16:35,552 - core.browser_manager - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-21 10:16:35,556 - core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-21 10:16:35,556 - core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-21 10:16:37,858 - core.browser_manager - INFO - ✅ WebDriver created and responsive
2025-06-21 10:16:37,953 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-21 10:16:37,963 - core.persistent_browser_manager - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-21 10:16:42,837 - core.persistent_browser_manager - INFO - WebDriver created and page loaded successfully
2025-06-21 10:16:42,837 - core.persistent_browser_manager - INFO - Performing initial login...
2025-06-21 10:16:49,402 - core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-21 10:16:53,022 - core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-21 10:16:53,024 - core.persistent_browser_manager - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-21 10:16:53,025 - core.persistent_browser_manager - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 10:16:56,371 - core.persistent_browser_manager - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 10:16:56,371 - core.persistent_browser_manager - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-21 10:16:56,384 - core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-21 10:16:56,384 - core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-21 10:16:56,384 - core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-21 10:16:56,395 - core.enhanced_staging_automation - INFO - ✅ Enhanced Staging Automation Engine initialized successfully
2025-06-21 10:16:56,419 - automation_service - INFO - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 10:16:56,419 - automation_service - INFO - Verifying form readiness...
2025-06-21 10:16:56,419 - core.enhanced_staging_automation - INFO - 🔍 Starting enhanced form readiness detection...
2025-06-21 10:16:56,508 - core.enhanced_staging_automation - INFO - ✅ Found date field (MainContent_txtTrxDate): ready for input
2025-06-21 10:16:56,588 - core.enhanced_staging_automation - INFO - ✅ Found autocomplete field 1: ready for input
2025-06-21 10:16:56,627 - core.enhanced_staging_automation - INFO - ✅ Found autocomplete field 2: ready for input
2025-06-21 10:16:56,627 - core.enhanced_staging_automation - INFO - 📊 Found 2 autocomplete fields ready
2025-06-21 10:16:56,629 - core.enhanced_staging_automation - INFO - 📊 Form readiness summary: 3 elements ready
2025-06-21 10:16:56,629 - core.enhanced_staging_automation - INFO - ✅ Form is ready for data entry
2025-06-21 10:16:57,641 - automation_service - INFO - ✅ Automation engine is in ready state
2025-06-21 10:16:57,641 - automation_service - INFO - ✅ Automation engine pre-initialized successfully
2025-06-21 10:16:59,089 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:16:59] "GET / HTTP/1.1" 200 -
2025-06-21 10:17:00,312 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-21 10:17:00,317 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-21 10:17:05,587 - data_interface.app - INFO - Successfully fetched 51 staging records
2025-06-21 10:17:05,591 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:17:05] "GET /api/employees HTTP/1.1" 200 -
2025-06-21 10:17:05,901 - data_interface.app - INFO - Successfully fetched 50 staging records
2025-06-21 10:17:05,909 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:17:05] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-21 10:17:32,844 - data_interface.app - INFO - Processing 1 selected records
2025-06-21 10:17:32,851 - automation_service - INFO - 🏃 Starting fast automation job auto_20250621_101732
2025-06-21 10:17:32,851 - automation_service - INFO - 🚀 Started automation job auto_20250621_101732 for 1 records (using pre-initialized engine)
2025-06-21 10:17:32,853 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:17:32] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-21 10:17:32,855 - automation_service - INFO - Created 1 mock staging records
2025-06-21 10:17:32,867 - automation_service - INFO - 📋 Staging Data Summary for Job auto_20250621_101732:
2025-06-21 10:17:32,869 - automation_service - INFO - Record 1: Employee 5b43e016-7793-4cea-a6a0-778bf09ae0c5 - 2025-06-10 - (OC7190) BOILER OPERATION / STN-BLR (STATION BOILE...
2025-06-21 10:17:32,869 - automation_service - INFO - ⚡ Processing 1 staging records with pre-initialized engine
2025-06-21 10:17:32,874 - core.enhanced_staging_automation - INFO - 🤖 Starting automation process for 1 staging records
2025-06-21 10:17:32,876 - core.enhanced_staging_automation - INFO - Processing record 1/1: Employee 5b43e016-7793-4cea-a6a0-778bf09ae0c5 (5b43e016-7793-4cea-a6a0-778bf09ae0c5)
2025-06-21 10:17:32,881 - core.enhanced_staging_automation - INFO - Processing record 5b43e016-7793-4cea-a6a0-778bf09ae0c5 (attempt 1/3)
2025-06-21 10:17:32,885 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-21 10:17:32,899 - core.enhanced_staging_automation - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 10:17:32,901 - core.enhanced_staging_automation - INFO - ✅ Already on task register page - skipping navigation
2025-06-21 10:17:32,903 - core.persistent_browser_manager - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 10:17:32,939 - core.persistent_browser_manager - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 10:17:35,536 - data_interface.app - INFO - Using cached staging data
2025-06-21 10:17:35,536 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:17:35] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-21 10:17:36,072 - core.persistent_browser_manager - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 10:17:36,074 - core.persistent_browser_manager - INFO - ✅ SUCCESS: Reached task register page!
2025-06-21 10:17:36,084 - core.enhanced_staging_automation - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 10:17:36,084 - core.enhanced_staging_automation - INFO - 🔄 Waiting for task register form to be ready...
2025-06-21 10:17:36,084 - core.enhanced_staging_automation - INFO - 🔍 Starting enhanced form readiness detection...
2025-06-21 10:17:36,172 - core.enhanced_staging_automation - INFO - ✅ Found date field (MainContent_txtTrxDate): ready for input
2025-06-21 10:17:36,319 - core.enhanced_staging_automation - INFO - ✅ Found autocomplete field 1: ready for input
2025-06-21 10:17:36,374 - core.enhanced_staging_automation - INFO - ✅ Found autocomplete field 2: ready for input
2025-06-21 10:17:36,377 - core.enhanced_staging_automation - INFO - 📊 Found 2 autocomplete fields ready
2025-06-21 10:17:36,381 - core.enhanced_staging_automation - INFO - 📊 Form readiness summary: 3 elements ready
2025-06-21 10:17:36,383 - core.enhanced_staging_automation - INFO - ✅ Form is ready for data entry
2025-06-21 10:17:37,391 - core.enhanced_staging_automation - INFO - ✅ Successfully navigated to task register and form is ready
2025-06-21 10:17:37,395 - core.enhanced_staging_automation - INFO - 🎯 Starting JAVASCRIPT date field filling
2025-06-21 10:17:37,397 - core.enhanced_staging_automation - INFO - 📅 Formatted date: 10/06/2025
2025-06-21 10:17:37,397 - core.enhanced_staging_automation - INFO - 📅 JavaScript date filling attempt 1/3
2025-06-21 10:17:37,427 - core.enhanced_staging_automation - INFO - ✅ Date field filled via JavaScript: 10/06/2025
2025-06-21 10:17:38,523 - core.enhanced_staging_automation - INFO - ✅ Date field value verified successfully
2025-06-21 10:17:38,526 - core.enhanced_staging_automation - INFO - 👤 Filling employee field with: Employee 5b43e016-7793-4cea-a6a0-778bf09ae0c5
2025-06-21 10:17:38,651 - core.enhanced_staging_automation - INFO - ✅ Found employee autocomplete field
2025-06-21 10:17:41,815 - core.enhanced_staging_automation - INFO - ✅ Employee field filled successfully
2025-06-21 10:17:41,817 - core.enhanced_staging_automation - INFO - 🔧 Filling charge job fields with 4 parts
2025-06-21 10:17:41,987 - core.enhanced_staging_automation - INFO - 📊 Found 2 available autocomplete fields
2025-06-21 10:17:41,989 - core.enhanced_staging_automation - INFO - 🔧 Filling Task Code (field 1): OC7190
2025-06-21 10:17:45,101 - core.enhanced_staging_automation - INFO - ✅ Task Code filled successfully
2025-06-21 10:17:45,101 - core.enhanced_staging_automation - WARNING - ⚠️ Field index 2 not available for Station Code
2025-06-21 10:17:45,101 - core.enhanced_staging_automation - WARNING - ⚠️ Field index 3 not available for Machine Code
2025-06-21 10:17:45,101 - core.enhanced_staging_automation - WARNING - ⚠️ Field index 4 not available for Expense Code
2025-06-21 10:17:45,101 - core.enhanced_staging_automation - INFO - ✅ All charge job fields processed
2025-06-21 10:17:45,101 - core.enhanced_staging_automation - INFO - 📤 Submitting form...
2025-06-21 10:17:45,315 - core.enhanced_staging_automation - INFO - ✅ Clicked Add button
2025-06-21 10:17:53,361 - core.enhanced_staging_automation - ERROR - Form submission verification failed: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=137.0.7151.104)
Stacktrace:
	GetHandleVerifier [0x0xe53b03+62899]
	GetHandleVerifier [0x0xe53b44+62964]
	(No symbol) [0x0xc810f3]
	(No symbol) [0x0xc708c0]
	(No symbol) [0x0xc8e87f]
	(No symbol) [0x0xcf514c]
	(No symbol) [0x0xd0f6a9]
	(No symbol) [0x0xcee306]
	(No symbol) [0x0xcbd670]
	(No symbol) [0x0xcbe4e4]
	GetHandleVerifier [0x0x10b4793+2556483]
	GetHandleVerifier [0x0x10afd02+2537394]
	GetHandleVerifier [0x0xe7a2fa+220586]
	GetHandleVerifier [0x0xe6aae8+157080]
	GetHandleVerifier [0x0xe7141d+184013]
	GetHandleVerifier [0x0xe5ba68+95512]
	GetHandleVerifier [0x0xe5bc10+95936]
	GetHandleVerifier [0x0xe46b5a+9738]
	BaseThreadInitThunk [0x0x77105d49+25]
	RtlInitializeExceptionChain [0x0x7773d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7773d021+561]

2025-06-21 10:17:53,364 - core.enhanced_staging_automation - WARNING - ⚠️ Attempt 1 failed for record 5b43e016-7793-4cea-a6a0-778bf09ae0c5: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=137.0.7151.104)
Stacktrace:
	GetHandleVerifier [0x0xe53b03+62899]
	GetHandleVerifier [0x0xe53b44+62964]
	(No symbol) [0x0xc810f3]
	(No symbol) [0x0xc708c0]
	(No symbol) [0x0xc8e87f]
	(No symbol) [0x0xcf514c]
	(No symbol) [0x0xd0f6a9]
	(No symbol) [0x0xcee306]
	(No symbol) [0x0xcbd670]
	(No symbol) [0x0xcbe4e4]
	GetHandleVerifier [0x0x10b4793+2556483]
	GetHandleVerifier [0x0x10afd02+2537394]
	GetHandleVerifier [0x0xe7a2fa+220586]
	GetHandleVerifier [0x0xe6aae8+157080]
	GetHandleVerifier [0x0xe7141d+184013]
	GetHandleVerifier [0x0xe5ba68+95512]
	GetHandleVerifier [0x0xe5bc10+95936]
	GetHandleVerifier [0x0xe46b5a+9738]
	BaseThreadInitThunk [0x0x77105d49+25]
	RtlInitializeExceptionChain [0x0x7773d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7773d021+561]

2025-06-21 10:17:55,379 - core.enhanced_staging_automation - INFO - Attempting to recover page state...
2025-06-21 10:17:55,382 - core.enhanced_staging_automation - WARNING - Page state recovery failed: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0xe53b03+62899]
	GetHandleVerifier [0x0xe53b44+62964]
	(No symbol) [0x0xc80f50]
	(No symbol) [0x0xcbc938]
	(No symbol) [0x0xcee3c6]
	(No symbol) [0x0xce9e72]
	(No symbol) [0x0xce9406]
	(No symbol) [0x0xc53a05]
	(No symbol) [0x0xc53f5e]
	(No symbol) [0x0xc543fd]
	GetHandleVerifier [0x0x10b4793+2556483]
	GetHandleVerifier [0x0x10afd02+2537394]
	GetHandleVerifier [0x0xe7a2fa+220586]
	GetHandleVerifier [0x0xe6aae8+157080]
	GetHandleVerifier [0x0xe7141d+184013]
	(No symbol) [0x0xc536d0]
	(No symbol) [0x0xc52f47]
	GetHandleVerifier [0x0x11eaa2c+3826908]
	BaseThreadInitThunk [0x0x77105d49+25]
	RtlInitializeExceptionChain [0x0x7773d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7773d021+561]

2025-06-21 10:17:55,382 - core.enhanced_staging_automation - INFO - Processing record 5b43e016-7793-4cea-a6a0-778bf09ae0c5 (attempt 2/3)
2025-06-21 10:17:55,389 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-21 10:17:55,396 - core.enhanced_staging_automation - ERROR - Failed to navigate to task register: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0xe53b03+62899]
	GetHandleVerifier [0x0xe53b44+62964]
	(No symbol) [0x0xc80f50]
	(No symbol) [0x0xcbc938]
	(No symbol) [0x0xcee3c6]
	(No symbol) [0x0xce9e72]
	(No symbol) [0x0xce9406]
	(No symbol) [0x0xc53a05]
	(No symbol) [0x0xc53f5e]
	(No symbol) [0x0xc543fd]
	GetHandleVerifier [0x0x10b4793+2556483]
	GetHandleVerifier [0x0x10afd02+2537394]
	GetHandleVerifier [0x0xe7a2fa+220586]
	GetHandleVerifier [0x0xe6aae8+157080]
	GetHandleVerifier [0x0xe7141d+184013]
	(No symbol) [0x0xc536d0]
	(No symbol) [0x0xc52f47]
	GetHandleVerifier [0x0x11eaa2c+3826908]
	BaseThreadInitThunk [0x0x77105d49+25]
	RtlInitializeExceptionChain [0x0x7773d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7773d021+561]

2025-06-21 10:17:55,396 - core.enhanced_staging_automation - WARNING - ⚠️ Attempt 2 failed for record 5b43e016-7793-4cea-a6a0-778bf09ae0c5: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0xe53b03+62899]
	GetHandleVerifier [0x0xe53b44+62964]
	(No symbol) [0x0xc80f50]
	(No symbol) [0x0xcbc938]
	(No symbol) [0x0xcee3c6]
	(No symbol) [0x0xce9e72]
	(No symbol) [0x0xce9406]
	(No symbol) [0x0xc53a05]
	(No symbol) [0x0xc53f5e]
	(No symbol) [0x0xc543fd]
	GetHandleVerifier [0x0x10b4793+2556483]
	GetHandleVerifier [0x0x10afd02+2537394]
	GetHandleVerifier [0x0xe7a2fa+220586]
	GetHandleVerifier [0x0xe6aae8+157080]
	GetHandleVerifier [0x0xe7141d+184013]
	(No symbol) [0x0xc536d0]
	(No symbol) [0x0xc52f47]
	GetHandleVerifier [0x0x11eaa2c+3826908]
	BaseThreadInitThunk [0x0x77105d49+25]
	RtlInitializeExceptionChain [0x0x7773d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7773d021+561]

2025-06-21 10:17:57,414 - core.enhanced_staging_automation - INFO - Attempting to recover page state...
2025-06-21 10:17:57,417 - core.enhanced_staging_automation - WARNING - Page state recovery failed: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0xe53b03+62899]
	GetHandleVerifier [0x0xe53b44+62964]
	(No symbol) [0x0xc80f50]
	(No symbol) [0x0xcbc938]
	(No symbol) [0x0xcee3c6]
	(No symbol) [0x0xce9e72]
	(No symbol) [0x0xce9406]
	(No symbol) [0x0xc53a05]
	(No symbol) [0x0xc53f5e]
	(No symbol) [0x0xc543fd]
	GetHandleVerifier [0x0x10b4793+2556483]
	GetHandleVerifier [0x0x10afd02+2537394]
	GetHandleVerifier [0x0xe7a2fa+220586]
	GetHandleVerifier [0x0xe6aae8+157080]
	GetHandleVerifier [0x0xe7141d+184013]
	(No symbol) [0x0xc536d0]
	(No symbol) [0x0xc52f47]
	GetHandleVerifier [0x0x11eaa2c+3826908]
	BaseThreadInitThunk [0x0x77105d49+25]
	RtlInitializeExceptionChain [0x0x7773d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7773d021+561]

2025-06-21 10:17:57,417 - core.enhanced_staging_automation - INFO - Processing record 5b43e016-7793-4cea-a6a0-778bf09ae0c5 (attempt 3/3)
2025-06-21 10:17:57,417 - core.enhanced_staging_automation - INFO - 🎯 Starting robust navigation to task register...
2025-06-21 10:17:57,429 - core.enhanced_staging_automation - ERROR - Failed to navigate to task register: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0xe53b03+62899]
	GetHandleVerifier [0x0xe53b44+62964]
	(No symbol) [0x0xc80f50]
	(No symbol) [0x0xcbc938]
	(No symbol) [0x0xcee3c6]
	(No symbol) [0x0xce9e72]
	(No symbol) [0x0xce9406]
	(No symbol) [0x0xc53a05]
	(No symbol) [0x0xc53f5e]
	(No symbol) [0x0xc543fd]
	GetHandleVerifier [0x0x10b4793+2556483]
	GetHandleVerifier [0x0x10afd02+2537394]
	GetHandleVerifier [0x0xe7a2fa+220586]
	GetHandleVerifier [0x0xe6aae8+157080]
	GetHandleVerifier [0x0xe7141d+184013]
	(No symbol) [0x0xc536d0]
	(No symbol) [0x0xc52f47]
	GetHandleVerifier [0x0x11eaa2c+3826908]
	BaseThreadInitThunk [0x0x77105d49+25]
	RtlInitializeExceptionChain [0x0x7773d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7773d021+561]

2025-06-21 10:17:57,434 - core.enhanced_staging_automation - WARNING - ⚠️ Attempt 3 failed for record 5b43e016-7793-4cea-a6a0-778bf09ae0c5: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0xe53b03+62899]
	GetHandleVerifier [0x0xe53b44+62964]
	(No symbol) [0x0xc80f50]
	(No symbol) [0x0xcbc938]
	(No symbol) [0x0xcee3c6]
	(No symbol) [0x0xce9e72]
	(No symbol) [0x0xce9406]
	(No symbol) [0x0xc53a05]
	(No symbol) [0x0xc53f5e]
	(No symbol) [0x0xc543fd]
	GetHandleVerifier [0x0x10b4793+2556483]
	GetHandleVerifier [0x0x10afd02+2537394]
	GetHandleVerifier [0x0xe7a2fa+220586]
	GetHandleVerifier [0x0xe6aae8+157080]
	GetHandleVerifier [0x0xe7141d+184013]
	(No symbol) [0x0xc536d0]
	(No symbol) [0x0xc52f47]
	GetHandleVerifier [0x0x11eaa2c+3826908]
	BaseThreadInitThunk [0x0x77105d49+25]
	RtlInitializeExceptionChain [0x0x7773d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7773d021+561]

2025-06-21 10:17:57,434 - core.enhanced_staging_automation - ERROR - ❌ Failed to process record 5b43e016-7793-4cea-a6a0-778bf09ae0c5 after 3 attempts: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0xe53b03+62899]
	GetHandleVerifier [0x0xe53b44+62964]
	(No symbol) [0x0xc80f50]
	(No symbol) [0x0xcbc938]
	(No symbol) [0x0xcee3c6]
	(No symbol) [0x0xce9e72]
	(No symbol) [0x0xce9406]
	(No symbol) [0x0xc53a05]
	(No symbol) [0x0xc53f5e]
	(No symbol) [0x0xc543fd]
	GetHandleVerifier [0x0x10b4793+2556483]
	GetHandleVerifier [0x0x10afd02+2537394]
	GetHandleVerifier [0x0xe7a2fa+220586]
	GetHandleVerifier [0x0xe6aae8+157080]
	GetHandleVerifier [0x0xe7141d+184013]
	(No symbol) [0x0xc536d0]
	(No symbol) [0x0xc52f47]
	GetHandleVerifier [0x0x11eaa2c+3826908]
	BaseThreadInitThunk [0x0x77105d49+25]
	RtlInitializeExceptionChain [0x0x7773d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7773d021+561]

2025-06-21 10:17:57,447 - core.enhanced_staging_automation - INFO - ✅ Automation completed: 0 successful, 1 failed
2025-06-21 10:17:57,447 - automation_service - INFO - ✅ Job auto_20250621_101732 completed: 0 successful, 1 failed
2025-06-21 10:22:25,901 - run_user_controlled_automation - INFO - 🌐 Fetching staging data from: http://localhost:5173/api/staging/data
2025-06-21 10:22:30,942 - run_user_controlled_automation - INFO - ✅ Fetched 51 records from API
2025-06-21 10:22:31,493 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-21 10:22:31,498 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-21 10:22:44,980 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:22:44] "GET /api/staging/data HTTP/1.1" 200 -
2025-06-21 10:22:49,629 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:22:49] "GET / HTTP/1.1" 200 -
2025-06-21 10:22:53,809 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:22:53] "GET /api/employees HTTP/1.1" 200 -
2025-06-21 10:31:32,530 - test_real_api_data - INFO - 🌐 Fetching data from: http://localhost:5173/api/staging/data
2025-06-21 10:31:37,985 - test_real_api_data - INFO - ✅ API response received
2025-06-21 10:31:37,985 - test_real_api_data - INFO - ✅ Fetched 51 records from API
2025-06-21 10:31:37,985 - test_real_api_data - INFO - 📋 First record employee: Abu Hurairoh
2025-06-21 10:31:37,985 - test_real_api_data - INFO - 📋 First record date: 2025-05-30
2025-06-21 10:31:37,985 - test_real_api_data - INFO - 📋 First record raw_charge_job: (OC7110) FRUIT RECEPTION AND STORAGE / STN-FRC (STATION FRUIT RECEPTION) / FRC00000 (LABOUR COST) / L (LABOUR)
2025-06-21 10:31:37,988 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-21 10:31:37,989 - test_real_api_data - INFO -    [0]: (OC7110) FRUIT RECEPTION AND STORAGE
2025-06-21 10:31:37,989 - test_real_api_data - INFO -    [1]: STN-FRC (STATION FRUIT RECEPTION)
2025-06-21 10:31:37,989 - test_real_api_data - INFO -    [2]: FRC00000 (LABOUR COST)
2025-06-21 10:31:37,989 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-21 10:31:37,992 - test_real_api_data - INFO - ✅ Created normal entry: 5.0 hours
2025-06-21 10:31:37,992 - test_real_api_data - INFO - ✅ Created overtime entry: 11.5 hours
2025-06-21 10:31:38,000 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-21 10:31:38,000 - test_real_api_data - INFO -    [0]: (OC7110) FRUIT RECEPTION AND STORAGE
2025-06-21 10:31:38,001 - test_real_api_data - INFO -    [1]: STN-FRC (STATION FRUIT RECEPTION)
2025-06-21 10:31:38,002 - test_real_api_data - INFO -    [2]: FRC00000 (LABOUR COST)
2025-06-21 10:31:38,002 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-21 10:31:38,002 - test_real_api_data - INFO - ✅ Created normal entry: 5.0 hours
2025-06-21 10:31:38,002 - test_real_api_data - INFO - ✅ Created overtime entry: 11.5 hours
2025-06-21 10:31:38,009 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-21 10:31:38,009 - test_real_api_data - INFO -    [0]: (OC7110) FRUIT RECEPTION AND STORAGE
2025-06-21 10:31:38,009 - test_real_api_data - INFO -    [1]: STN-FRC (STATION FRUIT RECEPTION)
2025-06-21 10:31:38,013 - test_real_api_data - INFO -    [2]: FRC00000 (LABOUR COST)
2025-06-21 10:31:38,013 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-21 10:31:38,016 - test_real_api_data - INFO - ✅ Created normal entry: 7.0 hours
2025-06-21 10:31:38,016 - test_real_api_data - INFO - ✅ Created overtime entry: 1.0 hours
2025-06-21 10:31:38,479 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-21 10:31:38,479 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-21 10:31:50,426 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:31:50] "GET /api/staging/data HTTP/1.1" 200 -
2025-06-21 10:31:58,250 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:31:58] "GET /api/employees HTTP/1.1" 200 -
2025-06-21 10:32:41,153 - test_real_api_data - INFO - 🚀 Initializing browser...
2025-06-21 10:32:41,153 - src.core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-21 10:32:41,155 - src.core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-21 10:32:41,349 - src.core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-21 10:32:41,536 - src.core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-21 10:32:41,536 - src.core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-21 10:32:41,538 - WDM - INFO - ====== WebDriver manager ======
2025-06-21 10:32:43,451 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-21 10:32:43,563 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-21 10:32:43,691 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-21 10:32:43,691 - src.core.browser_manager - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-21 10:32:43,691 - src.core.browser_manager - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-21 10:32:43,691 - src.core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-21 10:32:43,691 - src.core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-21 10:32:45,475 - src.core.browser_manager - INFO - ✅ WebDriver created and responsive
2025-06-21 10:32:45,585 - src.core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-21 10:32:45,597 - src.core.persistent_browser_manager - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-21 10:32:48,748 - src.core.persistent_browser_manager - INFO - WebDriver created and page loaded successfully
2025-06-21 10:32:48,748 - src.core.persistent_browser_manager - INFO - Performing initial login...
2025-06-21 10:33:00,278 - src.core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-21 10:33:02,302 - src.core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-21 10:33:02,302 - src.core.persistent_browser_manager - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-21 10:33:02,302 - src.core.persistent_browser_manager - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 10:33:05,089 - src.core.persistent_browser_manager - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 10:33:05,089 - src.core.persistent_browser_manager - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-21 10:33:05,105 - src.core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-21 10:33:05,106 - src.core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-21 10:33:05,106 - src.core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-21 10:33:05,108 - src.core.persistent_browser_manager - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 10:33:05,121 - src.core.persistent_browser_manager - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 10:33:07,953 - src.core.persistent_browser_manager - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-21 10:33:07,953 - src.core.persistent_browser_manager - INFO - ✅ SUCCESS: Reached task register page!
2025-06-21 10:33:07,953 - test_real_api_data - INFO - ✅ Browser initialized and ready
2025-06-21 10:33:08,353 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-21 10:33:08,353 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-21 10:33:11,350 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:33:11] "GET / HTTP/1.1" 200 -
2025-06-21 10:33:17,320 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:33:17] "GET /api/staging/data?status=staged HTTP/1.1" 200 -
2025-06-21 10:33:18,315 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:33:18] "GET /api/employees HTTP/1.1" 200 -
2025-06-21 10:33:25,836 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:33:25] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-21 10:33:29,488 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:33:29] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-21 10:33:29,491 - test_real_api_data - INFO - 🌐 Fetching data from: http://localhost:5173/api/staging/data
2025-06-21 10:33:34,832 - test_real_api_data - INFO - ✅ API response received
2025-06-21 10:33:34,832 - test_real_api_data - INFO - ✅ Fetched 51 records from API
2025-06-21 10:33:34,837 - test_real_api_data - INFO - 📋 First record employee: Abu Hurairoh
2025-06-21 10:33:34,837 - test_real_api_data - INFO - 📋 First record date: 2025-05-30
2025-06-21 10:33:34,837 - test_real_api_data - INFO - 📋 First record raw_charge_job: (OC7110) FRUIT RECEPTION AND STORAGE / STN-FRC (STATION FRUIT RECEPTION) / FRC00000 (LABOUR COST) / L (LABOUR)
2025-06-21 10:33:34,853 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-21 10:33:34,853 - test_real_api_data - INFO -    [0]: (OC7110) FRUIT RECEPTION AND STORAGE
2025-06-21 10:33:34,855 - test_real_api_data - INFO -    [1]: STN-FRC (STATION FRUIT RECEPTION)
2025-06-21 10:33:34,857 - test_real_api_data - INFO -    [2]: FRC00000 (LABOUR COST)
2025-06-21 10:33:34,860 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-21 10:33:34,863 - test_real_api_data - INFO - ✅ Created normal entry: 5.0 hours
2025-06-21 10:33:34,869 - test_real_api_data - INFO - ✅ Created overtime entry: 11.5 hours
2025-06-21 10:33:34,876 - test_real_api_data - INFO - ✅ Created normal entry: 5.0 hours
2025-06-21 10:33:34,878 - test_real_api_data - INFO - ✅ Created overtime entry: 11.5 hours
2025-06-21 10:33:34,909 - test_real_api_data - INFO - 🎯 Processing record 1: Abu Hurairoh
2025-06-21 10:33:34,911 - test_real_api_data - INFO - 📅 Date: 2025-05-30
2025-06-21 10:33:34,913 - test_real_api_data - INFO - 🔧 Raw charge job: (OC7110) FRUIT RECEPTION AND STORAGE / STN-FRC (STATION FRUIT RECEPTION) / FRC00000 (LABOUR COST) / L (LABOUR)
2025-06-21 10:33:34,913 - test_real_api_data - INFO - 🔘 Transaction type: Normal
2025-06-21 10:33:34,915 - test_real_api_data - INFO - ⏰ Hours: 5.0
2025-06-21 10:33:34,917 - test_real_api_data - INFO - 📝 Entry type: normal
2025-06-21 10:33:34,935 - test_real_api_data - INFO - 📅 Step 0: Filling document date field...
2025-06-21 10:33:34,935 - test_real_api_data - INFO - 📅 Document date calculated: Today=21/06/2025, Transaction=2025-05-30 -> Document=21/05/2025
2025-06-21 10:33:34,935 - test_real_api_data - INFO - 📅 Filling document date: 21/05/2025
2025-06-21 10:33:34,984 - test_real_api_data - INFO - ✅ Document date filled: 21/05/2025
2025-06-21 10:33:36,494 - test_real_api_data - INFO - ✅ Document date field filled successfully
2025-06-21 10:33:36,494 - test_real_api_data - INFO - 📅 Step 1: Filling transaction date: 30/05/2025
2025-06-21 10:33:36,513 - test_real_api_data - INFO - ✅ Date filled: 30/05/2025
2025-06-21 10:33:36,730 - werkzeug - INFO - 127.0.0.1 - - [21/Jun/2025 10:33:36] "GET /api/staging/data?status=staged HTTP/1.1" 200 -
2025-06-21 10:33:39,346 - test_real_api_data - INFO - 👤 Step 2: Filling employee: Abu Hurairoh
2025-06-21 10:33:39,384 - test_real_api_data - INFO - 🔍 Found 2 autocomplete fields
2025-06-21 10:33:44,118 - test_real_api_data - INFO - ✅ Employee filled: Abu Hurairoh
2025-06-21 10:33:44,118 - test_real_api_data - INFO - 🔘 Step 3: Selecting transaction type: Normal
2025-06-21 10:33:44,118 - test_real_api_data - INFO - 🔘 Selecting transaction type: Normal
2025-06-21 10:33:44,190 - test_real_api_data - INFO - ✅ Normal transaction type selected
2025-06-21 10:33:45,200 - test_real_api_data - INFO - ✅ Transaction type selected: Normal
2025-06-21 10:33:45,200 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-21 10:33:45,202 - test_real_api_data - INFO -    [0]: (OC7110) FRUIT RECEPTION AND STORAGE
2025-06-21 10:33:45,202 - test_real_api_data - INFO -    [1]: STN-FRC (STATION FRUIT RECEPTION)
2025-06-21 10:33:45,203 - test_real_api_data - INFO -    [2]: FRC00000 (LABOUR COST)
2025-06-21 10:33:45,203 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-21 10:33:45,203 - test_real_api_data - INFO - 🔧 Step 4: Filling 4 charge job components sequentially...
2025-06-21 10:33:45,223 - test_real_api_data - INFO - 🔍 Available autocomplete fields: 2
2025-06-21 10:33:45,224 - test_real_api_data - INFO - 🔧 Filling field 1 with component 0: (OC7110) FRUIT RECEPTION AND STORAGE
2025-06-21 10:33:49,637 - test_real_api_data - INFO - ✅ Component 0 filled: (OC7110) FRUIT RECEPTION AND STORAGE
2025-06-21 10:33:50,705 - test_real_api_data - INFO - 🔍 Autocomplete fields after filling component 0: 5
2025-06-21 10:33:50,705 - test_real_api_data - INFO - 🔧 Filling field 2 with component 1: STN-FRC (STATION FRUIT RECEPTION)
2025-06-21 10:33:55,069 - test_real_api_data - INFO - ✅ Component 1 filled: STN-FRC (STATION FRUIT RECEPTION)
2025-06-21 10:33:56,241 - test_real_api_data - INFO - 🔍 Autocomplete fields after filling component 1: 5
2025-06-21 10:33:56,243 - test_real_api_data - INFO - 🔧 Filling field 3 with component 2: FRC00000 (LABOUR COST)
2025-06-21 10:34:00,583 - test_real_api_data - INFO - ✅ Component 2 filled: FRC00000 (LABOUR COST)
2025-06-21 10:34:01,614 - test_real_api_data - INFO - 🔍 Autocomplete fields after filling component 2: 5
2025-06-21 10:34:01,614 - test_real_api_data - INFO - 🔧 Filling field 4 with component 3: L (LABOUR)
2025-06-21 10:34:05,962 - test_real_api_data - INFO - ✅ Component 3 filled: L (LABOUR)
2025-06-21 10:34:07,168 - test_real_api_data - INFO - 🔍 Autocomplete fields after filling component 3: 5
2025-06-21 10:34:07,168 - test_real_api_data - INFO - ✅ All charge job components filled successfully
2025-06-21 10:34:07,168 - test_real_api_data - INFO - ⏰ Step 5: Filling hours field with: 5.0
2025-06-21 10:34:07,168 - test_real_api_data - INFO - ⏰ Filling hours field with: 5.0
2025-06-21 10:34:07,188 - test_real_api_data - INFO - ✅ Hours field filled: 5.0
2025-06-21 10:34:07,190 - test_real_api_data - INFO - ✅ Hours field filled: 5.0
2025-06-21 10:34:07,261 - test_real_api_data - INFO - ✅ Found Add button with selector: input[value='Add']
2025-06-21 10:34:07,429 - test_real_api_data - INFO - ✅ Add button clicked - Record saved!
2025-06-21 10:34:13,452 - test_real_api_data - INFO - 🎯 Processing record 2: Abu Hurairoh
2025-06-21 10:34:13,452 - test_real_api_data - INFO - 📅 Date: 2025-05-30
2025-06-21 10:34:13,452 - test_real_api_data - INFO - 🔧 Raw charge job: (OC7110) FRUIT RECEPTION AND STORAGE / STN-FRC (STATION FRUIT RECEPTION) / FRC00000 (LABOUR COST) / L (LABOUR)
2025-06-21 10:34:13,452 - test_real_api_data - INFO - 🔘 Transaction type: Overtime
2025-06-21 10:34:13,452 - test_real_api_data - INFO - ⏰ Hours: 11.5
2025-06-21 10:34:13,452 - test_real_api_data - INFO - 📝 Entry type: overtime
2025-06-21 10:34:13,468 - test_real_api_data - INFO - 📅 Step 0: Filling document date field...
2025-06-21 10:34:13,468 - test_real_api_data - INFO - 📅 Document date calculated: Today=21/06/2025, Transaction=2025-05-30 -> Document=21/05/2025
2025-06-21 10:34:13,471 - test_real_api_data - INFO - 📅 Filling document date: 21/05/2025
2025-06-21 10:34:13,483 - test_real_api_data - INFO - ✅ Document date filled: 21/05/2025
2025-06-21 10:34:14,990 - test_real_api_data - INFO - ✅ Document date field filled successfully
2025-06-21 10:34:14,990 - test_real_api_data - INFO - 📅 Step 1: Filling transaction date: 30/05/2025
2025-06-21 10:34:15,025 - test_real_api_data - INFO - ✅ Date filled: 30/05/2025
2025-06-21 10:34:18,593 - test_real_api_data - INFO - 👤 Step 2: Filling employee: Abu Hurairoh
2025-06-21 10:34:18,798 - test_real_api_data - INFO - 🔍 Found 5 autocomplete fields
2025-06-21 10:34:23,636 - test_real_api_data - INFO - ✅ Employee filled: Abu Hurairoh
2025-06-21 10:34:23,636 - test_real_api_data - INFO - 🔘 Step 3: Selecting transaction type: Overtime
2025-06-21 10:34:23,636 - test_real_api_data - INFO - 🔘 Selecting transaction type: Overtime
2025-06-21 10:34:23,702 - test_real_api_data - INFO - ✅ Overtime transaction type selected
2025-06-21 10:34:24,704 - test_real_api_data - INFO - ✅ Transaction type selected: Overtime
2025-06-21 10:34:24,704 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-21 10:34:24,704 - test_real_api_data - INFO -    [0]: (OC7110) FRUIT RECEPTION AND STORAGE
2025-06-21 10:34:24,706 - test_real_api_data - INFO -    [1]: STN-FRC (STATION FRUIT RECEPTION)
2025-06-21 10:34:24,706 - test_real_api_data - INFO -    [2]: FRC00000 (LABOUR COST)
2025-06-21 10:34:24,706 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-21 10:34:24,706 - test_real_api_data - INFO - 🔧 Step 4: Filling 4 charge job components sequentially...
2025-06-21 10:34:25,345 - test_real_api_data - INFO - 🔍 Available autocomplete fields: 2
2025-06-21 10:34:25,347 - test_real_api_data - INFO - 🔧 Filling field 1 with component 0: (OC7110) FRUIT RECEPTION AND STORAGE
2025-06-21 10:34:29,923 - test_real_api_data - INFO - ✅ Component 0 filled: (OC7110) FRUIT RECEPTION AND STORAGE
2025-06-21 10:34:30,951 - test_real_api_data - INFO - 🔍 Autocomplete fields after filling component 0: 2
2025-06-21 10:34:30,951 - test_real_api_data - WARNING - ⚠️ No more autocomplete fields available for component 1: STN-FRC (STATION FRUIT RECEPTION)
2025-06-21 10:34:30,951 - test_real_api_data - INFO - ✅ All charge job components filled successfully
2025-06-21 10:34:30,951 - test_real_api_data - INFO - ⏰ Step 5: Filling hours field with: 11.5
2025-06-21 10:34:30,951 - test_real_api_data - INFO - ⏰ Filling hours field with: 11.5
2025-06-21 10:34:30,967 - test_real_api_data - INFO - ✅ Hours field filled: 11.5
2025-06-21 10:34:30,970 - test_real_api_data - INFO - ✅ Hours field filled: 11.5
2025-06-21 10:34:31,033 - test_real_api_data - INFO - ✅ Found Add button with selector: input[value='Add']
2025-06-21 10:34:34,144 - test_real_api_data - INFO - ✅ Add button clicked - Record saved!
2025-06-21 12:20:42,510 - src.core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-21 12:20:42,545 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)': /session/730a00f453a13f500aa5d34302552a49
2025-06-23 00:11:24,180 - test_real_api_data - INFO - 🚀 Initializing browser...
2025-06-23 00:11:24,181 - src.core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-23 00:11:24,181 - src.core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-23 00:11:24,278 - src.core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-23 00:11:24,343 - src.core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-23 00:11:24,344 - src.core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-23 00:11:24,348 - WDM - INFO - ====== WebDriver manager ======
2025-06-23 00:11:25,472 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-23 00:11:25,616 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-23 00:11:25,722 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-23 00:11:25,973 - WDM - INFO - WebDriver version 137.0.7151.119 selected
2025-06-23 00:11:25,973 - WDM - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.119/win32/chromedriver-win32.zip
2025-06-23 00:11:25,973 - WDM - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.119/win32/chromedriver-win32.zip
2025-06-23 00:11:26,068 - WDM - INFO - Driver downloading response is 200
2025-06-23 00:11:28,126 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-23 00:11:28,791 - WDM - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119]
2025-06-23 00:11:28,791 - src.core.browser_manager - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-23 00:11:28,793 - src.core.browser_manager - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-23 00:11:28,793 - src.core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-23 00:11:28,793 - src.core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-23 00:11:30,594 - src.core.browser_manager - INFO - ✅ WebDriver created and responsive
2025-06-23 00:11:30,858 - src.core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-23 00:11:30,895 - src.core.persistent_browser_manager - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-23 00:11:36,530 - src.core.persistent_browser_manager - INFO - WebDriver created and page loaded successfully
2025-06-23 00:11:36,530 - src.core.persistent_browser_manager - INFO - Performing initial login...
2025-06-23 00:11:47,930 - src.core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-23 00:11:54,370 - src.core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-23 00:11:54,371 - src.core.persistent_browser_manager - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-23 00:11:54,372 - src.core.persistent_browser_manager - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:11:57,369 - src.core.persistent_browser_manager - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:11:57,369 - src.core.persistent_browser_manager - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-23 00:11:57,378 - src.core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-23 00:11:57,378 - src.core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-23 00:11:57,378 - src.core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-23 00:11:57,378 - src.core.persistent_browser_manager - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:11:57,404 - src.core.persistent_browser_manager - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:12:00,086 - src.core.persistent_browser_manager - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:12:00,086 - src.core.persistent_browser_manager - INFO - ✅ SUCCESS: Reached task register page!
2025-06-23 00:12:00,086 - test_real_api_data - INFO - ✅ Browser initialized and ready
2025-06-23 00:12:00,400 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-23 00:12:00,400 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 00:12:03,070 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 00:12:03] "GET / HTTP/1.1" 200 -
2025-06-23 00:12:05,441 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 00:12:05] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-23 00:12:08,912 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 00:12:08] "GET /api/employees HTTP/1.1" 200 -
2025-06-23 00:12:09,100 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 00:12:09] "GET /api/staging/data?status=staged HTTP/1.1" 200 -
2025-06-23 00:13:45,660 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 00:13:45] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-23 00:13:45,666 - test_real_api_data - INFO - 🌐 Fetching data from: http://localhost:5173/api/staging/data
2025-06-23 00:13:50,877 - test_real_api_data - INFO - ✅ API response received
2025-06-23 00:13:50,877 - test_real_api_data - INFO - ✅ Fetched 60 records from API
2025-06-23 00:13:50,880 - test_real_api_data - INFO - 📋 First record employee: Aan Kris Wanda
2025-06-23 00:13:50,880 - test_real_api_data - INFO - 📋 First record date: 2025-06-29
2025-06-23 00:13:50,880 - test_real_api_data - INFO - 📋 First record raw_charge_job: (OC7120) STERILIZER OPERATION / STN-STR (STATION STERILIZER) / STR00000 (LABOUR COST) / L (LABOUR)
2025-06-23 00:13:50,880 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 00:13:50,880 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 00:13:50,880 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 00:13:50,880 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 00:13:50,885 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 00:13:50,887 - test_real_api_data - INFO - ✅ Created normal entry: 7.0 hours
2025-06-23 00:13:50,887 - test_real_api_data - INFO - ✅ Created overtime entry: 2.5 hours
2025-06-23 00:13:50,887 - test_real_api_data - INFO - ✅ Created normal entry: 7.0 hours
2025-06-23 00:13:50,887 - test_real_api_data - INFO - ✅ Created overtime entry: 2.5 hours
2025-06-23 00:13:50,896 - test_real_api_data - INFO - 🎯 Processing record 1: Aan Kris Wanda
2025-06-23 00:13:50,896 - test_real_api_data - INFO - 📅 Date: 2025-06-10
2025-06-23 00:13:50,899 - test_real_api_data - INFO - 🔧 Raw charge job: (OC7120) STERILIZER OPERATION / STN-STR (STATION STERILIZER) / STR00000 (LABOUR COST) / L (LABOUR)
2025-06-23 00:13:50,899 - test_real_api_data - INFO - 🔘 Transaction type: Normal
2025-06-23 00:13:50,899 - test_real_api_data - INFO - ⏰ Hours: 7.0
2025-06-23 00:13:50,899 - test_real_api_data - INFO - 📝 Entry type: normal
2025-06-23 00:13:50,908 - test_real_api_data - INFO - 📅 Step 0: Filling document date field...
2025-06-23 00:13:50,908 - test_real_api_data - INFO - 📅 Document date calculated: Today=23/06/2025, Transaction=2025-06-10 -> Document=23/06/2025
2025-06-23 00:13:50,908 - test_real_api_data - INFO - 📅 Filling document date: 23/06/2025
2025-06-23 00:13:50,919 - test_real_api_data - INFO - ✅ Document date filled: 23/06/2025
2025-06-23 00:13:52,439 - test_real_api_data - INFO - ✅ Document date field filled successfully
2025-06-23 00:13:52,439 - test_real_api_data - INFO - 📅 Step 1: Filling transaction date: 10/06/2025
2025-06-23 00:13:52,448 - test_real_api_data - INFO - ✅ Date filled: 10/06/2025
2025-06-23 00:13:52,858 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 00:13:52] "GET /api/staging/data?status=staged HTTP/1.1" 200 -
2025-06-23 00:13:54,963 - test_real_api_data - INFO - 👤 Step 2: Filling employee: Aan Kris Wanda
2025-06-23 00:13:55,006 - test_real_api_data - INFO - 🔍 Found 2 autocomplete fields
2025-06-23 00:13:59,553 - test_real_api_data - INFO - ✅ Employee filled: Aan Kris Wanda
2025-06-23 00:13:59,553 - test_real_api_data - INFO - 🔘 Step 3: Selecting transaction type: Normal
2025-06-23 00:13:59,553 - test_real_api_data - INFO - 🔘 Selecting transaction type: Normal
2025-06-23 00:13:59,562 - test_real_api_data - INFO - ✅ Normal transaction type selected
2025-06-23 00:14:00,570 - test_real_api_data - INFO - ✅ Transaction type selected: Normal
2025-06-23 00:14:00,570 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 00:14:00,570 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 00:14:00,570 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 00:14:00,570 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 00:14:00,570 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 00:14:00,570 - test_real_api_data - INFO - 🔧 Step 4: Filling 4 charge job components sequentially...
2025-06-23 00:14:00,584 - test_real_api_data - INFO - 🔍 Available autocomplete fields: 2
2025-06-23 00:14:00,584 - test_real_api_data - INFO - 🔧 Filling field 1 with component 0: (OC7120) STERILIZER OPERATION
2025-06-23 00:14:04,705 - test_real_api_data - INFO - ✅ Component 0 filled: (OC7120) STERILIZER OPERATION
2025-06-23 00:14:05,738 - test_real_api_data - INFO - 🔍 Autocomplete fields after filling component 0: 2
2025-06-23 00:14:05,738 - test_real_api_data - WARNING - ⚠️ No more autocomplete fields available for component 1: STN-STR (STATION STERILIZER)
2025-06-23 00:14:05,738 - test_real_api_data - INFO - ✅ All charge job components filled successfully
2025-06-23 00:14:05,738 - test_real_api_data - INFO - ⏰ Step 5: Filling hours field with: 7.0
2025-06-23 00:14:05,746 - test_real_api_data - INFO - ⏰ Filling hours field with: 7.0
2025-06-23 00:14:05,753 - test_real_api_data - INFO - ✅ Hours field filled: 7.0
2025-06-23 00:14:05,753 - test_real_api_data - INFO - ✅ Hours field filled: 7.0
2025-06-23 00:14:05,798 - test_real_api_data - INFO - ✅ Found Add button with selector: input[value='Add']
2025-06-23 00:14:05,889 - test_real_api_data - INFO - ✅ Add button clicked - Record saved!
2025-06-23 00:14:11,908 - test_real_api_data - INFO - 🎯 Processing record 2: Aan Kris Wanda
2025-06-23 00:14:11,908 - test_real_api_data - INFO - 📅 Date: 2025-06-10
2025-06-23 00:14:11,909 - test_real_api_data - INFO - 🔧 Raw charge job: (OC7120) STERILIZER OPERATION / STN-STR (STATION STERILIZER) / STR00000 (LABOUR COST) / L (LABOUR)
2025-06-23 00:14:11,909 - test_real_api_data - INFO - 🔘 Transaction type: Overtime
2025-06-23 00:14:11,909 - test_real_api_data - INFO - ⏰ Hours: 2.5
2025-06-23 00:14:11,910 - test_real_api_data - INFO - 📝 Entry type: overtime
2025-06-23 00:14:11,918 - test_real_api_data - INFO - 📅 Step 0: Filling document date field...
2025-06-23 00:14:11,918 - test_real_api_data - INFO - 📅 Document date calculated: Today=23/06/2025, Transaction=2025-06-10 -> Document=23/06/2025
2025-06-23 00:14:11,918 - test_real_api_data - INFO - 📅 Filling document date: 23/06/2025
2025-06-23 00:14:11,932 - test_real_api_data - INFO - ✅ Document date filled: 23/06/2025
2025-06-23 00:14:13,440 - test_real_api_data - INFO - ✅ Document date field filled successfully
2025-06-23 00:14:13,440 - test_real_api_data - INFO - 📅 Step 1: Filling transaction date: 10/06/2025
2025-06-23 00:14:13,450 - test_real_api_data - INFO - ✅ Date filled: 10/06/2025
2025-06-23 00:14:15,881 - test_real_api_data - INFO - 👤 Step 2: Filling employee: Aan Kris Wanda
2025-06-23 00:14:15,906 - test_real_api_data - INFO - 🔍 Found 2 autocomplete fields
2025-06-23 00:14:20,428 - test_real_api_data - INFO - ✅ Employee filled: Aan Kris Wanda
2025-06-23 00:14:20,429 - test_real_api_data - INFO - 🔘 Step 3: Selecting transaction type: Overtime
2025-06-23 00:14:20,429 - test_real_api_data - INFO - 🔘 Selecting transaction type: Overtime
2025-06-23 00:14:20,436 - test_real_api_data - INFO - ✅ Overtime transaction type selected
2025-06-23 00:14:21,438 - test_real_api_data - INFO - ✅ Transaction type selected: Overtime
2025-06-23 00:14:21,438 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 00:14:21,439 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 00:14:21,439 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 00:14:21,439 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 00:14:21,439 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 00:14:21,439 - test_real_api_data - INFO - 🔧 Step 4: Filling 4 charge job components sequentially...
2025-06-23 00:14:21,467 - test_real_api_data - INFO - 🔍 Available autocomplete fields: 2
2025-06-23 00:14:21,467 - test_real_api_data - INFO - 🔧 Filling field 1 with component 0: (OC7120) STERILIZER OPERATION
2025-06-23 00:14:25,497 - test_real_api_data - INFO - ✅ Component 0 filled: (OC7120) STERILIZER OPERATION
2025-06-23 00:14:26,514 - test_real_api_data - INFO - 🔍 Autocomplete fields after filling component 0: 2
2025-06-23 00:14:26,514 - test_real_api_data - WARNING - ⚠️ No more autocomplete fields available for component 1: STN-STR (STATION STERILIZER)
2025-06-23 00:14:26,514 - test_real_api_data - INFO - ✅ All charge job components filled successfully
2025-06-23 00:14:26,530 - test_real_api_data - INFO - ⏰ Step 5: Filling hours field with: 2.5
2025-06-23 00:14:26,530 - test_real_api_data - INFO - ⏰ Filling hours field with: 2.5
2025-06-23 00:14:26,539 - test_real_api_data - INFO - ✅ Hours field filled: 2.5
2025-06-23 00:14:26,539 - test_real_api_data - INFO - ✅ Hours field filled: 2.5
2025-06-23 00:14:26,584 - test_real_api_data - INFO - ✅ Found Add button with selector: input[value='Add']
2025-06-23 00:14:26,651 - test_real_api_data - INFO - ✅ Add button clicked - Record saved!
2025-06-23 00:15:27,038 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 00:15:27] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-23 00:15:27,038 - test_real_api_data - INFO - 🌐 Fetching data from: http://localhost:5173/api/staging/data
2025-06-23 00:15:32,565 - test_real_api_data - INFO - ✅ API response received
2025-06-23 00:15:32,565 - test_real_api_data - INFO - ✅ Fetched 60 records from API
2025-06-23 00:15:32,565 - test_real_api_data - INFO - 📋 First record employee: Aan Kris Wanda
2025-06-23 00:15:32,565 - test_real_api_data - INFO - 📋 First record date: 2025-06-29
2025-06-23 00:15:32,565 - test_real_api_data - INFO - 📋 First record raw_charge_job: (OC7120) STERILIZER OPERATION / STN-STR (STATION STERILIZER) / STR00000 (LABOUR COST) / L (LABOUR)
2025-06-23 00:15:32,571 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 00:15:32,571 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 00:15:32,571 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 00:15:32,571 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 00:15:32,571 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 00:15:32,571 - test_real_api_data - INFO - ✅ Created normal entry: 7.0 hours
2025-06-23 00:15:32,571 - test_real_api_data - INFO - ✅ Created overtime entry: 2.5 hours
2025-06-23 00:15:32,571 - test_real_api_data - INFO - ✅ Created normal entry: 7.0 hours
2025-06-23 00:15:32,571 - test_real_api_data - INFO - ✅ Created overtime entry: 2.5 hours
2025-06-23 00:15:32,589 - test_real_api_data - INFO - 🎯 Processing record 1: Aan Kris Wanda
2025-06-23 00:15:32,589 - test_real_api_data - INFO - 📅 Date: 2025-06-10
2025-06-23 00:15:32,589 - test_real_api_data - INFO - 🔧 Raw charge job: (OC7120) STERILIZER OPERATION / STN-STR (STATION STERILIZER) / STR00000 (LABOUR COST) / L (LABOUR)
2025-06-23 00:15:32,589 - test_real_api_data - INFO - 🔘 Transaction type: Normal
2025-06-23 00:15:32,589 - test_real_api_data - INFO - ⏰ Hours: 7.0
2025-06-23 00:15:32,589 - test_real_api_data - INFO - 📝 Entry type: normal
2025-06-23 00:15:32,599 - test_real_api_data - INFO - 📅 Step 0: Filling document date field...
2025-06-23 00:15:32,599 - test_real_api_data - INFO - 📅 Document date calculated: Today=23/06/2025, Transaction=2025-06-10 -> Document=23/06/2025
2025-06-23 00:15:32,599 - test_real_api_data - INFO - 📅 Filling document date: 23/06/2025
2025-06-23 00:15:32,616 - test_real_api_data - INFO - ✅ Document date filled: 23/06/2025
2025-06-23 00:15:34,110 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 00:15:34] "GET /api/staging/data?status=staged HTTP/1.1" 200 -
2025-06-23 00:15:34,131 - test_real_api_data - INFO - ✅ Document date field filled successfully
2025-06-23 00:15:34,131 - test_real_api_data - INFO - 📅 Step 1: Filling transaction date: 10/06/2025
2025-06-23 00:15:34,142 - test_real_api_data - INFO - ✅ Date filled: 10/06/2025
2025-06-23 00:15:36,579 - test_real_api_data - INFO - 👤 Step 2: Filling employee: Aan Kris Wanda
2025-06-23 00:15:36,595 - test_real_api_data - INFO - 🔍 Found 2 autocomplete fields
2025-06-23 00:15:41,123 - test_real_api_data - INFO - ✅ Employee filled: Aan Kris Wanda
2025-06-23 00:15:41,124 - test_real_api_data - INFO - 🔘 Step 3: Selecting transaction type: Normal
2025-06-23 00:15:41,124 - test_real_api_data - INFO - 🔘 Selecting transaction type: Normal
2025-06-23 00:15:41,131 - test_real_api_data - INFO - ✅ Normal transaction type selected
2025-06-23 00:15:42,135 - test_real_api_data - INFO - ✅ Transaction type selected: Normal
2025-06-23 00:15:42,135 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 00:15:42,135 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 00:15:42,135 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 00:15:42,135 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 00:15:42,135 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 00:15:42,135 - test_real_api_data - INFO - 🔧 Step 4: Filling 4 charge job components sequentially...
2025-06-23 00:15:42,159 - test_real_api_data - INFO - 🔍 Available autocomplete fields: 2
2025-06-23 00:15:42,159 - test_real_api_data - INFO - 🔧 Filling field 1 with component 0: (OC7120) STERILIZER OPERATION
2025-06-23 00:15:46,239 - test_real_api_data - INFO - ✅ Component 0 filled: (OC7120) STERILIZER OPERATION
2025-06-23 00:15:47,268 - test_real_api_data - INFO - 🔍 Autocomplete fields after filling component 0: 2
2025-06-23 00:15:47,268 - test_real_api_data - WARNING - ⚠️ No more autocomplete fields available for component 1: STN-STR (STATION STERILIZER)
2025-06-23 00:15:47,268 - test_real_api_data - INFO - ✅ All charge job components filled successfully
2025-06-23 00:15:47,268 - test_real_api_data - INFO - ⏰ Step 5: Filling hours field with: 7.0
2025-06-23 00:15:47,268 - test_real_api_data - INFO - ⏰ Filling hours field with: 7.0
2025-06-23 00:15:47,283 - test_real_api_data - INFO - ✅ Hours field filled: 7.0
2025-06-23 00:15:47,283 - test_real_api_data - INFO - ✅ Hours field filled: 7.0
2025-06-23 00:15:47,318 - test_real_api_data - INFO - ✅ Found Add button with selector: input[value='Add']
2025-06-23 00:15:47,391 - test_real_api_data - INFO - ✅ Add button clicked - Record saved!
2025-06-23 00:15:53,409 - test_real_api_data - INFO - 🎯 Processing record 2: Aan Kris Wanda
2025-06-23 00:15:53,409 - test_real_api_data - INFO - 📅 Date: 2025-06-10
2025-06-23 00:15:53,409 - test_real_api_data - INFO - 🔧 Raw charge job: (OC7120) STERILIZER OPERATION / STN-STR (STATION STERILIZER) / STR00000 (LABOUR COST) / L (LABOUR)
2025-06-23 00:15:53,409 - test_real_api_data - INFO - 🔘 Transaction type: Overtime
2025-06-23 00:15:53,410 - test_real_api_data - INFO - ⏰ Hours: 2.5
2025-06-23 00:15:53,410 - test_real_api_data - INFO - 📝 Entry type: overtime
2025-06-23 00:15:53,417 - test_real_api_data - INFO - 📅 Step 0: Filling document date field...
2025-06-23 00:15:53,417 - test_real_api_data - INFO - 📅 Document date calculated: Today=23/06/2025, Transaction=2025-06-10 -> Document=23/06/2025
2025-06-23 00:15:53,417 - test_real_api_data - INFO - 📅 Filling document date: 23/06/2025
2025-06-23 00:15:53,425 - test_real_api_data - INFO - ✅ Document date filled: 23/06/2025
2025-06-23 00:15:54,933 - test_real_api_data - INFO - ✅ Document date field filled successfully
2025-06-23 00:15:54,933 - test_real_api_data - INFO - 📅 Step 1: Filling transaction date: 10/06/2025
2025-06-23 00:15:54,958 - test_real_api_data - INFO - ✅ Date filled: 10/06/2025
2025-06-23 00:15:57,393 - test_real_api_data - INFO - 👤 Step 2: Filling employee: Aan Kris Wanda
2025-06-23 00:15:57,417 - test_real_api_data - INFO - 🔍 Found 2 autocomplete fields
2025-06-23 00:16:01,953 - test_real_api_data - INFO - ✅ Employee filled: Aan Kris Wanda
2025-06-23 00:16:01,953 - test_real_api_data - INFO - 🔘 Step 3: Selecting transaction type: Overtime
2025-06-23 00:16:01,953 - test_real_api_data - INFO - 🔘 Selecting transaction type: Overtime
2025-06-23 00:16:01,961 - test_real_api_data - INFO - ✅ Overtime transaction type selected
2025-06-23 00:16:02,962 - test_real_api_data - INFO - ✅ Transaction type selected: Overtime
2025-06-23 00:16:02,962 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 00:16:02,962 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 00:16:02,962 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 00:16:02,962 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 00:16:02,962 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 00:16:02,962 - test_real_api_data - INFO - 🔧 Step 4: Filling 4 charge job components sequentially...
2025-06-23 00:16:03,005 - test_real_api_data - INFO - 🔍 Available autocomplete fields: 2
2025-06-23 00:16:03,005 - test_real_api_data - INFO - 🔧 Filling field 1 with component 0: (OC7120) STERILIZER OPERATION
2025-06-23 00:16:07,110 - test_real_api_data - INFO - ✅ Component 0 filled: (OC7120) STERILIZER OPERATION
2025-06-23 00:16:08,138 - test_real_api_data - INFO - 🔍 Autocomplete fields after filling component 0: 2
2025-06-23 00:16:08,139 - test_real_api_data - WARNING - ⚠️ No more autocomplete fields available for component 1: STN-STR (STATION STERILIZER)
2025-06-23 00:16:08,139 - test_real_api_data - INFO - ✅ All charge job components filled successfully
2025-06-23 00:16:08,139 - test_real_api_data - INFO - ⏰ Step 5: Filling hours field with: 2.5
2025-06-23 00:16:08,140 - test_real_api_data - INFO - ⏰ Filling hours field with: 2.5
2025-06-23 00:16:08,147 - test_real_api_data - INFO - ✅ Hours field filled: 2.5
2025-06-23 00:16:08,147 - test_real_api_data - INFO - ✅ Hours field filled: 2.5
2025-06-23 00:16:08,187 - test_real_api_data - INFO - ✅ Found Add button with selector: input[value='Add']
2025-06-23 00:16:08,274 - test_real_api_data - INFO - ✅ Add button clicked - Record saved!
2025-06-23 00:37:12,777 - src.core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-23 00:37:16,824 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002877CC87500>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/008b2d41cb0f30a08e21bc3c63e45f0e
2025-06-23 00:37:20,881 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002877CC87710>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/008b2d41cb0f30a08e21bc3c63e45f0e
2025-06-23 00:37:33,454 - test_real_api_data - INFO - 🚀 Initializing browser...
2025-06-23 00:37:33,455 - src.core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-23 00:37:33,455 - src.core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-23 00:37:33,609 - src.core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-23 00:37:33,728 - src.core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-23 00:37:33,728 - src.core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-23 00:37:33,730 - WDM - INFO - ====== WebDriver manager ======
2025-06-23 00:37:34,783 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-23 00:37:34,894 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-23 00:37:35,001 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-23 00:37:35,001 - src.core.browser_manager - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-23 00:37:35,001 - src.core.browser_manager - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-23 00:37:35,001 - src.core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-23 00:37:35,001 - src.core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-23 00:37:36,702 - src.core.browser_manager - INFO - ✅ WebDriver created and responsive
2025-06-23 00:37:36,776 - src.core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-23 00:37:36,783 - src.core.persistent_browser_manager - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-23 00:37:41,969 - src.core.persistent_browser_manager - INFO - WebDriver created and page loaded successfully
2025-06-23 00:37:41,969 - src.core.persistent_browser_manager - INFO - Performing initial login...
2025-06-23 00:37:55,509 - src.core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-23 00:37:57,520 - src.core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-23 00:37:57,520 - src.core.persistent_browser_manager - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-23 00:37:57,520 - src.core.persistent_browser_manager - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:38:00,124 - src.core.persistent_browser_manager - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:38:00,124 - src.core.persistent_browser_manager - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-23 00:38:00,131 - src.core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-23 00:38:00,131 - src.core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-23 00:38:00,131 - src.core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-23 00:38:00,131 - src.core.persistent_browser_manager - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:38:00,156 - src.core.persistent_browser_manager - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:38:02,836 - src.core.persistent_browser_manager - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:38:02,836 - src.core.persistent_browser_manager - INFO - ✅ SUCCESS: Reached task register page!
2025-06-23 00:38:02,836 - test_real_api_data - INFO - ✅ Browser initialized and ready
2025-06-23 00:38:03,153 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-23 00:38:03,153 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 00:38:05,727 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 00:38:05] "GET / HTTP/1.1" 200 -
2025-06-23 00:38:11,311 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 00:38:11] "GET /api/employees HTTP/1.1" 200 -
2025-06-23 00:38:13,454 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 00:38:13] "GET /api/staging/data?status=staged HTTP/1.1" 200 -
2025-06-23 00:38:43,995 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 00:38:43] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-23 00:38:43,995 - test_real_api_data - INFO - 🌐 Fetching data from: http://localhost:5173/api/staging/data
2025-06-23 00:38:49,062 - test_real_api_data - INFO - ✅ API response received
2025-06-23 00:38:49,065 - test_real_api_data - INFO - ✅ Fetched 60 records from API
2025-06-23 00:38:49,066 - test_real_api_data - INFO - 📋 First record employee: Aan Kris Wanda
2025-06-23 00:38:49,066 - test_real_api_data - INFO - 📋 First record date: 2025-06-29
2025-06-23 00:38:49,066 - test_real_api_data - INFO - 📋 First record raw_charge_job: (OC7120) STERILIZER OPERATION / STN-STR (STATION STERILIZER) / STR00000 (LABOUR COST) / L (LABOUR)
2025-06-23 00:38:49,070 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 00:38:49,070 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 00:38:49,070 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 00:38:49,070 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 00:38:49,070 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 00:38:49,070 - test_real_api_data - INFO - ✅ Created normal entry: 7.0 hours
2025-06-23 00:38:49,070 - test_real_api_data - INFO - ✅ Created overtime entry: 2.5 hours
2025-06-23 00:38:49,074 - test_real_api_data - INFO - ✅ Created normal entry: 7.0 hours
2025-06-23 00:38:49,074 - test_real_api_data - INFO - ✅ Created overtime entry: 2.5 hours
2025-06-23 00:38:49,087 - __main__ - INFO - 📅 Weekday detected: Using 7 hours for regular entry
2025-06-23 00:38:49,087 - __main__ - INFO - 🎯 Processing record 1: Aan Kris Wanda
2025-06-23 00:38:49,087 - __main__ - INFO - 📅 Date: 2025-06-01
2025-06-23 00:38:49,087 - __main__ - INFO - 🔘 Transaction type: Normal
2025-06-23 00:38:49,087 - __main__ - INFO - ⏰ Calculated hours: 7.0
2025-06-23 00:38:49,087 - test_real_api_data - INFO - 📅 Document date calculated: Today=23/06/2025, Transaction=2025-06-01 -> Document=23/06/2025
2025-06-23 00:38:49,087 - __main__ - INFO - 📅 Step 0: Filling document date: 23/06/2025
2025-06-23 00:38:49,104 - __main__ - INFO - ✅ Document date filled: 23/06/2025
2025-06-23 00:38:50,610 - __main__ - INFO - 📅 Step 1: Filling transaction date: 01/06/2025
2025-06-23 00:38:50,619 - __main__ - INFO - ✅ Date filled: 01/06/2025
2025-06-23 00:38:51,308 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 00:38:51] "GET /api/staging/data?status=staged HTTP/1.1" 200 -
2025-06-23 00:38:53,156 - __main__ - INFO - 👤 Step 2: Filling employee using smart autocomplete: Aan Kris Wanda
2025-06-23 00:38:56,214 - __main__ - INFO - ✅ Employee: Found single option after typing 'Aa', selecting first option
2025-06-23 00:38:57,791 - __main__ - INFO - ✅ Employee filled using smart autocomplete: Aan Kris Wanda
2025-06-23 00:38:59,792 - __main__ - INFO - 🔘 Step 3: Selecting transaction type: Normal
2025-06-23 00:38:59,792 - test_real_api_data - INFO - 🔘 Selecting transaction type: Normal
2025-06-23 00:38:59,816 - test_real_api_data - INFO - ✅ Normal transaction type selected
2025-06-23 00:39:00,829 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 00:39:00,830 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 00:39:00,830 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 00:39:00,830 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 00:39:00,830 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 00:39:00,830 - __main__ - INFO - 🔧 Step 4: Filling 4 charge job components using smart autocomplete
2025-06-23 00:39:04,807 - __main__ - INFO - ✅ Charge Component 1: Found single option after typing '(OC712', selecting first option
2025-06-23 00:39:06,399 - __main__ - INFO - ✅ Charge Component 1 filled using smart autocomplete: (OC7120) STERILIZER OPERATION
2025-06-23 00:39:08,497 - __main__ - INFO - ✅ Charge Component 2: Found single option after typing 'S', selecting first option
2025-06-23 00:39:10,096 - __main__ - INFO - ✅ Charge Component 2 filled using smart autocomplete: STN-STR (STATION STERILIZER)
2025-06-23 00:39:17,551 - __main__ - INFO - ✅ Charge Component 3: Found single option after typing 'STR00000', selecting first option
2025-06-23 00:39:19,147 - __main__ - INFO - ✅ Charge Component 3 filled using smart autocomplete: STR00000 (LABOUR COST)
2025-06-23 00:39:21,140 - __main__ - INFO - ✅ Charge Component 4: Found single option after typing 'L', selecting first option
2025-06-23 00:39:22,736 - __main__ - INFO - ✅ Charge Component 4 filled using smart autocomplete: L (LABOUR)
2025-06-23 00:39:23,810 - __main__ - INFO - ⏰ Step 5: Filling calculated hours: 7.0
2025-06-23 00:39:23,810 - test_real_api_data - INFO - ⏰ Filling hours field with: 7.0
2025-06-23 00:39:23,817 - test_real_api_data - INFO - ✅ Hours field filled: 7.0
2025-06-23 00:39:23,936 - __main__ - INFO - ✅ Add button clicked - Record saved!
2025-06-23 00:39:29,955 - __main__ - INFO - ⏰ Overtime hours from API: 2.5
2025-06-23 00:39:29,967 - __main__ - INFO - 🎯 Processing record 2: Aan Kris Wanda
2025-06-23 00:39:29,967 - __main__ - INFO - 📅 Date: 2025-06-01
2025-06-23 00:39:29,967 - __main__ - INFO - 🔘 Transaction type: Overtime
2025-06-23 00:39:29,969 - __main__ - INFO - ⏰ Calculated hours: 2.5
2025-06-23 00:39:29,969 - test_real_api_data - INFO - 📅 Document date calculated: Today=23/06/2025, Transaction=2025-06-01 -> Document=23/06/2025
2025-06-23 00:39:29,969 - __main__ - INFO - 📅 Step 0: Filling document date: 23/06/2025
2025-06-23 00:39:30,229 - __main__ - INFO - ✅ Document date filled: 23/06/2025
2025-06-23 00:39:31,730 - __main__ - INFO - 📅 Step 1: Filling transaction date: 01/06/2025
2025-06-23 00:39:32,811 - __main__ - INFO - ✅ Date filled: 01/06/2025
2025-06-23 00:39:35,173 - __main__ - INFO - 👤 Step 2: Filling employee using smart autocomplete: Aan Kris Wanda
2025-06-23 00:39:38,137 - __main__ - INFO - ✅ Employee: Found single option after typing 'Aa', selecting first option
2025-06-23 00:39:39,734 - __main__ - INFO - ✅ Employee filled using smart autocomplete: Aan Kris Wanda
2025-06-23 00:39:41,734 - __main__ - INFO - 🔘 Step 3: Selecting transaction type: Overtime
2025-06-23 00:39:41,734 - test_real_api_data - INFO - 🔘 Selecting transaction type: Overtime
2025-06-23 00:39:41,817 - test_real_api_data - INFO - ✅ Overtime transaction type selected
2025-06-23 00:39:42,821 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 00:39:42,821 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 00:39:42,821 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 00:39:42,821 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 00:39:42,821 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 00:39:42,821 - __main__ - INFO - 🔧 Step 4: Filling 4 charge job components using smart autocomplete
2025-06-23 00:39:46,661 - __main__ - INFO - ✅ Charge Component 1: Found single option after typing '(OC712', selecting first option
2025-06-23 00:39:48,240 - __main__ - INFO - ✅ Charge Component 1 filled using smart autocomplete: (OC7120) STERILIZER OPERATION
2025-06-23 00:39:49,252 - __main__ - WARNING - ⚠️ No more fields for component 1: STN-STR (STATION STERILIZER)
2025-06-23 00:39:49,254 - __main__ - INFO - ⏰ Step 5: Filling calculated hours: 2.5
2025-06-23 00:39:49,254 - test_real_api_data - INFO - ⏰ Filling hours field with: 2.5
2025-06-23 00:39:49,260 - test_real_api_data - INFO - ✅ Hours field filled: 2.5
2025-06-23 00:39:49,382 - __main__ - INFO - ✅ Add button clicked - Record saved!
2025-06-23 00:40:26,742 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 00:40:26] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-23 00:40:26,742 - test_real_api_data - INFO - 🌐 Fetching data from: http://localhost:5173/api/staging/data
2025-06-23 00:40:31,663 - test_real_api_data - INFO - ✅ API response received
2025-06-23 00:40:31,663 - test_real_api_data - INFO - ✅ Fetched 60 records from API
2025-06-23 00:40:31,666 - test_real_api_data - INFO - 📋 First record employee: Aan Kris Wanda
2025-06-23 00:40:31,666 - test_real_api_data - INFO - 📋 First record date: 2025-06-29
2025-06-23 00:40:31,666 - test_real_api_data - INFO - 📋 First record raw_charge_job: (OC7120) STERILIZER OPERATION / STN-STR (STATION STERILIZER) / STR00000 (LABOUR COST) / L (LABOUR)
2025-06-23 00:40:31,667 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 00:40:31,667 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 00:40:31,667 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 00:40:31,667 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 00:40:31,667 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 00:40:31,667 - test_real_api_data - INFO - ✅ Created normal entry: 7.0 hours
2025-06-23 00:40:31,667 - test_real_api_data - INFO - ✅ Created overtime entry: 2.5 hours
2025-06-23 00:40:31,667 - test_real_api_data - INFO - ✅ Created normal entry: 7.0 hours
2025-06-23 00:40:31,675 - test_real_api_data - INFO - ✅ Created overtime entry: 2.5 hours
2025-06-23 00:40:31,686 - __main__ - INFO - 📅 Weekday detected: Using 7 hours for regular entry
2025-06-23 00:40:31,686 - __main__ - INFO - 🎯 Processing record 1: Aan Kris Wanda
2025-06-23 00:40:31,686 - __main__ - INFO - 📅 Date: 2025-06-01
2025-06-23 00:40:31,686 - __main__ - INFO - 🔘 Transaction type: Normal
2025-06-23 00:40:31,686 - __main__ - INFO - ⏰ Calculated hours: 7.0
2025-06-23 00:40:31,686 - test_real_api_data - INFO - 📅 Document date calculated: Today=23/06/2025, Transaction=2025-06-01 -> Document=23/06/2025
2025-06-23 00:40:31,686 - __main__ - INFO - 📅 Step 0: Filling document date: 23/06/2025
2025-06-23 00:40:31,702 - __main__ - INFO - ✅ Document date filled: 23/06/2025
2025-06-23 00:40:33,208 - __main__ - INFO - 📅 Step 1: Filling transaction date: 01/06/2025
2025-06-23 00:40:33,238 - __main__ - INFO - ✅ Date filled: 01/06/2025
2025-06-23 00:40:34,492 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 00:40:34] "GET /api/staging/data?status=staged HTTP/1.1" 200 -
2025-06-23 00:40:36,020 - __main__ - INFO - 👤 Step 2: Filling employee using smart autocomplete: Aan Kris Wanda
2025-06-23 00:40:38,980 - __main__ - INFO - ✅ Employee: Found single option after typing 'Aa', selecting first option
2025-06-23 00:40:40,561 - __main__ - INFO - ✅ Employee filled using smart autocomplete: Aan Kris Wanda
2025-06-23 00:40:42,561 - __main__ - INFO - 🔘 Step 3: Selecting transaction type: Normal
2025-06-23 00:40:42,561 - test_real_api_data - INFO - 🔘 Selecting transaction type: Normal
2025-06-23 00:40:42,653 - test_real_api_data - INFO - ✅ Normal transaction type selected
2025-06-23 00:40:43,660 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 00:40:43,660 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 00:40:43,660 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 00:40:43,660 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 00:40:43,660 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 00:40:43,660 - __main__ - INFO - 🔧 Step 4: Filling 4 charge job components using smart autocomplete
2025-06-23 00:40:47,547 - __main__ - INFO - ✅ Charge Component 1: Found single option after typing '(OC712', selecting first option
2025-06-23 00:40:49,135 - __main__ - INFO - ✅ Charge Component 1 filled using smart autocomplete: (OC7120) STERILIZER OPERATION
2025-06-23 00:40:51,170 - __main__ - INFO - ✅ Charge Component 2: Found single option after typing 'S', selecting first option
2025-06-23 00:40:52,750 - __main__ - INFO - ✅ Charge Component 2 filled using smart autocomplete: STN-STR (STATION STERILIZER)
2025-06-23 00:41:00,312 - __main__ - INFO - ✅ Charge Component 3: Found single option after typing 'STR00000', selecting first option
2025-06-23 00:41:01,908 - __main__ - INFO - ✅ Charge Component 3 filled using smart autocomplete: STR00000 (LABOUR COST)
2025-06-23 00:41:04,007 - __main__ - INFO - ✅ Charge Component 4: Found single option after typing 'L', selecting first option
2025-06-23 00:41:05,589 - __main__ - INFO - ✅ Charge Component 4 filled using smart autocomplete: L (LABOUR)
2025-06-23 00:41:06,672 - __main__ - INFO - ⏰ Step 5: Filling calculated hours: 7.0
2025-06-23 00:41:06,672 - test_real_api_data - INFO - ⏰ Filling hours field with: 7.0
2025-06-23 00:41:06,680 - test_real_api_data - INFO - ✅ Hours field filled: 7.0
2025-06-23 00:41:07,801 - __main__ - INFO - ✅ Add button clicked - Record saved!
2025-06-23 00:41:13,821 - __main__ - INFO - ⏰ Overtime hours from API: 2.5
2025-06-23 00:41:13,821 - __main__ - INFO - 🎯 Processing record 2: Aan Kris Wanda
2025-06-23 00:41:13,821 - __main__ - INFO - 📅 Date: 2025-06-01
2025-06-23 00:41:13,821 - __main__ - INFO - 🔘 Transaction type: Overtime
2025-06-23 00:41:13,829 - __main__ - INFO - ⏰ Calculated hours: 2.5
2025-06-23 00:41:13,831 - test_real_api_data - INFO - 📅 Document date calculated: Today=23/06/2025, Transaction=2025-06-01 -> Document=23/06/2025
2025-06-23 00:41:13,832 - __main__ - INFO - 📅 Step 0: Filling document date: 23/06/2025
2025-06-23 00:41:13,845 - __main__ - INFO - ✅ Document date filled: 23/06/2025
2025-06-23 00:41:15,348 - __main__ - INFO - 📅 Step 1: Filling transaction date: 01/06/2025
2025-06-23 00:41:15,364 - __main__ - INFO - ✅ Date filled: 01/06/2025
2025-06-23 00:41:18,111 - __main__ - INFO - 👤 Step 2: Filling employee using smart autocomplete: Aan Kris Wanda
2025-06-23 00:41:21,045 - __main__ - INFO - ✅ Employee: Found single option after typing 'Aa', selecting first option
2025-06-23 00:41:22,634 - __main__ - INFO - ✅ Employee filled using smart autocomplete: Aan Kris Wanda
2025-06-23 00:41:24,634 - __main__ - INFO - 🔘 Step 3: Selecting transaction type: Overtime
2025-06-23 00:41:24,634 - test_real_api_data - INFO - 🔘 Selecting transaction type: Overtime
2025-06-23 00:41:24,720 - test_real_api_data - INFO - ✅ Overtime transaction type selected
2025-06-23 00:41:25,724 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 00:41:25,724 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 00:41:25,724 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 00:41:25,724 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 00:41:25,724 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 00:41:25,724 - __main__ - INFO - 🔧 Step 4: Filling 4 charge job components using smart autocomplete
2025-06-23 00:41:29,585 - __main__ - INFO - ✅ Charge Component 1: Found single option after typing '(OC712', selecting first option
2025-06-23 00:41:31,187 - __main__ - INFO - ✅ Charge Component 1 filled using smart autocomplete: (OC7120) STERILIZER OPERATION
2025-06-23 00:41:33,215 - __main__ - INFO - ✅ Charge Component 2: Found single option after typing 'S', selecting first option
2025-06-23 00:41:34,791 - __main__ - INFO - ✅ Charge Component 2 filled using smart autocomplete: STN-STR (STATION STERILIZER)
2025-06-23 00:41:42,231 - __main__ - INFO - ✅ Charge Component 3: Found single option after typing 'STR00000', selecting first option
2025-06-23 00:41:43,819 - __main__ - INFO - ✅ Charge Component 3 filled using smart autocomplete: STR00000 (LABOUR COST)
2025-06-23 00:41:45,863 - __main__ - INFO - ✅ Charge Component 4: Found single option after typing 'L', selecting first option
2025-06-23 00:41:47,469 - __main__ - INFO - ✅ Charge Component 4 filled using smart autocomplete: L (LABOUR)
2025-06-23 00:41:48,545 - __main__ - INFO - ⏰ Step 5: Filling calculated hours: 2.5
2025-06-23 00:41:48,545 - test_real_api_data - INFO - ⏰ Filling hours field with: 2.5
2025-06-23 00:41:48,553 - test_real_api_data - INFO - ✅ Hours field filled: 2.5
2025-06-23 00:41:49,324 - __main__ - INFO - ✅ Add button clicked - Record saved!
2025-06-23 00:52:42,673 - test_real_api_data - INFO - 🚀 Initializing browser...
2025-06-23 00:52:42,674 - src.core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-23 00:52:42,674 - src.core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-23 00:52:42,766 - src.core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-23 00:52:42,829 - src.core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-23 00:52:42,830 - src.core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-23 00:52:42,833 - WDM - INFO - ====== WebDriver manager ======
2025-06-23 00:52:44,365 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-23 00:52:44,453 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-23 00:52:44,586 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-23 00:52:44,587 - src.core.browser_manager - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-23 00:52:44,587 - src.core.browser_manager - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-23 00:52:44,588 - src.core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-23 00:52:44,588 - src.core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-23 00:52:46,846 - src.core.browser_manager - INFO - ✅ WebDriver created and responsive
2025-06-23 00:52:46,998 - src.core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-23 00:52:47,024 - src.core.persistent_browser_manager - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-23 00:52:49,946 - src.core.persistent_browser_manager - INFO - WebDriver created and page loaded successfully
2025-06-23 00:52:49,946 - src.core.persistent_browser_manager - INFO - Performing initial login...
2025-06-23 00:52:58,487 - src.core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-23 00:53:02,899 - src.core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-23 00:53:02,900 - src.core.persistent_browser_manager - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-23 00:53:02,900 - src.core.persistent_browser_manager - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:53:04,759 - src.core.persistent_browser_manager - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:53:04,759 - src.core.persistent_browser_manager - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-23 00:53:04,771 - src.core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-23 00:53:04,773 - src.core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-23 00:53:04,773 - src.core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-23 00:53:04,773 - src.core.persistent_browser_manager - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:53:04,799 - src.core.persistent_browser_manager - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:53:07,635 - src.core.persistent_browser_manager - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 00:53:07,635 - src.core.persistent_browser_manager - INFO - ✅ SUCCESS: Reached task register page!
2025-06-23 00:53:07,635 - test_real_api_data - INFO - ✅ Browser initialized and ready
2025-06-23 00:53:08,021 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-23 00:53:08,021 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 00:53:10,650 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 00:53:10] "GET / HTTP/1.1" 200 -
2025-06-23 00:53:17,575 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 00:53:17] "GET /api/employees HTTP/1.1" 200 -
2025-06-23 00:53:17,664 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 00:53:17] "GET /api/staging/data?status=staged HTTP/1.1" 200 -
2025-06-23 08:00:08,881 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:00:08] "GET / HTTP/1.1" 200 -
2025-06-23 08:00:14,688 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:00:14] "GET /api/staging/data?status=staged HTTP/1.1" 200 -
2025-06-23 08:00:14,942 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:00:14] "GET /api/employees HTTP/1.1" 200 -
2025-06-23 08:01:39,762 - src.core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-23 08:01:39,769 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)': /session/77415c5af1933f321bc7f552d5160a30
2025-06-23 08:01:43,831 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E14F919AF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/77415c5af1933f321bc7f552d5160a30
2025-06-23 08:02:24,650 - src.core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-23 08:03:53,933 - test_real_api_data - INFO - 🚀 Initializing browser...
2025-06-23 08:03:53,937 - src.core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-23 08:03:53,937 - src.core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-23 08:03:54,089 - src.core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-23 08:03:54,220 - src.core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-23 08:03:54,236 - src.core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-23 08:03:54,236 - WDM - INFO - ====== WebDriver manager ======
2025-06-23 08:03:55,352 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-23 08:03:55,627 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-23 08:03:55,752 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-23 08:03:55,753 - src.core.browser_manager - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-23 08:03:55,754 - src.core.browser_manager - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-23 08:03:55,754 - src.core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-23 08:03:55,755 - src.core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-23 08:03:57,470 - src.core.browser_manager - INFO - ✅ WebDriver created and responsive
2025-06-23 08:03:57,564 - src.core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-23 08:03:57,578 - src.core.persistent_browser_manager - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-23 08:04:03,663 - src.core.persistent_browser_manager - INFO - WebDriver created and page loaded successfully
2025-06-23 08:04:03,663 - src.core.persistent_browser_manager - INFO - Performing initial login...
2025-06-23 08:04:18,861 - src.core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-23 08:04:20,862 - src.core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-23 08:04:20,862 - src.core.persistent_browser_manager - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-23 08:04:20,862 - src.core.persistent_browser_manager - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:04:23,557 - src.core.persistent_browser_manager - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:04:23,557 - src.core.persistent_browser_manager - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-23 08:04:23,564 - src.core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-23 08:04:23,564 - src.core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-23 08:04:23,564 - src.core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-23 08:04:23,566 - src.core.persistent_browser_manager - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:04:23,584 - src.core.persistent_browser_manager - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:04:26,251 - src.core.persistent_browser_manager - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:04:26,257 - src.core.persistent_browser_manager - INFO - ✅ SUCCESS: Reached task register page!
2025-06-23 08:04:26,258 - test_real_api_data - INFO - ✅ Browser initialized and ready
2025-06-23 08:04:26,768 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-23 08:04:26,782 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 08:04:29,288 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:04:29] "GET / HTTP/1.1" 200 -
2025-06-23 08:04:34,973 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:04:34] "GET /api/employees HTTP/1.1" 200 -
2025-06-23 08:04:34,986 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:04:34] "GET /api/staging/data?status=staged HTTP/1.1" 200 -
2025-06-23 08:05:00,243 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:05:00] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-23 08:05:00,243 - test_real_api_data - INFO - 🌐 Fetching data from: http://localhost:5173/api/staging/data
2025-06-23 08:05:04,994 - test_real_api_data - INFO - ✅ API response received
2025-06-23 08:05:04,994 - test_real_api_data - INFO - ✅ Fetched 60 records from API
2025-06-23 08:05:04,994 - test_real_api_data - INFO - 📋 First record employee: Aan Kris Wanda
2025-06-23 08:05:04,994 - test_real_api_data - INFO - 📋 First record date: 2025-06-29
2025-06-23 08:05:04,994 - test_real_api_data - INFO - 📋 First record raw_charge_job: (OC7120) STERILIZER OPERATION / STN-STR (STATION STERILIZER) / STR00000 (LABOUR COST) / L (LABOUR)
2025-06-23 08:05:04,994 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 08:05:04,994 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 08:05:04,994 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 08:05:04,994 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 08:05:04,994 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 08:05:04,994 - test_real_api_data - INFO - ✅ Created normal entry: 7.0 hours
2025-06-23 08:05:04,994 - test_real_api_data - INFO - ✅ Created overtime entry: 2.0 hours
2025-06-23 08:05:05,005 - test_real_api_data - INFO - ✅ Created normal entry: 7.0 hours
2025-06-23 08:05:05,005 - test_real_api_data - INFO - ✅ Created overtime entry: 2.0 hours
2025-06-23 08:05:05,010 - __main__ - INFO - 📅 Weekday detected: Using 7 hours for regular entry
2025-06-23 08:05:05,010 - __main__ - INFO - 🎯 Processing record 1: Aan Kris Wanda
2025-06-23 08:05:05,010 - __main__ - INFO - 📅 Date: 2025-06-17
2025-06-23 08:05:05,010 - __main__ - INFO - 🔘 Transaction type: Normal
2025-06-23 08:05:05,010 - __main__ - INFO - ⏰ Calculated hours: 7.0
2025-06-23 08:05:05,017 - __main__ - INFO - 📅 Testing mode: Current date 23/06/2025 → Document date 23/05/2025 (-1 month)
2025-06-23 08:05:05,026 - __main__ - INFO - 📅 Step 0: Filling document date (testing mode): 23/05/2025
2025-06-23 08:05:05,036 - __main__ - INFO - ✅ Document date filled: 23/05/2025
2025-06-23 08:05:06,544 - __main__ - INFO - 📅 Step 1: Filling transaction date: 17/06/2025
2025-06-23 08:05:06,552 - __main__ - INFO - ✅ Date filled: 17/06/2025
2025-06-23 08:05:07,171 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:05:07] "GET /api/staging/data?status=staged HTTP/1.1" 200 -
2025-06-23 08:05:09,289 - __main__ - INFO - 👤 Step 2: Filling employee using smart autocomplete: Aan Kris Wanda
2025-06-23 08:05:12,203 - __main__ - INFO - ✅ Employee: Found single option after typing 'Aa', selecting first option
2025-06-23 08:05:13,795 - __main__ - INFO - ✅ Employee filled using smart autocomplete: Aan Kris Wanda
2025-06-23 08:05:15,802 - __main__ - INFO - 🔘 Step 3: Selecting transaction type: Normal
2025-06-23 08:05:15,802 - test_real_api_data - INFO - 🔘 Selecting transaction type: Normal
2025-06-23 08:05:15,818 - test_real_api_data - INFO - ✅ Normal transaction type selected
2025-06-23 08:05:16,822 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 08:05:16,822 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 08:05:16,822 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 08:05:16,822 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 08:05:16,822 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 08:05:16,822 - __main__ - INFO - 🔧 Step 4: Filling 4 charge job components using smart autocomplete
2025-06-23 08:05:20,565 - __main__ - INFO - ✅ Charge Component 1: Found single option after typing '(OC712', selecting first option
2025-06-23 08:05:22,156 - __main__ - INFO - ✅ Charge Component 1 filled using smart autocomplete: (OC7120) STERILIZER OPERATION
2025-06-23 08:05:24,348 - __main__ - INFO - ✅ Charge Component 2: Found single option after typing 'S', selecting first option
2025-06-23 08:05:25,925 - __main__ - INFO - ✅ Charge Component 2 filled using smart autocomplete: STN-STR (STATION STERILIZER)
2025-06-23 08:05:33,480 - __main__ - INFO - ✅ Charge Component 3: Found single option after typing 'STR00000', selecting first option
2025-06-23 08:05:35,067 - __main__ - INFO - ✅ Charge Component 3 filled using smart autocomplete: STR00000 (LABOUR COST)
2025-06-23 08:05:37,172 - __main__ - INFO - ✅ Charge Component 4: Found single option after typing 'L', selecting first option
2025-06-23 08:05:38,785 - __main__ - INFO - ✅ Charge Component 4 filled using smart autocomplete: L (LABOUR)
2025-06-23 08:05:39,860 - __main__ - INFO - ⏰ Step 5: Filling calculated hours: 7.0
2025-06-23 08:05:39,860 - test_real_api_data - INFO - ⏰ Filling hours field with: 7.0
2025-06-23 08:05:39,872 - test_real_api_data - INFO - ✅ Hours field filled: 7.0
2025-06-23 08:05:39,990 - __main__ - INFO - ✅ Add button clicked - Record saved!
2025-06-23 08:05:46,008 - __main__ - INFO - ⏰ Overtime hours from API: 2.0
2025-06-23 08:05:46,008 - __main__ - INFO - 🎯 Processing record 2: Aan Kris Wanda
2025-06-23 08:05:46,008 - __main__ - INFO - 📅 Date: 2025-06-17
2025-06-23 08:05:46,008 - __main__ - INFO - 🔘 Transaction type: Overtime
2025-06-23 08:05:46,008 - __main__ - INFO - ⏰ Calculated hours: 2.0
2025-06-23 08:05:46,008 - __main__ - INFO - 📅 Testing mode: Current date 23/06/2025 → Document date 23/05/2025 (-1 month)
2025-06-23 08:05:46,008 - __main__ - INFO - 📅 Step 0: Filling document date (testing mode): 23/05/2025
2025-06-23 08:05:46,019 - __main__ - INFO - ✅ Document date filled: 23/05/2025
2025-06-23 08:05:47,521 - __main__ - INFO - 📅 Step 1: Filling transaction date: 17/06/2025
2025-06-23 08:05:47,536 - __main__ - INFO - ✅ Date filled: 17/06/2025
2025-06-23 08:05:50,665 - __main__ - INFO - 👤 Step 2: Filling employee using smart autocomplete: Aan Kris Wanda
2025-06-23 08:05:53,557 - __main__ - INFO - ✅ Employee: Found single option after typing 'Aa', selecting first option
2025-06-23 08:05:55,137 - __main__ - INFO - ✅ Employee filled using smart autocomplete: Aan Kris Wanda
2025-06-23 08:05:57,148 - __main__ - INFO - 🔘 Step 3: Selecting transaction type: Overtime
2025-06-23 08:05:57,148 - test_real_api_data - INFO - 🔘 Selecting transaction type: Overtime
2025-06-23 08:05:57,311 - test_real_api_data - INFO - ✅ Overtime transaction type selected
2025-06-23 08:05:58,315 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 08:05:58,317 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 08:05:58,318 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 08:05:58,319 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 08:05:58,319 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 08:05:58,320 - __main__ - INFO - 🔧 Step 4: Filling 4 charge job components using smart autocomplete
2025-06-23 08:06:03,578 - __main__ - INFO - ✅ Charge Component 1: Found single option after typing '(OC712', selecting first option
2025-06-23 08:06:05,161 - __main__ - INFO - ✅ Charge Component 1 filled using smart autocomplete: (OC7120) STERILIZER OPERATION
2025-06-23 08:06:07,476 - __main__ - INFO - ✅ Charge Component 2: Found single option after typing 'S', selecting first option
2025-06-23 08:06:09,075 - __main__ - INFO - ✅ Charge Component 2 filled using smart autocomplete: STN-STR (STATION STERILIZER)
2025-06-23 08:06:17,067 - __main__ - INFO - ✅ Charge Component 3: Found single option after typing 'STR00000', selecting first option
2025-06-23 08:06:18,674 - __main__ - INFO - ✅ Charge Component 3 filled using smart autocomplete: STR00000 (LABOUR COST)
2025-06-23 08:06:20,763 - __main__ - INFO - ✅ Charge Component 4: Found single option after typing 'L', selecting first option
2025-06-23 08:06:22,345 - __main__ - INFO - ✅ Charge Component 4 filled using smart autocomplete: L (LABOUR)
2025-06-23 08:06:25,025 - __main__ - INFO - ⏰ Step 5: Filling calculated hours: 2.0
2025-06-23 08:06:25,026 - test_real_api_data - INFO - ⏰ Filling hours field with: 2.0
2025-06-23 08:06:25,038 - test_real_api_data - INFO - ✅ Hours field filled: 2.0
2025-06-23 08:06:25,140 - __main__ - INFO - ✅ Add button clicked - Record saved!
2025-06-23 08:20:40,093 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:20:40] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-23 08:20:40,093 - test_real_api_data - INFO - 🌐 Fetching data from: http://localhost:5173/api/staging/data
2025-06-23 08:20:45,855 - test_real_api_data - INFO - ✅ API response received
2025-06-23 08:20:45,855 - test_real_api_data - INFO - ✅ Fetched 60 records from API
2025-06-23 08:20:45,855 - test_real_api_data - INFO - 📋 First record employee: Aan Kris Wanda
2025-06-23 08:20:45,855 - test_real_api_data - INFO - 📋 First record date: 2025-06-29
2025-06-23 08:20:45,855 - test_real_api_data - INFO - 📋 First record raw_charge_job: (OC7120) STERILIZER OPERATION / STN-STR (STATION STERILIZER) / STR00000 (LABOUR COST) / L (LABOUR)
2025-06-23 08:20:45,862 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 08:20:45,862 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 08:20:45,862 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 08:20:45,865 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 08:20:45,865 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 08:20:45,865 - test_real_api_data - INFO - ✅ Created normal entry: 5.0 hours
2025-06-23 08:20:45,865 - test_real_api_data - INFO - ✅ Created overtime entry: 2.5 hours
2025-06-23 08:20:45,871 - test_real_api_data - INFO - ✅ Created normal entry: 5.0 hours
2025-06-23 08:20:45,871 - test_real_api_data - INFO - ✅ Created overtime entry: 2.5 hours
2025-06-23 08:20:47,501 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:20:47] "GET /api/staging/data?status=staged HTTP/1.1" 200 -
2025-06-23 08:20:56,596 - src.core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-23 08:20:56,599 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)': /session/4f6bd7f7f223745e54ebbdeaf444394e
2025-06-23 08:22:54,975 - test_real_api_data - INFO - 🌐 Fetching data from: http://localhost:5173/api/staging/data
2025-06-23 08:22:59,725 - test_real_api_data - INFO - ✅ API response received
2025-06-23 08:22:59,725 - test_real_api_data - INFO - ✅ Fetched 60 records from API
2025-06-23 08:22:59,725 - test_real_api_data - INFO - 📋 First record employee: Aan Kris Wanda
2025-06-23 08:22:59,726 - test_real_api_data - INFO - 📋 First record date: 2025-06-29
2025-06-23 08:22:59,726 - test_real_api_data - INFO - 📋 First record raw_charge_job: (OC7120) STERILIZER OPERATION / STN-STR (STATION STERILIZER) / STR00000 (LABOUR COST) / L (LABOUR)
2025-06-23 08:22:59,727 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 08:22:59,727 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 08:22:59,727 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 08:22:59,728 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 08:22:59,728 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 08:22:59,729 - test_real_api_data - INFO - ✅ Created zero-hours entry
2025-06-23 08:22:59,731 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 08:22:59,732 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 08:22:59,732 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 08:22:59,732 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 08:22:59,732 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 08:22:59,734 - test_real_api_data - INFO - ✅ Created zero-hours entry
2025-06-23 08:22:59,736 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 08:22:59,737 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 08:22:59,737 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 08:22:59,737 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 08:22:59,738 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 08:22:59,740 - test_real_api_data - INFO - ✅ Created zero-hours entry
2025-06-23 08:23:00,007 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-23 08:23:00,008 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 08:23:11,941 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:23:11] "GET /api/staging/data HTTP/1.1" 200 -
2025-06-23 08:23:18,895 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:23:18] "GET /api/employees HTTP/1.1" 200 -
2025-06-23 08:25:05,097 - test_real_api_data - INFO - 🚀 Initializing browser...
2025-06-23 08:25:05,097 - src.core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-23 08:25:05,099 - src.core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-23 08:25:05,166 - src.core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-23 08:25:05,344 - src.core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-23 08:25:05,355 - src.core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-23 08:25:05,360 - WDM - INFO - ====== WebDriver manager ======
2025-06-23 08:25:06,412 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-23 08:25:06,533 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-23 08:25:06,668 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-23 08:25:06,668 - src.core.browser_manager - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-23 08:25:06,668 - src.core.browser_manager - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-23 08:25:06,668 - src.core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-23 08:25:06,668 - src.core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-23 08:25:07,972 - src.core.browser_manager - INFO - ✅ WebDriver created and responsive
2025-06-23 08:25:08,067 - src.core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-23 08:25:08,151 - src.core.persistent_browser_manager - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-23 08:25:11,002 - src.core.persistent_browser_manager - INFO - WebDriver created and page loaded successfully
2025-06-23 08:25:11,002 - src.core.persistent_browser_manager - INFO - Performing initial login...
2025-06-23 08:25:23,113 - src.core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-23 08:25:25,128 - src.core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-23 08:25:25,137 - src.core.persistent_browser_manager - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-23 08:25:25,139 - src.core.persistent_browser_manager - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:25:27,584 - src.core.persistent_browser_manager - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:25:27,584 - src.core.persistent_browser_manager - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-23 08:25:27,597 - src.core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-23 08:25:27,597 - src.core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-23 08:25:27,597 - src.core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-23 08:25:27,597 - src.core.persistent_browser_manager - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:25:27,615 - src.core.persistent_browser_manager - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:25:30,264 - src.core.persistent_browser_manager - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:25:30,264 - src.core.persistent_browser_manager - INFO - ✅ SUCCESS: Reached task register page!
2025-06-23 08:25:30,264 - test_real_api_data - INFO - ✅ Browser initialized and ready
2025-06-23 08:25:30,517 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-23 08:25:30,517 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 08:25:33,095 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:25:33] "GET / HTTP/1.1" 200 -
2025-06-23 08:25:38,469 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:25:38] "GET /api/staging/data?status=staged HTTP/1.1" 200 -
2025-06-23 08:25:38,552 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:25:38] "GET /api/employees HTTP/1.1" 200 -
2025-06-23 08:25:49,167 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:25:49] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-23 08:25:49,170 - test_real_api_data - INFO - 🌐 Fetching data from: http://localhost:5173/api/staging/data
2025-06-23 08:25:54,172 - test_real_api_data - INFO - ✅ API response received
2025-06-23 08:25:54,173 - test_real_api_data - INFO - ✅ Fetched 60 records from API
2025-06-23 08:25:54,173 - test_real_api_data - INFO - 📋 First record employee: Aan Kris Wanda
2025-06-23 08:25:54,173 - test_real_api_data - INFO - 📋 First record date: 2025-06-29
2025-06-23 08:25:54,173 - test_real_api_data - INFO - 📋 First record raw_charge_job: (OC7120) STERILIZER OPERATION / STN-STR (STATION STERILIZER) / STR00000 (LABOUR COST) / L (LABOUR)
2025-06-23 08:25:54,176 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 08:25:54,176 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 08:25:54,176 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 08:25:54,176 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 08:25:54,176 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 08:25:54,176 - test_real_api_data - INFO - ✅ Created normal entry: 7.0 hours
2025-06-23 08:25:54,176 - test_real_api_data - INFO - ✅ Created overtime entry: 2.0 hours
2025-06-23 08:25:54,181 - test_real_api_data - INFO - ✅ Created normal entry: 7.0 hours
2025-06-23 08:25:54,185 - test_real_api_data - INFO - ✅ Created overtime entry: 2.0 hours
2025-06-23 08:25:54,197 - __main__ - INFO - 📅 Weekday detected: Using 7 hours for regular entry
2025-06-23 08:25:54,197 - __main__ - INFO - 🎯 Processing record 1: Aan Kris Wanda
2025-06-23 08:25:54,197 - __main__ - INFO - 📅 Date: 2025-06-17
2025-06-23 08:25:54,198 - __main__ - INFO - 🔘 Transaction type: Normal
2025-06-23 08:25:54,198 - __main__ - INFO - ⏰ Calculated hours: 7.0
2025-06-23 08:25:54,209 - __main__ - INFO - 📅 Testing mode: Attendance date 17/06/2025 → Document date 17/05/2025 (-1 month)
2025-06-23 08:25:54,209 - __main__ - INFO - 📅 Step 0: Filling document date (testing mode): 17/05/2025
2025-06-23 08:25:54,220 - __main__ - INFO - ✅ Document date filled: 17/05/2025
2025-06-23 08:25:55,710 - __main__ - INFO - 📅 Testing mode: Original attendance date 17/06/2025 → Transaction date 17/05/2025 (-1 month)
2025-06-23 08:25:55,710 - __main__ - INFO - 📅 Step 1: Filling transaction date (testing mode): 17/05/2025
2025-06-23 08:25:55,718 - __main__ - INFO - ✅ Transaction date filled: 17/05/2025
2025-06-23 08:25:56,344 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:25:56] "GET /api/staging/data?status=staged HTTP/1.1" 200 -
2025-06-23 08:25:58,251 - __main__ - INFO - 👤 Step 2: Filling employee using smart autocomplete: Aan Kris Wanda
2025-06-23 08:26:01,225 - __main__ - INFO - ✅ Employee: Found single option after typing 'Aa', selecting first option
2025-06-23 08:26:02,803 - __main__ - INFO - ✅ Employee filled using smart autocomplete: Aan Kris Wanda
2025-06-23 08:26:04,812 - __main__ - INFO - 🔘 Step 3: Selecting transaction type: Normal
2025-06-23 08:26:04,812 - test_real_api_data - INFO - 🔘 Selecting transaction type: Normal
2025-06-23 08:26:04,829 - test_real_api_data - INFO - ✅ Normal transaction type selected
2025-06-23 08:26:05,838 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 08:26:05,838 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 08:26:05,838 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 08:26:05,838 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 08:26:05,838 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 08:26:05,843 - __main__ - INFO - 🔧 Step 4: Filling 4 charge job components using smart autocomplete
2025-06-23 08:26:09,590 - __main__ - INFO - ✅ Charge Component 1: Found single option after typing '(OC712', selecting first option
2025-06-23 08:26:11,180 - __main__ - INFO - ✅ Charge Component 1 filled using smart autocomplete: (OC7120) STERILIZER OPERATION
2025-06-23 08:26:21,645 - __main__ - INFO - ✅ Charge Component 2: Found single option after typing 'ST', selecting first option
2025-06-23 08:26:23,245 - __main__ - INFO - ✅ Charge Component 2 filled using smart autocomplete: STN-STR (STATION STERILIZER)
2025-06-23 08:26:30,789 - __main__ - INFO - ✅ Charge Component 3: Found single option after typing 'STR00000', selecting first option
2025-06-23 08:26:32,386 - __main__ - INFO - ✅ Charge Component 3 filled using smart autocomplete: STR00000 (LABOUR COST)
2025-06-23 08:26:34,405 - __main__ - INFO - ✅ Charge Component 4: Found single option after typing 'L', selecting first option
2025-06-23 08:26:36,005 - __main__ - INFO - ✅ Charge Component 4 filled using smart autocomplete: L (LABOUR)
2025-06-23 08:26:37,079 - __main__ - INFO - ⏰ Step 5: Filling calculated hours: 7.0
2025-06-23 08:26:37,079 - test_real_api_data - INFO - ⏰ Filling hours field with: 7.0
2025-06-23 08:26:37,088 - test_real_api_data - INFO - ✅ Hours field filled: 7.0
2025-06-23 08:26:39,812 - __main__ - INFO - ✅ Add button clicked - Record saved!
2025-06-23 08:26:45,821 - __main__ - INFO - ⏰ Overtime hours from API: 2.0
2025-06-23 08:26:45,821 - __main__ - INFO - 🎯 Processing record 2: Aan Kris Wanda
2025-06-23 08:26:45,821 - __main__ - INFO - 📅 Date: 2025-06-17
2025-06-23 08:26:45,821 - __main__ - INFO - 🔘 Transaction type: Overtime
2025-06-23 08:26:45,821 - __main__ - INFO - ⏰ Calculated hours: 2.0
2025-06-23 08:26:45,821 - __main__ - INFO - 📅 Testing mode: Attendance date 17/06/2025 → Document date 17/05/2025 (-1 month)
2025-06-23 08:26:45,821 - __main__ - INFO - 📅 Step 0: Filling document date (testing mode): 17/05/2025
2025-06-23 08:26:45,834 - __main__ - INFO - ✅ Document date filled: 17/05/2025
2025-06-23 08:26:47,350 - __main__ - INFO - 📅 Testing mode: Original attendance date 17/06/2025 → Transaction date 17/05/2025 (-1 month)
2025-06-23 08:26:47,352 - __main__ - INFO - 📅 Step 1: Filling transaction date (testing mode): 17/05/2025
2025-06-23 08:26:47,378 - __main__ - INFO - ✅ Transaction date filled: 17/05/2025
2025-06-23 08:26:50,180 - __main__ - INFO - 👤 Step 2: Filling employee using smart autocomplete: Aan Kris Wanda
2025-06-23 08:26:53,188 - __main__ - INFO - ✅ Employee: Found single option after typing 'Aa', selecting first option
2025-06-23 08:26:54,780 - __main__ - INFO - ✅ Employee filled using smart autocomplete: Aan Kris Wanda
2025-06-23 08:26:56,787 - __main__ - INFO - 🔘 Step 3: Selecting transaction type: Overtime
2025-06-23 08:26:56,787 - test_real_api_data - INFO - 🔘 Selecting transaction type: Overtime
2025-06-23 08:26:56,872 - test_real_api_data - INFO - ✅ Overtime transaction type selected
2025-06-23 08:26:57,877 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 08:26:57,877 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 08:26:57,877 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 08:26:57,878 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 08:26:57,878 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 08:26:57,878 - __main__ - INFO - 🔧 Step 4: Filling 4 charge job components using smart autocomplete
2025-06-23 08:27:01,702 - __main__ - INFO - ✅ Charge Component 1: Found single option after typing '(OC712', selecting first option
2025-06-23 08:27:03,290 - __main__ - INFO - ✅ Charge Component 1 filled using smart autocomplete: (OC7120) STERILIZER OPERATION
2025-06-23 08:27:05,376 - __main__ - INFO - ✅ Charge Component 2: Found single option after typing 'S', selecting first option
2025-06-23 08:27:06,989 - __main__ - INFO - ✅ Charge Component 2 filled using smart autocomplete: STN-STR (STATION STERILIZER)
2025-06-23 08:27:14,432 - __main__ - INFO - ✅ Charge Component 3: Found single option after typing 'STR00000', selecting first option
2025-06-23 08:27:16,010 - __main__ - INFO - ✅ Charge Component 3 filled using smart autocomplete: STR00000 (LABOUR COST)
2025-06-23 08:27:18,077 - __main__ - INFO - ✅ Charge Component 4: Found single option after typing 'L', selecting first option
2025-06-23 08:27:19,671 - __main__ - INFO - ✅ Charge Component 4 filled using smart autocomplete: L (LABOUR)
2025-06-23 08:27:20,745 - __main__ - INFO - ⏰ Step 5: Filling calculated hours: 2.0
2025-06-23 08:27:20,745 - test_real_api_data - INFO - ⏰ Filling hours field with: 2.0
2025-06-23 08:27:20,753 - test_real_api_data - INFO - ✅ Hours field filled: 2.0
2025-06-23 08:27:21,897 - __main__ - INFO - ✅ Add button clicked - Record saved!
2025-06-23 08:31:21,289 - test_real_api_data - INFO - 🌐 Fetching data from: http://localhost:5173/api/staging/data
2025-06-23 08:31:28,388 - test_real_api_data - INFO - ✅ API response received
2025-06-23 08:31:28,388 - test_real_api_data - INFO - ✅ Fetched 60 records from API
2025-06-23 08:31:28,388 - test_real_api_data - INFO - 📋 First record employee: Aan Kris Wanda
2025-06-23 08:31:28,389 - test_real_api_data - INFO - 📋 First record date: 2025-06-29
2025-06-23 08:31:28,389 - test_real_api_data - INFO - 📋 First record raw_charge_job: (OC7120) STERILIZER OPERATION / STN-STR (STATION STERILIZER) / STR00000 (LABOUR COST) / L (LABOUR)
2025-06-23 08:31:28,390 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 08:31:28,391 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 08:31:28,391 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 08:31:28,391 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 08:31:28,392 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 08:31:28,392 - test_real_api_data - INFO - ✅ Created zero-hours entry
2025-06-23 08:31:28,401 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 08:31:28,401 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 08:31:28,402 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 08:31:28,402 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 08:31:28,402 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 08:31:28,403 - test_real_api_data - INFO - ✅ Created zero-hours entry
2025-06-23 08:31:28,405 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 08:31:28,405 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 08:31:28,405 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 08:31:28,405 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 08:31:28,405 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 08:31:28,406 - test_real_api_data - INFO - ✅ Created zero-hours entry
2025-06-23 08:31:28,633 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-23 08:31:28,633 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 08:31:40,748 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:31:40] "GET /api/staging/data HTTP/1.1" 200 -
2025-06-23 08:31:47,602 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:31:47] "GET /api/employees HTTP/1.1" 200 -
2025-06-23 08:32:09,959 - src.core.persistent_browser_manager - INFO - Cleaning up persistent browser manager...
2025-06-23 08:32:09,967 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)': /session/f7bd18c9ece5004ef005de7bef74a2db
2025-06-23 08:32:14,030 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFDF9A7FE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f7bd18c9ece5004ef005de7bef74a2db
2025-06-23 08:32:19,907 - test_real_api_data - INFO - 🚀 Initializing browser...
2025-06-23 08:32:19,907 - src.core.persistent_browser_manager - INFO - Initializing persistent browser session...
2025-06-23 08:32:19,907 - src.core.persistent_browser_manager - INFO - Creating WebDriver instance...
2025-06-23 08:32:20,056 - src.core.browser_manager - INFO - System info: {'platform': 'Windows', 'architecture': '64bit', 'machine': 'AMD64', 'python_version': '3.12.6'}
2025-06-23 08:32:20,199 - src.core.browser_manager - INFO - ✅ Network connectivity to millwarep3:8004 - OK
2025-06-23 08:32:20,199 - src.core.browser_manager - INFO - Setting up Chrome service (attempt 1/3)
2025-06-23 08:32:20,199 - WDM - INFO - ====== WebDriver manager ======
2025-06-23 08:32:21,319 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-23 08:32:21,413 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-23 08:32:21,521 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-23 08:32:21,530 - src.core.browser_manager - WARNING - Invalid driver path detected: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-23 08:32:21,530 - src.core.browser_manager - INFO - Found correct ChromeDriver at: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-23 08:32:21,530 - src.core.browser_manager - INFO - ✅ Chrome service setup successful with driver: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119\chromedriver-win32\chromedriver.exe
2025-06-23 08:32:21,530 - src.core.browser_manager - INFO - Creating WebDriver (attempt 1/3)
2025-06-23 08:32:23,059 - src.core.browser_manager - INFO - ✅ WebDriver created and responsive
2025-06-23 08:32:23,177 - src.core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-23 08:32:23,211 - src.core.persistent_browser_manager - INFO - Navigating to login page (attempt 1/3): http://millwarep3:8004/
2025-06-23 08:32:26,659 - src.core.persistent_browser_manager - INFO - WebDriver created and page loaded successfully
2025-06-23 08:32:26,661 - src.core.persistent_browser_manager - INFO - Performing initial login...
2025-06-23 08:32:33,815 - src.core.persistent_browser_manager - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-23 08:32:38,143 - src.core.persistent_browser_manager - INFO - Current URL after popup handling: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK
2025-06-23 08:32:38,143 - src.core.persistent_browser_manager - INFO - 🎯 Detected location setting page - IMMEDIATE REDIRECT to task register...
2025-06-23 08:32:38,143 - src.core.persistent_browser_manager - INFO - 🚀 IMMEDIATE REDIRECT: http://millwarep3:8004/EN/system/user/frmSystemUserSetlocation.aspx?FROM=OK → http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:32:39,791 - src.core.persistent_browser_manager - INFO - After redirect - Current URL: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:32:39,791 - src.core.persistent_browser_manager - INFO - ✅ IMMEDIATE REDIRECT SUCCESSFUL - Now at task register
2025-06-23 08:32:39,799 - src.core.persistent_browser_manager - INFO - ✅ Initial login completed successfully
2025-06-23 08:32:39,799 - src.core.persistent_browser_manager - INFO - Started session keepalive thread
2025-06-23 08:32:39,799 - src.core.persistent_browser_manager - INFO - ✅ Persistent browser session initialized successfully
2025-06-23 08:32:39,799 - src.core.persistent_browser_manager - INFO - 🎯 Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:32:39,833 - src.core.persistent_browser_manager - INFO - Current URL before navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:32:42,517 - src.core.persistent_browser_manager - INFO - Final URL after navigation: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-23 08:32:42,517 - src.core.persistent_browser_manager - INFO - ✅ SUCCESS: Reached task register page!
2025-06-23 08:32:42,517 - test_real_api_data - INFO - ✅ Browser initialized and ready
2025-06-23 08:32:42,804 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-06-23 08:32:42,804 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 08:32:45,429 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:32:45] "GET / HTTP/1.1" 200 -
2025-06-23 08:32:50,551 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:32:50] "GET /api/employees HTTP/1.1" 200 -
2025-06-23 08:32:50,596 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:32:50] "GET /api/staging/data?status=staged HTTP/1.1" 200 -
2025-06-23 08:33:24,106 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:33:24] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-23 08:33:24,115 - test_real_api_data - INFO - 🌐 Fetching data from: http://localhost:5173/api/staging/data
2025-06-23 08:33:28,763 - test_real_api_data - INFO - ✅ API response received
2025-06-23 08:33:28,763 - test_real_api_data - INFO - ✅ Fetched 60 records from API
2025-06-23 08:33:28,763 - test_real_api_data - INFO - 📋 First record employee: Aan Kris Wanda
2025-06-23 08:33:28,763 - test_real_api_data - INFO - 📋 First record date: 2025-06-29
2025-06-23 08:33:28,763 - test_real_api_data - INFO - 📋 First record raw_charge_job: (OC7120) STERILIZER OPERATION / STN-STR (STATION STERILIZER) / STR00000 (LABOUR COST) / L (LABOUR)
2025-06-23 08:33:28,772 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 08:33:28,772 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 08:33:28,772 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 08:33:28,772 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 08:33:28,772 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 08:33:28,775 - test_real_api_data - INFO - ✅ Created normal entry: 7.0 hours
2025-06-23 08:33:28,775 - test_real_api_data - INFO - ✅ Created overtime entry: 3.5 hours
2025-06-23 08:33:28,775 - test_real_api_data - INFO - ✅ Created normal entry: 7.0 hours
2025-06-23 08:33:28,775 - test_real_api_data - INFO - ✅ Created overtime entry: 3.5 hours
2025-06-23 08:33:28,788 - __main__ - INFO - 📅 Weekday detected: Using 7 hours for regular entry
2025-06-23 08:33:28,788 - __main__ - INFO - 🎯 Processing record 1: Aan Kris Wanda
2025-06-23 08:33:28,788 - __main__ - INFO - 📅 Date: 2025-06-08
2025-06-23 08:33:28,788 - __main__ - INFO - 🔘 Transaction type: Normal
2025-06-23 08:33:28,788 - __main__ - INFO - ⏰ Calculated hours: 7.0
2025-06-23 08:33:28,798 - __main__ - INFO - 📅 Testing mode: Current date 23/06/2025 → Document date 23/05/2025 (same day, -1 month)
2025-06-23 08:33:28,798 - __main__ - INFO - 📅 Step 0: Filling document date (testing mode): 23/05/2025
2025-06-23 08:33:28,813 - __main__ - INFO - ✅ Document date filled: 23/05/2025
2025-06-23 08:33:30,322 - __main__ - INFO - 📅 Testing mode: Original attendance date 08/06/2025 → Transaction date 08/05/2025 (-1 month)
2025-06-23 08:33:30,323 - __main__ - INFO - 📅 Step 1: Filling transaction date (testing mode): 08/05/2025
2025-06-23 08:33:30,336 - __main__ - INFO - ✅ Transaction date filled: 08/05/2025
2025-06-23 08:33:30,802 - werkzeug - INFO - 127.0.0.1 - - [23/Jun/2025 08:33:30] "GET /api/staging/data?status=staged HTTP/1.1" 200 -
2025-06-23 08:33:33,018 - __main__ - INFO - 👤 Step 2: Filling employee using smart autocomplete: Aan Kris Wanda
2025-06-23 08:33:35,997 - __main__ - INFO - ✅ Employee: Found single option after typing 'Aa', selecting first option
2025-06-23 08:33:37,587 - __main__ - INFO - ✅ Employee filled using smart autocomplete: Aan Kris Wanda
2025-06-23 08:33:39,592 - __main__ - INFO - 🔘 Step 3: Selecting transaction type: Normal
2025-06-23 08:33:39,592 - test_real_api_data - INFO - 🔘 Selecting transaction type: Normal
2025-06-23 08:33:39,609 - test_real_api_data - INFO - ✅ Normal transaction type selected
2025-06-23 08:33:40,619 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 08:33:40,619 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 08:33:40,619 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 08:33:40,619 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 08:33:40,619 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 08:33:40,619 - __main__ - INFO - 🔧 Step 4: Filling 4 charge job components using smart autocomplete
2025-06-23 08:33:44,380 - __main__ - INFO - ✅ Charge Component 1: Found single option after typing '(OC712', selecting first option
2025-06-23 08:33:45,950 - __main__ - INFO - ✅ Charge Component 1 filled using smart autocomplete: (OC7120) STERILIZER OPERATION
2025-06-23 08:33:47,952 - __main__ - INFO - ✅ Charge Component 2: Found single option after typing 'S', selecting first option
2025-06-23 08:33:49,530 - __main__ - INFO - ✅ Charge Component 2 filled using smart autocomplete: STN-STR (STATION STERILIZER)
2025-06-23 08:33:57,127 - __main__ - INFO - ✅ Charge Component 3: Found single option after typing 'STR00000', selecting first option
2025-06-23 08:33:58,725 - __main__ - INFO - ✅ Charge Component 3 filled using smart autocomplete: STR00000 (LABOUR COST)
2025-06-23 08:34:00,820 - __main__ - INFO - ✅ Charge Component 4: Found single option after typing 'L', selecting first option
2025-06-23 08:34:02,407 - __main__ - INFO - ✅ Charge Component 4 filled using smart autocomplete: L (LABOUR)
2025-06-23 08:34:03,508 - __main__ - INFO - ⏰ Step 5: Filling calculated hours: 7.0
2025-06-23 08:34:03,508 - test_real_api_data - INFO - ⏰ Filling hours field with: 7.0
2025-06-23 08:34:03,516 - test_real_api_data - INFO - ✅ Hours field filled: 7.0
2025-06-23 08:34:04,902 - __main__ - INFO - ✅ Add button clicked - Record saved!
2025-06-23 08:34:10,924 - __main__ - INFO - ⏰ Overtime hours from API: 3.5
2025-06-23 08:34:10,924 - __main__ - INFO - 🎯 Processing record 2: Aan Kris Wanda
2025-06-23 08:34:10,924 - __main__ - INFO - 📅 Date: 2025-06-08
2025-06-23 08:34:10,924 - __main__ - INFO - 🔘 Transaction type: Overtime
2025-06-23 08:34:10,933 - __main__ - INFO - ⏰ Calculated hours: 3.5
2025-06-23 08:34:10,934 - __main__ - INFO - 📅 Testing mode: Current date 23/06/2025 → Document date 23/05/2025 (same day, -1 month)
2025-06-23 08:34:10,934 - __main__ - INFO - 📅 Step 0: Filling document date (testing mode): 23/05/2025
2025-06-23 08:34:10,949 - __main__ - INFO - ✅ Document date filled: 23/05/2025
2025-06-23 08:34:12,458 - __main__ - INFO - 📅 Testing mode: Original attendance date 08/06/2025 → Transaction date 08/05/2025 (-1 month)
2025-06-23 08:34:12,458 - __main__ - INFO - 📅 Step 1: Filling transaction date (testing mode): 08/05/2025
2025-06-23 08:34:12,488 - __main__ - INFO - ✅ Transaction date filled: 08/05/2025
2025-06-23 08:34:15,489 - __main__ - INFO - 👤 Step 2: Filling employee using smart autocomplete: Aan Kris Wanda
2025-06-23 08:34:18,520 - __main__ - INFO - ✅ Employee: Found single option after typing 'Aa', selecting first option
2025-06-23 08:34:20,105 - __main__ - INFO - ✅ Employee filled using smart autocomplete: Aan Kris Wanda
2025-06-23 08:34:22,118 - __main__ - INFO - 🔘 Step 3: Selecting transaction type: Overtime
2025-06-23 08:34:22,118 - test_real_api_data - INFO - 🔘 Selecting transaction type: Overtime
2025-06-23 08:34:22,189 - test_real_api_data - INFO - ✅ Overtime transaction type selected
2025-06-23 08:34:23,194 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 08:34:23,194 - test_real_api_data - INFO -    [0]: (OC7120) STERILIZER OPERATION
2025-06-23 08:34:23,194 - test_real_api_data - INFO -    [1]: STN-STR (STATION STERILIZER)
2025-06-23 08:34:23,194 - test_real_api_data - INFO -    [2]: STR00000 (LABOUR COST)
2025-06-23 08:34:23,194 - test_real_api_data - INFO -    [3]: L (LABOUR)
2025-06-23 08:34:23,194 - __main__ - INFO - 🔧 Step 4: Filling 4 charge job components using smart autocomplete
2025-06-23 08:34:26,911 - __main__ - INFO - ✅ Charge Component 1: Found single option after typing '(OC712', selecting first option
2025-06-23 08:34:28,506 - __main__ - INFO - ✅ Charge Component 1 filled using smart autocomplete: (OC7120) STERILIZER OPERATION
2025-06-23 08:34:30,579 - __main__ - INFO - ✅ Charge Component 2: Found single option after typing 'S', selecting first option
2025-06-23 08:34:32,187 - __main__ - INFO - ✅ Charge Component 2 filled using smart autocomplete: STN-STR (STATION STERILIZER)
2025-06-23 08:34:39,669 - __main__ - INFO - ✅ Charge Component 3: Found single option after typing 'STR00000', selecting first option
2025-06-23 08:34:41,234 - __main__ - INFO - ✅ Charge Component 3 filled using smart autocomplete: STR00000 (LABOUR COST)
2025-06-23 08:34:43,272 - __main__ - INFO - ✅ Charge Component 4: Found single option after typing 'L', selecting first option
2025-06-23 08:34:44,855 - __main__ - INFO - ✅ Charge Component 4 filled using smart autocomplete: L (LABOUR)
2025-06-23 08:34:45,951 - __main__ - INFO - ⏰ Step 5: Filling calculated hours: 3.5
2025-06-23 08:34:45,951 - test_real_api_data - INFO - ⏰ Filling hours field with: 3.5
2025-06-23 08:34:45,966 - test_real_api_data - INFO - ✅ Hours field filled: 3.5
2025-06-23 08:34:47,572 - __main__ - INFO - ✅ Add button clicked - Record saved!
