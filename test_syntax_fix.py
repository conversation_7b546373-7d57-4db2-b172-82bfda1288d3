#!/usr/bin/env python3
"""
Simple test to verify syntax fixes are working
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all imports work correctly"""
    try:
        print("🧪 Testing imports...")
        
        # Test automation service import
        from automation_service import get_automation_service
        print("✅ automation_service import successful")
        
        # Test enhanced staging automation import
        from core.enhanced_staging_automation import EnhancedStagingAutomationEngine
        print("✅ enhanced_staging_automation import successful")
        
        # Test browser manager import
        from core.browser_manager import BrowserManager
        print("✅ browser_manager import successful")
        
        # Test data interface import
        from data_interface.app import app
        print("✅ data_interface.app import successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def test_config_loading():
    """Test configuration loading"""
    try:
        print("\n🧪 Testing configuration...")
        
        config = {
            "browser": {
                "headless": False,
                "window_size": [1280, 720],
                "disable_notifications": True,
                "event_delay": 0.5
            },
            "automation": {
                "implicit_wait": 10,
                "page_load_timeout": 30,
                "script_timeout": 30,
                "max_retries": 3,
                "element_timeout": 15
            },
            "credentials": {
                "username": "adm075",
                "password": "adm075"
            },
            "urls": {
                "login": "http://millwarep3:8004/",
                "taskRegister": "http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx"
            }
        }
        
        print("✅ Configuration loaded successfully")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_automation_service_creation():
    """Test automation service creation"""
    try:
        print("\n🧪 Testing automation service creation...")
        
        from automation_service import get_automation_service
        
        config = {
            "browser": {"headless": True},  # Use headless for testing
            "automation": {"max_retries": 1},
            "credentials": {"username": "test", "password": "test"},
            "urls": {"login": "http://test.com"}
        }
        
        # This should create the service without initializing WebDriver
        automation_service = get_automation_service(config)
        
        if automation_service:
            print("✅ Automation service created successfully")
            
            # Test service methods
            status = automation_service.get_engine_status()
            print(f"✅ Engine status retrieved: {status}")
            
            return True
        else:
            print("❌ Automation service creation failed")
            return False
        
    except Exception as e:
        print(f"❌ Automation service test failed: {e}")
        return False

def test_enhanced_automation_engine():
    """Test enhanced automation engine creation"""
    try:
        print("\n🧪 Testing enhanced automation engine...")
        
        from core.enhanced_staging_automation import EnhancedStagingAutomationEngine
        
        config = {
            "browser": {"headless": True},
            "automation": {"max_retries": 1},
            "credentials": {"username": "test", "password": "test"},
            "urls": {"login": "http://test.com"}
        }
        
        # This should create the engine without initializing
        engine = EnhancedStagingAutomationEngine(config)
        
        if engine:
            print("✅ Enhanced automation engine created successfully")
            
            # Test date formatting
            formatted_date = engine.format_date_for_input("2025-06-21")
            print(f"✅ Date formatting test: {formatted_date}")
            
            # Test charge job parsing
            test_job = "(OC7190) BOILER OPERATION / STN-BLR (STATION BOILER) / BLR00000 (LABOUR COST) / L (LABOUR)"
            parsed_parts = engine.parse_charge_job(test_job)
            print(f"✅ Charge job parsing test: {len(parsed_parts)} parts")
            
            return True
        else:
            print("❌ Enhanced automation engine creation failed")
            return False
        
    except Exception as e:
        print(f"❌ Enhanced automation engine test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 SYNTAX FIX VERIFICATION TEST")
    print("="*50)
    print("Testing that all syntax errors have been resolved...")
    print("="*50)
    
    tests = [
        ("Import Tests", test_imports),
        ("Configuration Loading", test_config_loading),
        ("Automation Service Creation", test_automation_service_creation),
        ("Enhanced Automation Engine", test_enhanced_automation_engine)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST RESULTS SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All syntax fixes verified! System is ready to run.")
        print("\nNext steps:")
        print("1. Run: python run_user_controlled_automation.py")
        print("2. Wait for WebDriver initialization")
        print("3. Use web interface at http://localhost:5000")
    else:
        print("⚠️ Some tests failed. Check the errors above.")

if __name__ == "__main__":
    main()
