#!/usr/bin/env python3
"""
Test script for location page handling and enhanced debug logging
Tests the improvements made to handle the location setting page redirect
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from automation_service import get_automation_service
from core.persistent_browser_manager import PersistentBrowserManager
from core.enhanced_staging_automation import EnhancedStagingAutomationEngine


def setup_logging():
    """Setup detailed logging for test"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('test_location_handling.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def load_test_config():
    """Load test configuration"""
    return {
        "browser": {
            "headless": False,
            "window_size": [1280, 720],
            "disable_notifications": True,
            "event_delay": 0.5
        },
        "automation": {
            "implicit_wait": 10,
            "page_load_timeout": 30,
            "script_timeout": 30,
            "max_retries": 3,
            "retry_delay": 2,
            "element_timeout": 15
        },
        "credentials": {
            "username": "adm075",
            "password": "adm075"
        },
        "urls": {
            "login": "http://millwarep3:8004/",
            "taskRegister": "http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx"
        },
        "session": {
            "timeout_minutes": 30,
            "keepalive_interval": 10
        }
    }


async def test_location_page_handling():
    """Test the location page handling specifically"""
    print("\n" + "="*70)
    print("🧪 TESTING LOCATION PAGE HANDLING")
    print("="*70)
    
    config = load_test_config()
    
    try:
        print("1️⃣ Testing persistent browser manager with location page handling...")
        browser_manager = PersistentBrowserManager(config)
        
        # Initialize and check for location page handling
        start_time = time.time()
        success = await browser_manager.initialize()
        init_time = time.time() - start_time
        
        if success:
            print(f"✅ Browser initialization completed in {init_time:.2f} seconds")
            
            # Get current URL after initialization
            driver = browser_manager.get_driver()
            if driver:
                current_url = driver.current_url
                print(f"📍 Current URL after initialization: {current_url}")
                
                # Check if we successfully avoided location page
                if "frmSystemUserSetlocation.aspx" in current_url:
                    print("⚠️ Still on location setting page - testing navigation strategies...")
                    await browser_manager._handle_location_setting_page()
                    
                    final_url = driver.current_url
                    print(f"📍 Final URL after location handling: {final_url}")
                else:
                    print("✅ Successfully bypassed location setting page")
                
                # Test navigation to task register
                print("\n2️⃣ Testing navigation to task register...")
                await browser_manager.navigate_to_task_register()
                
                task_register_url = driver.current_url
                print(f"📍 Task register URL: {task_register_url}")
                
                if "frmPrTrxTaskRegisterDet.aspx" in task_register_url:
                    print("✅ Successfully navigated to task register")
                else:
                    print(f"❌ Failed to reach task register, at: {task_register_url}")
            
        else:
            print("❌ Browser initialization failed")
            return False
        
        # Cleanup
        await browser_manager.cleanup()
        print("✅ Browser manager cleanup completed")
        
        return success
        
    except Exception as e:
        print(f"❌ Location page handling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_enhanced_debug_logging():
    """Test the enhanced debug logging for staging data"""
    print("\n" + "="*70)
    print("🧪 TESTING ENHANCED DEBUG LOGGING")
    print("="*70)
    
    config = load_test_config()
    
    try:
        print("1️⃣ Creating automation service with debug logging...")
        service = get_automation_service(config)
        
        # Wait for pre-initialization
        print("2️⃣ Waiting for automation engine to be ready...")
        max_wait = 30
        wait_time = 0
        
        while wait_time < max_wait:
            engine_status = service.get_engine_status()
            if engine_status['initialized']:
                print(f"✅ Engine ready in {wait_time} seconds")
                break
            
            print(f"⏳ Waiting for engine... ({wait_time}s)")
            time.sleep(2)
            wait_time += 2
        
        if wait_time >= max_wait:
            print("❌ Engine initialization timeout")
            return False
        
        # Create test staging data with comprehensive details
        print("\n3️⃣ Creating comprehensive test staging data...")
        test_staging_data = [
            {
                'id': 'test_emp_001_20250610',
                'employee_id': 'EMP001',
                'employee_name': 'John Smith',
                'date': '2025-06-10',
                'task_code': 'OC7190',
                'station_code': 'STN-BLR',
                'raw_charge_job': '(OC7190) BOILER OPERATION / STN-BLR (STATION BOILER) / BLR00000 (LABOUR COST) / L (LABOUR)',
                'status': 'staged',
                'hours': 8.0,
                'unit': 1.0
            },
            {
                'id': 'test_emp_002_20250610',
                'employee_id': 'EMP002',
                'employee_name': 'Jane Doe',
                'date': '2025-06-10',
                'task_code': 'MT5540',
                'station_code': 'STN-MT',
                'raw_charge_job': '(MT5540) MAINTENANCE WORK / STN-MT (MAINTENANCE STATION) / MT00000 (MAINTENANCE COST) / M (MAINTENANCE)',
                'status': 'staged',
                'hours': 7.5,
                'unit': 1.0
            },
            {
                'id': 'test_emp_003_20250610',
                'employee_id': 'EMP003',
                'employee_name': 'Bob Wilson',
                'date': '2025-06-10',
                'task_code': 'PR2200',
                'station_code': 'STN-PR',
                'raw_charge_job': '(PR2200) PRODUCTION SUPPORT / STN-PR (PRODUCTION STATION) / PR00000 (PRODUCTION COST) / P (PRODUCTION)',
                'status': 'staged',
                'hours': 8.5,
                'unit': 1.0
            }
        ]
        
        # Test the debug logging by calling the service method
        print("\n4️⃣ Testing comprehensive debug logging...")
        service._log_staging_data_details(test_staging_data, "test_debug_001")
        
        print("\n5️⃣ Testing charge job parsing debug function...")
        for i, record in enumerate(test_staging_data, 1):
            print(f"\n🔧 Testing Record {i} Charge Job Parsing:")
            parsed = service._parse_charge_job_debug(record['raw_charge_job'])
            for key, value in parsed.items():
                print(f"   • {key}: {value}")
        
        print("\n✅ Debug logging tests completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Debug logging test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_full_automation_flow():
    """Test the complete automation flow with location handling and debug logging"""
    print("\n" + "="*70)
    print("🧪 TESTING COMPLETE AUTOMATION FLOW")
    print("="*70)
    
    config = load_test_config()
    
    try:
        print("1️⃣ Starting full automation test...")
        service = get_automation_service(config)
        
        # Wait for engine readiness
        print("2️⃣ Waiting for automation engine...")
        max_wait = 45
        wait_time = 0
        
        while wait_time < max_wait:
            engine_status = service.get_engine_status()
            if engine_status['initialized']:
                print(f"✅ Engine ready in {wait_time} seconds")
                break
            
            print(f"⏳ Engine initializing... ({wait_time}s)")
            time.sleep(3)
            wait_time += 3
        
        if wait_time >= max_wait:
            print("❌ Engine initialization timeout")
            return False
        
        # Start automation job with test data
        print("\n3️⃣ Starting automation job with enhanced logging...")
        test_record_ids = ['test_emp_001_20250610', 'test_emp_002_20250610']
        
        start_time = time.time()
        job_id = service.start_automation_job(test_record_ids)
        job_start_time = time.time() - start_time
        
        print(f"✅ Job {job_id} started in {job_start_time:.2f} seconds")
        
        # Monitor job with detailed output
        print("\n4️⃣ Monitoring automation job progress...")
        job_completed = False
        monitor_start = time.time()
        last_status = None
        
        while time.time() - monitor_start < 120:  # 2 minute timeout
            job_status = service.get_job_status(job_id)
            if job_status and job_status['status'] != last_status:
                print(f"📊 Job Status Update: {job_status['status']}")
                last_status = job_status['status']
                
                if job_status['status'] in ['completed', 'failed']:
                    job_completed = True
                    total_time = time.time() - start_time
                    
                    print(f"\n📋 FINAL RESULTS:")
                    print(f"   • Total Time: {total_time:.2f} seconds")
                    print(f"   • Status: {job_status['status']}")
                    print(f"   • Records Processed: {job_status['total_records']}")
                    print(f"   • Successful: {job_status['successful_records']}")
                    print(f"   • Failed: {job_status['failed_records']}")
                    
                    if job_status['status'] == 'failed':
                        print(f"   • Error: {job_status.get('error_message', 'Unknown error')}")
                    
                    break
            
            time.sleep(5)
        
        if not job_completed:
            print("⚠️ Job monitoring timeout")
            # Try to get final status
            final_status = service.get_job_status(job_id)
            if final_status:
                print(f"Final status: {final_status['status']}")
        
        return job_completed
        
    except Exception as e:
        print(f"❌ Full automation flow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function"""
    print("🧪 LOCATION PAGE HANDLING & DEBUG LOGGING TESTS")
    print("=" * 80)
    
    setup_logging()
    
    # Test location page handling
    location_test_passed = await test_location_page_handling()
    
    # Test debug logging
    debug_test_passed = test_enhanced_debug_logging()
    
    # Test full automation flow
    automation_test_passed = await test_full_automation_flow()
    
    # Summary
    print("\n" + "="*70)
    print("📋 TEST SUMMARY")
    print("="*70)
    print(f"Location Page Handling: {'✅ PASSED' if location_test_passed else '❌ FAILED'}")
    print(f"Enhanced Debug Logging: {'✅ PASSED' if debug_test_passed else '❌ FAILED'}")
    print(f"Full Automation Flow: {'✅ PASSED' if automation_test_passed else '❌ FAILED'}")
    
    all_passed = location_test_passed and debug_test_passed and automation_test_passed
    
    if all_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Location page handling is working")
        print("✅ Enhanced debug logging is functional")
        print("✅ Complete automation flow is operational")
        print("\n📝 Key improvements validated:")
        print("  ✅ Location setting page bypass strategies")
        print("  ✅ Robust navigation to task register")
        print("  ✅ Comprehensive staging data logging")
        print("  ✅ Detailed charge job parsing display")
        print("  ✅ Real-time automation progress feedback")
    else:
        print("\n❌ SOME TESTS FAILED")
        print("🔍 Check the logs for detailed error information")
        print("📝 Issues to investigate:")
        if not location_test_passed:
            print("  ❌ Location page handling needs attention")
        if not debug_test_passed:
            print("  ❌ Debug logging implementation issues")
        if not automation_test_passed:
            print("  ❌ Automation flow execution problems")
    
    # Cleanup
    try:
        from automation_service import cleanup_automation_service
        cleanup_automation_service()
        print("\n🧹 Cleanup completed")
    except Exception as e:
        print(f"⚠️ Cleanup warning: {e}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        import traceback
        traceback.print_exc() 