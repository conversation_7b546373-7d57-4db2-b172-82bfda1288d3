#!/usr/bin/env python3
"""
Quick test to validate system initialization fix
"""

import asyncio
import logging
import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from automation_service import AutomationService


def load_config():
    """Load test configuration"""
    return {
        "browser": {
            "headless": False,
            "window_size": [1280, 720],
            "disable_notifications": True
        },
        "automation": {
            "implicit_wait": 8,
            "page_load_timeout": 15,
            "script_timeout": 15,
            "max_retries": 3
        },
        "credentials": {
            "username": "adm075",
            "password": "adm075"
        },
        "urls": {
            "login": "http://millwarep3:8004/",
            "taskRegister": "http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx"
        }
    }


def setup_logging():
    """Setup basic logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


async def main():
    """Test the fixed system initialization"""
    print("🧪 TESTING SYSTEM INITIALIZATION FIX")
    print("=" * 80)
    
    setup_logging()
    config = load_config()
    
    try:
        print("1️⃣ Creating AutomationService...")
        service = AutomationService(config)
        
        print("2️⃣ Checking initial engine status...")
        status = service.get_engine_status()
        print(f"Initial status: {status}")
        
        print("3️⃣ Monitoring initialization for 60 seconds...")
        start_time = time.time()
        max_wait = 60
        
        while time.time() - start_time < max_wait:
            elapsed = int(time.time() - start_time)
            status = service.get_engine_status()
            
            print(f"⏳ {elapsed}s - Status: {status}")
            
            if status.get('initialized', False):
                print(f"✅ ENGINE INITIALIZED SUCCESSFULLY in {elapsed} seconds!")
                print(f"Engine details: {status}")
                break
            
            time.sleep(3)
        else:
            print("❌ INITIALIZATION TIMEOUT - Engine did not initialize within 60 seconds")
            status = service.get_engine_status()
            print(f"Final status: {status}")
        
        # Cleanup
        print("🧹 Cleaning up...")
        await service.cleanup()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main()) 