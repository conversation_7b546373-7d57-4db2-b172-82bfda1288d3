2025-06-23 00:24:56,604 - run_user_controlled_automation - INFO - 📅 Weekday detected: Using 7 hours for regular entry
2025-06-23 00:24:56,608 - run_user_controlled_automation - INFO - 📅 Saturday detected: Using 5 hours for regular entry
2025-06-23 00:24:56,610 - run_user_controlled_automation - INFO - ⏰ Overtime hours from API: 3
2025-06-23 00:24:56,610 - run_user_controlled_automation - INFO - ⚠️ Regular hours is 0, using API data: 0
2025-06-23 00:24:56,610 - run_user_controlled_automation - INFO - 📅 Saturday detected: Using 5 hours for regular entry
2025-06-23 00:24:56,612 - run_user_controlled_automation - INFO - 📅 Weekday detected: Using 7 hours for regular entry
2025-06-23 00:24:56,612 - run_user_controlled_automation - INFO - 📅 Weekday detected: Using 7 hours for regular entry
2025-06-23 00:24:56,613 - run_user_controlled_automation - INFO - 📅 Weekday detected: Using 7 hours for regular entry
2025-06-23 00:24:56,614 - run_user_controlled_automation - INFO - 📅 Weekday detected: Using 7 hours for regular entry
2025-06-23 00:24:56,614 - run_user_controlled_automation - INFO - 📅 Weekday detected: Using 7 hours for regular entry
2025-06-23 00:24:56,615 - run_user_controlled_automation - INFO - 📅 Weekday detected: Using 7 hours for regular entry
2025-06-23 00:24:56,616 - run_user_controlled_automation - INFO - 📅 Saturday detected: Using 5 hours for regular entry
2025-06-23 00:24:56,616 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 00:24:56,616 - test_real_api_data - INFO -    [0]: (ABC123)
2025-06-23 00:24:56,616 - test_real_api_data - INFO -    [1]: PRODUCTION
2025-06-23 00:24:56,616 - test_real_api_data - INFO -    [2]: MACHINE-01
2025-06-23 00:24:56,618 - test_real_api_data - INFO -    [3]: MAINTENANCE
2025-06-23 00:24:56,618 - test_real_api_data - INFO - 🔧 Parsed charge job components:
2025-06-23 00:24:56,619 - test_real_api_data - INFO -    [0]: (XYZ789)
2025-06-23 00:24:56,619 - test_real_api_data - INFO -    [1]: ASSEMBLY
2025-06-23 00:24:56,619 - test_real_api_data - INFO -    [2]: LINE-02
2025-06-23 00:24:56,620 - test_real_api_data - INFO -    [3]: OPERATION
2025-06-23 00:24:56,620 - test_real_api_data - INFO - ✅ Created normal entry: 8.0 hours
2025-06-23 00:24:56,621 - test_real_api_data - INFO - ✅ Created overtime entry: 2.0 hours
2025-06-23 00:24:56,621 - run_user_controlled_automation - INFO - 📅 Weekday detected: Using 7 hours for regular entry
2025-06-23 00:24:56,621 - run_user_controlled_automation - INFO - ⏰ Overtime hours from API: 2
2025-06-23 00:24:56,621 - test_real_api_data - INFO - ✅ Created normal entry: 5.0 hours
2025-06-23 00:24:56,622 - run_user_controlled_automation - INFO - 📅 Saturday detected: Using 5 hours for regular entry
