<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automated Data Entry System - Staging Data Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .filter-section {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        .data-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        .status-staged { background-color: #ffc107; color: #000; }
        .status-processing { background-color: #17a2b8; color: white; }
        .status-completed { background-color: #28a745; color: white; }
        .status-failed { background-color: #dc3545; color: white; }
        .loading-spinner {
            display: none;
        }
        .table-container {
            max-height: 600px;
            overflow-y: auto;
        }
        .action-buttons {
            position: sticky;
            bottom: 0;
            background: white;
            padding: 1rem;
            border-top: 1px solid #dee2e6;
            margin-top: 1rem;
        }
        .record-checkbox {
            transform: scale(1.2);
        }
        .selected-count {
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-robot"></i> Automated Data Entry System</h1>
                    <p class="mb-0">Staging Data Interface for Millware Task Register Automation</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex align-items-center justify-content-end">
                        <div class="loading-spinner me-3">
                            <div class="spinner-border spinner-border-sm text-light" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <button class="btn btn-light" onclick="refreshData()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Mode Selection Section -->
        <div class="filter-section mb-3">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5><i class="fas fa-cog"></i> Automation Mode Selection</h5>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="automationMode" id="testingMode" value="testing" checked>
                        <label class="form-check-label fw-bold text-warning" for="testingMode">
                            <i class="fas fa-flask"></i> Testing Mode (Port 8004)
                        </label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="automationMode" id="realMode" value="real">
                        <label class="form-check-label fw-bold text-success" for="realMode">
                            <i class="fas fa-database"></i> Real Database (Port 8003)
                        </label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div id="modeDescription" class="small text-muted">
                        <i class="fas fa-info-circle"></i> <span id="modeDescText">Testing mode: Document date = tanggal sekarang bulan -1, Transaction date = tanggal absen bulan -1</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-section">
            <h5><i class="fas fa-filter"></i> Filters</h5>
            <div class="row">
                <div class="col-md-3">
                    <label for="employeeFilter" class="form-label">Employee Name</label>
                    <select id="employeeFilter" class="form-select">
                        <option value="">All Employees</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="dateFromFilter" class="form-label">Date From</label>
                    <input type="date" id="dateFromFilter" class="form-control">
                </div>
                <div class="col-md-3">
                    <label for="dateToFilter" class="form-label">Date To</label>
                    <input type="date" id="dateToFilter" class="form-control">
                </div>
                <div class="col-md-3">
                    <label for="statusFilter" class="form-label">Status</label>
                    <select id="statusFilter" class="form-select">
                        <option value="staged">Staged</option>
                        <option value="processing">Processing</option>
                        <option value="completed">Completed</option>
                        <option value="failed">Failed</option>
                        <option value="">All Statuses</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button class="btn btn-primary" onclick="applyFilters()">
                        <i class="fas fa-search"></i> Apply Filters
                    </button>
                    <button class="btn btn-secondary ms-2" onclick="clearFilters()">
                        <i class="fas fa-times"></i> Clear
                    </button>
                </div>
            </div>
        </div>

        <!-- Data Table Section -->
        <div class="data-table">
            <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
                <h5 class="mb-0"><i class="fas fa-table"></i> Staging Data</h5>
                <div class="d-flex align-items-center">
                    <span class="me-3">Selected: <span class="selected-count" id="selectedCount">0</span></span>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="selectAll">
                        <label class="form-check-label" for="selectAll">Select All</label>
                    </div>
                </div>
            </div>
            
            <div class="table-container">
                <table class="table table-hover mb-0">
                    <thead class="table-light sticky-top">
                        <tr>
                            <th width="50">Select</th>
                            <th>Employee Name</th>
                            <th>Date</th>
                            <th>Employee ID</th>
                            <th>Working Hours</th>
                            <th>Task Code</th>
                            <th>Station Code</th>
                            <th>Raw Charge Job</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="dataTableBody">
                        <!-- Data will be loaded here -->
                    </tbody>
                </table>
            </div>
            
            <div class="action-buttons">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span id="recordCount">Loading...</span>
                    </div>
                    <div>
                        <button class="btn btn-success btn-lg" id="processSelectedBtn" onclick="processSelected()" disabled>
                            <i class="fas fa-play"></i> Process Selected Records
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert Section -->
        <div id="alertContainer" class="mt-3"></div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Global variables
        let stagingData = [];
        let selectedRecords = new Set();

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadEmployees();
            loadStagingData();
            setupEventListeners();
            setupModeSelection();
        });

        function setupEventListeners() {
            // Select all checkbox
            document.getElementById('selectAll').addEventListener('change', function() {
                const isChecked = this.checked;
                const checkboxes = document.querySelectorAll('.record-checkbox');
                
                checkboxes.forEach(checkbox => {
                    checkbox.checked = isChecked;
                    const recordIndex = parseInt(checkbox.dataset.recordIndex);
                    if (isChecked) {
                        selectedRecords.add(recordIndex);
                    } else {
                        selectedRecords.delete(recordIndex);
                    }
                });
                
                updateSelectedCount();
            });
        }

        async function loadEmployees() {
            try {
                const response = await fetch('/api/employees');
                const employees = await response.json();
                
                const select = document.getElementById('employeeFilter');
                select.innerHTML = '<option value="">All Employees</option>';
                
                employees.forEach(employee => {
                    const option = document.createElement('option');
                    option.value = employee;
                    option.textContent = employee;
                    select.appendChild(option);
                });
                
            } catch (error) {
                console.error('Error loading employees:', error);
                showAlert('Error loading employee list', 'danger');
            }
        }

        async function loadStagingData() {
            showLoading(true);
            
            try {
                const params = new URLSearchParams();
                
                // Add filter parameters
                const employeeFilter = document.getElementById('employeeFilter').value;
                const dateFromFilter = document.getElementById('dateFromFilter').value;
                const dateToFilter = document.getElementById('dateToFilter').value;
                const statusFilter = document.getElementById('statusFilter').value;
                
                if (employeeFilter) params.append('employee_name', employeeFilter);
                if (dateFromFilter) params.append('date_from', dateFromFilter);
                if (dateToFilter) params.append('date_to', dateToFilter);
                if (statusFilter) params.append('status', statusFilter);
                
                // Updated endpoint to match the enhanced system
                const response = await fetch(`/api/staging/data?${params}`);
                const data = await response.json();
                
                if (data.error) {
                    throw new Error(data.error);
                }
                
                stagingData = data.data || [];
                renderDataTable();
                updateRecordCount(stagingData.length);
                
            } catch (error) {
                console.error('Error loading staging data:', error);
                showAlert(`Error loading data: ${error.message}`, 'danger');
                stagingData = [];
                renderDataTable();
            } finally {
                showLoading(false);
            }
        }

        function renderDataTable() {
            const tbody = document.getElementById('dataTableBody');
            
            if (stagingData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center py-4">
                            <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                            <p class="text-muted">No staging data found</p>
                        </td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = stagingData.map((record, index) => `
                <tr>
                    <td>
                        <input type="checkbox" class="form-check-input record-checkbox" 
                               data-record-index="${index}"
                               data-record-id="${record.id || record.employee_id + '_' + record.date}"
                               onchange="handleRecordSelection(this)">
                    </td>
                    <td><strong>${record.employee_name || 'N/A'}</strong></td>
                    <td>${formatDate(record.date)}</td>
                    <td><code>${record.employee_id || 'N/A'}</code></td>
                    <td>
                        <div class="working-hours-display">
                            <div class="d-flex flex-column">
                                <div class="mb-1">
                                    <span class="badge bg-primary me-1">
                                        <i class="fas fa-clock"></i> Regular: ${record.regular_hours || 0}h
                                    </span>
                                </div>
                                <div class="mb-1">
                                    <span class="badge bg-warning text-dark me-1">
                                        <i class="fas fa-plus-circle"></i> Overtime: ${record.overtime_hours || 0}h
                                    </span>
                                </div>
                                <div>
                                    <small class="text-muted">
                                        <i class="fas fa-calculator"></i> Total: ${(record.regular_hours || 0) + (record.overtime_hours || 0)}h
                                    </small>
                                </div>
                            </div>
                        </div>
                    </td>
                    <td><span class="badge bg-info">${record.task_code || 'N/A'}</span></td>
                    <td><span class="badge bg-secondary">${record.station_code || 'N/A'}</span></td>
                    <td>
                        <small class="text-muted" title="${record.raw_charge_job || 'N/A'}">
                            ${truncateText(record.raw_charge_job || 'N/A', 50)}
                        </small>
                    </td>
                    <td>
                        <span class="badge status-badge status-${record.status || 'staged'}">
                            ${(record.status || 'staged').toUpperCase()}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="viewRecord(${index})">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        function handleRecordSelection(checkbox) {
            const recordIndex = parseInt(checkbox.dataset.recordIndex);
            
            if (checkbox.checked) {
                selectedRecords.add(recordIndex);
            } else {
                selectedRecords.delete(recordIndex);
            }
            
            updateSelectedCount();
        }

        function updateSelectedCount() {
            const count = selectedRecords.size;
            document.getElementById('selectedCount').textContent = count;
            document.getElementById('processSelectedBtn').disabled = count === 0;
        }

        function updateRecordCount(total) {
            document.getElementById('recordCount').textContent = `Total: ${total} records`;
        }

        function setupModeSelection() {
            // Setup mode selection event handlers
            document.querySelectorAll('input[name="automationMode"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    updateModeDescription(this.value);
                });
            });
            
            // Set initial description
            updateModeDescription('testing');
        }

        function updateModeDescription(mode) {
            const descText = document.getElementById('modeDescText');
            if (mode === 'testing') {
                descText.textContent = 'Testing mode: Document date = tanggal sekarang bulan -1, Transaction date = tanggal absen bulan -1 (Port 8004)';
            } else {
                descText.textContent = 'Real Database mode: Document date = tanggal sekarang, Transaction date = tanggal absen (Port 8003)';
            }
        }

        function getSelectedMode() {
            const checkedMode = document.querySelector('input[name="automationMode"]:checked');
            return checkedMode ? checkedMode.value : 'testing';
        }

        async function processSelected() {
            if (selectedRecords.size === 0) {
                showAlert('Please select at least one record to process', 'warning');
                return;
            }
            
            const selectedMode = getSelectedMode();
            const modeText = selectedMode === 'testing' ? 'Testing Mode (Document date -1 bulan)' : 'Real Database Mode (Document date sekarang)';
            
            const confirmed = confirm(`Are you sure you want to process ${selectedRecords.size} selected records using ${modeText}?`);
            if (!confirmed) return;
            
            showLoading(true);
            
            try {
                // Convert selected record indices to array and send to enhanced API with mode
                const selectedIndices = Array.from(selectedRecords);
                
                console.log('Sending selected indices:', selectedIndices, 'Mode:', selectedMode);
                
                const response = await fetch('/api/process-selected', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        selected_indices: selectedIndices,
                        automation_mode: selectedMode
                    })
                });
                
                const result = await response.json();
                
                if (result.error) {
                    throw new Error(result.error);
                }
                
                showAlert(`Successfully started automation for ${result.selected_count} records using ${modeText}. Check console/terminal for detailed progress.`, 'success');
                
                // Clear selection
                selectedRecords.clear();
                updateSelectedCount();
                document.getElementById('selectAll').checked = false;
                
                // Refresh data after a short delay
                setTimeout(() => loadStagingData(), 2000);
                
            } catch (error) {
                console.error('Error processing records:', error);
                showAlert(`Error processing records: ${error.message}`, 'danger');
            } finally {
                showLoading(false);
            }
        }

        function applyFilters() {
            loadStagingData();
        }

        function clearFilters() {
            document.getElementById('employeeFilter').value = '';
            document.getElementById('dateFromFilter').value = '';
            document.getElementById('dateToFilter').value = '';
            document.getElementById('statusFilter').value = 'staged';
            loadStagingData();
        }

        function refreshData() {
            loadStagingData();
        }

        function viewRecord(recordIndex) {
            const record = stagingData[recordIndex];
            if (record) {
                // Display detailed record information
                const details = `
Record Details:

🆔 ID: ${record.id || 'N/A'}
👤 Employee: ${record.employee_name || 'N/A'}
🏷️ Employee ID: ${record.employee_id || 'N/A'}
📅 Date: ${record.date || 'N/A'}
⏰ Regular Hours: ${record.regular_hours || 0}
⏰ Overtime Hours: ${record.overtime_hours || 0}
⏰ Total Hours: ${record.total_hours || 0}
🔧 Raw Charge Job: ${record.raw_charge_job || 'N/A'}
🏭 Task Code: ${record.task_code || 'N/A'}
📍 Station Code: ${record.station_code || 'N/A'}
🔧 Machine Code: ${record.machine_code || 'N/A'}
💰 Expense Code: ${record.expense_code || 'N/A'}
📝 Status: ${record.status || 'staged'}
📋 Notes: ${record.notes || 'N/A'}
                `;
                alert(details);
            }
        }

        function showLoading(show) {
            const spinner = document.querySelector('.loading-spinner');
            spinner.style.display = show ? 'block' : 'none';
        }

        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            const alertId = 'alert_' + Date.now();
            
            const alertHtml = `
                <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            alertContainer.insertAdjacentHTML('beforeend', alertHtml);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }

        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            try {
                return new Date(dateString).toLocaleDateString();
            } catch {
                return dateString;
            }
        }

        function truncateText(text, maxLength) {
            if (!text) return '';
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        }
    </script>
</body>
</html>
