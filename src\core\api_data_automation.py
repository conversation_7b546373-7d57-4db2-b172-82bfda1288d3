"""
API Data Automation Module for Venus AutoFill
Fetches staging data from API and fills Millware ERP forms with proper sequence
Handles overtime entries as separate transactions
"""

import asyncio
import json
import logging
import requests
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, StaleElementReferenceException

from .persistent_browser_manager import PersistentBrowserManager


class APIDataAutomation:
    """Handles API data fetching and form automation with proper sequence"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.browser_manager = None
        self.logger = logging.getLogger(__name__)
        self.api_url = "http://localhost:5173/api/staging/data"
        
    async def fetch_staging_data(self) -> List[Dict]:
        """Fetch staging data from API"""
        try:
            self.logger.info(f"Fetching data from API: {self.api_url}")
            response = requests.get(self.api_url, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            self.logger.info(f"✅ Fetched {len(data)} records from API")
            return data
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"❌ API request failed: {e}")
            raise
        except json.JSONDecodeError as e:
            self.logger.error(f"❌ Invalid JSON response: {e}")
            raise
    
    def create_overtime_entries(self, record: Dict) -> List[Dict]:
        """
        Create separate entries for normal and overtime hours
        If overtime_hours > 0, creates two entries: normal and overtime
        If overtime_hours = 0, creates one normal entry
        """
        entries = []
        
        regular_hours = float(record.get('regular_hours', 0))
        overtime_hours = float(record.get('overtime_hours', 0))
        
        # Always create normal entry if regular_hours > 0
        if regular_hours > 0:
            normal_entry = record.copy()
            normal_entry['transaction_type'] = 'Normal'
            normal_entry['hours'] = regular_hours
            normal_entry['entry_type'] = 'normal'
            entries.append(normal_entry)
            self.logger.info(f"✅ Created normal entry: {regular_hours} hours")
        
        # Create overtime entry if overtime_hours > 0
        if overtime_hours > 0:
            overtime_entry = record.copy()
            overtime_entry['transaction_type'] = 'Overtime'
            overtime_entry['hours'] = overtime_hours
            overtime_entry['entry_type'] = 'overtime'
            entries.append(overtime_entry)
            self.logger.info(f"✅ Created overtime entry: {overtime_hours} hours")
        
        # If both are 0, still create one entry with 0 hours
        if regular_hours == 0 and overtime_hours == 0:
            normal_entry = record.copy()
            normal_entry['transaction_type'] = 'Normal'
            normal_entry['hours'] = 0
            normal_entry['entry_type'] = 'normal'
            entries.append(normal_entry)
            self.logger.info(f"✅ Created zero-hours entry")
        
        return entries
    
    def parse_charge_job(self, raw_charge_job: str) -> Tuple[str, str, str, str]:
        """
        Parse raw_charge_job into components
        Example: "(OC7190) BOILER OPERATION / STN-BLR (STATION BOILER) / BLR00000 (LABOUR COST) / L (LABOUR)"
        Returns: (task_code, station_code, machine_code, expense_code)
        """
        try:
            parts = [part.strip() for part in raw_charge_job.split(' / ')]
            
            task_code = parts[0] if len(parts) > 0 else ""
            station_code = parts[1] if len(parts) > 1 else ""
            machine_code = parts[2] if len(parts) > 2 else ""
            expense_code = parts[3] if len(parts) > 3 else ""
            
            self.logger.info(f"Parsed charge job:")
            self.logger.info(f"  Task: {task_code}")
            self.logger.info(f"  Station: {station_code}")
            self.logger.info(f"  Machine: {machine_code}")
            self.logger.info(f"  Expense: {expense_code}")
            
            return task_code, station_code, machine_code, expense_code
            
        except Exception as e:
            self.logger.error(f"❌ Failed to parse charge job: {e}")
            return "", "", "", ""
    
    def format_date(self, date_str: str) -> str:
        """
        Convert date from API format (2025-05-30) to form format (30/05/2025)
        """
        try:
            date_obj = datetime.strptime(date_str, "%Y-%m-%d")
            formatted = date_obj.strftime("%d/%m/%Y")
            self.logger.info(f"Date formatted: {date_str} -> {formatted}")
            return formatted
        except Exception as e:
            self.logger.error(f"❌ Date formatting failed: {e}")
            return date_str
    
    async def select_transaction_type(self, driver, transaction_type: str) -> bool:
        """
        Select transaction type radio button (Normal or Overtime)
        """
        try:
            self.logger.info(f"🔘 Selecting transaction type: {transaction_type}")
            
            if transaction_type.lower() == 'normal':
                # Select Normal radio button
                script = """
                    var normalRadio = document.getElementById('MainContent_rblOT_0');
                    if (normalRadio) {
                        normalRadio.checked = true;
                        normalRadio.click();
                        return true;
                    }
                    return false;
                """
            elif transaction_type.lower() == 'overtime':
                # Select Overtime radio button
                script = """
                    var overtimeRadio = document.getElementById('MainContent_rblOT_1');
                    if (overtimeRadio) {
                        overtimeRadio.checked = true;
                        overtimeRadio.click();
                        return true;
                    }
                    return false;
                """
            else:
                self.logger.error(f"❌ Unknown transaction type: {transaction_type}")
                return False
            
            result = driver.execute_script(script)
            if result:
                self.logger.info(f"✅ {transaction_type} transaction type selected")
                await asyncio.sleep(1)  # Wait for any page updates
                return True
            else:
                self.logger.error(f"❌ Failed to select {transaction_type} transaction type")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Transaction type selection failed: {e}")
            return False
    
    async def fill_hours_field(self, driver, hours_value: float) -> bool:
        """Fill the hours field with the specified value"""
        try:
            self.logger.info(f"⏰ Filling hours field with: {hours_value}")
            
            # Use JavaScript to fill hours field
            script = f"""
                var hoursField = document.getElementById('MainContent_txtHours');
                if (hoursField) {{
                    hoursField.value = '{hours_value}';
                    hoursField.dispatchEvent(new Event('change', {{bubbles: true}}));
                    hoursField.dispatchEvent(new Event('blur', {{bubbles: true}}));
                    return true;
                }} else {{
                    return false;
                }}
            """
            
            result = driver.execute_script(script)
            if result:
                self.logger.info(f"✅ Hours field filled: {hours_value}")
                return True
            else:
                self.logger.error("❌ Hours field not found")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Hours field filling failed: {e}")
            return False
    
    async def fill_date_field(self, driver, date_value: str) -> bool:
        """Fill date field using JavaScript (proven method)"""
        try:
            # Check if date is already formatted (DD/MM/YYYY) or needs formatting (YYYY-MM-DD)
            if '/' in date_value and len(date_value.split('/')) == 3:
                # Already formatted as DD/MM/YYYY
                formatted_date = date_value
                self.logger.info(f"Date already formatted: {formatted_date}")
            else:
                # Need to format from YYYY-MM-DD to DD/MM/YYYY
                formatted_date = self.format_date(date_value)
            
            # Use JavaScript to fill date field (stale element immune)
            script = f"""
                var dateField = document.getElementById('MainContent_txtTrxDate');
                if (dateField) {{
                    dateField.value = '{formatted_date}';
                    dateField.dispatchEvent(new Event('change', {{bubbles: true}}));
                    dateField.dispatchEvent(new Event('blur', {{bubbles: true}}));
                    return true;
                }} else {{
                    return false;
                }}
            """
            
            result = driver.execute_script(script)
            if result:
                self.logger.info(f"✅ Date filled via JavaScript: {formatted_date}")
                
                # Send Enter and wait for reload
                date_field = driver.find_element(By.ID, "MainContent_txtTrxDate")
                date_field.send_keys(Keys.ENTER)
                self.logger.info("📤 Enter sent, waiting for reload...")
                
                # Wait for page to process
                await asyncio.sleep(2)
                return True
            else:
                self.logger.error("❌ Date field not found")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Date field filling failed: {e}")
            return False
    
    async def fill_employee_field(self, driver, employee_name: str) -> bool:
        """Fill employee field with arrow down + enter sequence"""
        try:
            # Find employee autocomplete input (based on actual HTML structure)
            employee_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".ui-autocomplete-input.ui-widget.ui-widget-content"))
            )
            
            # Clear and type employee name
            employee_input.clear()
            employee_input.send_keys(employee_name)
            self.logger.info(f"📝 Employee name typed: {employee_name}")
            
            # Wait for autocomplete suggestions
            await asyncio.sleep(1)
            
            # Arrow down + Enter
            employee_input.send_keys(Keys.ARROW_DOWN)
            await asyncio.sleep(0.5)
            employee_input.send_keys(Keys.ENTER)
            
            self.logger.info("✅ Employee selected with arrow down + enter")
            await asyncio.sleep(1)  # Wait for field to process
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Employee field filling failed: {e}")
            return False
    
    async def fill_autocomplete_field_by_index(self, driver, field_index: int, value: str, field_name: str) -> bool:
        """Fill autocomplete field by index position (0-based)"""
        try:
            # Find all autocomplete inputs
            autocomplete_inputs = driver.find_elements(By.CSS_SELECTOR, ".ui-autocomplete-input")
            
            if field_index >= len(autocomplete_inputs):
                self.logger.error(f"❌ {field_name} field index {field_index} not found. Only {len(autocomplete_inputs)} autocomplete fields available")
                return False
            
            field_input = autocomplete_inputs[field_index]
            
            # Check if field is visible and interactable
            if not field_input.is_displayed() or not field_input.is_enabled():
                self.logger.error(f"❌ {field_name} field at index {field_index} is not interactable")
                return False
            
            self.logger.info(f"✅ Found {field_name} field at index {field_index}")
            
            # Clear and type value
            field_input.clear()
            field_input.send_keys(value)
            self.logger.info(f"📝 {field_name} typed: {value}")
            
            # Wait for autocomplete suggestions
            await asyncio.sleep(1.5)  # Increased wait time
            
            # Arrow down + Enter
            field_input.send_keys(Keys.ARROW_DOWN)
            await asyncio.sleep(0.8)  # Increased wait time
            field_input.send_keys(Keys.ENTER)
            
            self.logger.info(f"✅ {field_name} selected with arrow down + enter")
            await asyncio.sleep(1.5)  # Wait for field to process
            return True
            
        except Exception as e:
            self.logger.error(f"❌ {field_name} field filling failed: {e}")
            return False
    
    async def process_single_record(self, driver, record: Dict) -> bool:
        """Process a single record following the exact sequence"""
        try:
            self.logger.info(f"🔄 Processing record ID: {record.get('id', 'Unknown')}")
            self.logger.info(f"Employee: {record.get('employee_name', 'Unknown')}")
            self.logger.info(f"Entry Type: {record.get('entry_type', 'normal')} - Hours: {record.get('hours', 0)}")
            
            # Parse charge job components
            task_code, station_code, machine_code, expense_code = self.parse_charge_job(
                record.get('raw_charge_job', '')
            )
            
            # Step 1: Fill date field
            if not await self.fill_date_field(driver, record.get('date', '')):
                return False
            
            # Step 2: Fill employee field
            if not await self.fill_employee_field(driver, record.get('employee_name', '')):
                return False
            
            # Step 3: Select transaction type (Normal or Overtime)
            transaction_type = record.get('transaction_type', 'Normal')
            if not await self.select_transaction_type(driver, transaction_type):
                return False
            
            # Step 4: Fill task code
            if task_code and not await self.fill_autocomplete_field_by_index(
                driver, 1, task_code, "Task Code"
            ):
                return False
            
            # Step 5: Fill station code
            if station_code and not await self.fill_autocomplete_field_by_index(
                driver, 2, station_code, "Station Code"
            ):
                return False
            
            # Step 6: Fill machine code
            if machine_code and not await self.fill_autocomplete_field_by_index(
                driver, 3, machine_code, "Machine Code"
            ):
                return False
            
            # Step 7: Fill expense code
            if expense_code and not await self.fill_autocomplete_field_by_index(
                driver, 4, expense_code, "Expense Code"
            ):
                return False
            
            # Step 8: Fill hours field
            hours = record.get('hours', 0)
            if not await self.fill_hours_field(driver, hours):
                return False
            
            self.logger.info("✅ Record processed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Record processing failed: {e}")
            return False
    
    async def run_api_automation(self) -> bool:
        """Main automation method - fetch data and process records"""
        try:
            # Fetch data from API
            raw_records = await self.fetch_staging_data()
            
            if not raw_records:
                self.logger.warning("⚠️ No records found in API response")
                return False
            
            # Process records to create separate normal/overtime entries
            all_entries = []
            for record in raw_records:
                entries = self.create_overtime_entries(record)
                all_entries.extend(entries)
            
            self.logger.info(f"📊 Created {len(all_entries)} entries from {len(raw_records)} records")
            
            # Get browser driver
            driver = await self.browser_manager.get_driver()
            if not driver:
                self.logger.error("❌ Failed to get browser driver")
                return False
            
            # Navigate to task register page
            task_register_url = "http://millwarep3.rebinmas.com:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx"
            driver.get(task_register_url)
            self.logger.info(f"🌐 Navigated to: {task_register_url}")
            
            # Wait for page to load
            await asyncio.sleep(3)
            
            # Process each entry
            successful_entries = 0
            for i, entry in enumerate(all_entries, 1):
                self.logger.info(f"\n{'='*60}")
                self.logger.info(f"Processing Entry {i}/{len(all_entries)}")
                self.logger.info(f"Record ID: {entry.get('id', 'Unknown')}")
                self.logger.info(f"Type: {entry.get('transaction_type', 'Normal')} - Hours: {entry.get('hours', 0)}")
                self.logger.info(f"{'='*60}")
                
                if await self.process_single_record(driver, entry):
                    successful_entries += 1
                    
                    # TODO: Add form submission here (Add button click)
                    # For now, just wait before next entry
                    await asyncio.sleep(2)
                    
                    # Navigate back to form for next entry if not the last entry
                    if i < len(all_entries):
                        driver.get(task_register_url)
                        await asyncio.sleep(2)
                else:
                    self.logger.error(f"❌ Failed to process entry {i}")
            
            self.logger.info(f"\n🎯 Automation Complete!")
            self.logger.info(f"✅ Successfully processed: {successful_entries}/{len(all_entries)} entries")
            self.logger.info(f"📊 From {len(raw_records)} original records")
            
            return successful_entries > 0
            
        except Exception as e:
            self.logger.error(f"❌ API automation failed: {e}")
            return False

    async def fill_single_record(self, form_data: Dict) -> bool:
        """Fill a single record with form data (used by demo)"""
        try:
            if not self.browser_manager:
                self.logger.error("❌ Browser manager not initialized")
                return False
            
            driver = self.browser_manager.get_driver()
            if not driver:
                self.logger.error("❌ WebDriver not available")
                return False
            
            self.logger.info(f"🔄 Filling form with data: {form_data.get('employee', 'Unknown')}")
            
            # Step 1: Fill date field
            if 'date' in form_data and not await self.fill_date_field(driver, form_data['date']):
                return False
            
            # Step 2: Fill employee field (index 0 - first autocomplete field)
            if 'employee' in form_data and not await self.fill_autocomplete_field_by_index(
                driver, 0, form_data['employee'], "Employee"
            ):
                return False
            
            # Step 3: Fill task code (index 1 - second autocomplete field)  
            if 'task_code' in form_data and form_data['task_code']:
                if not await self.fill_autocomplete_field_by_index(
                    driver, 1, form_data['task_code'], "Task Code"
                ):
                    return False
            
            # Note: Based on form inspection, there are only 2 autocomplete fields visible
            # The Task Code field might contain multiple components or need different approach
            # For now, we'll focus on the first two fields that are working
            
            self.logger.info("✅ Form filled successfully (Date + Employee + Task)")
            self.logger.info("ℹ️ Station, Machine, Expense codes may be handled differently in this form")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Form filling failed: {e}")
            return False