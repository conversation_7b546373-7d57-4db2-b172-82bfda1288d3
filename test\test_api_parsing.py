"""
Test API Data Parsing and Connectivity
Verifies API response and charge job parsing logic
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.persistent_browser_manager import PersistentBrowserManager
from core.api_data_automation import APIDataAutomation


def setup_test_logging():
    """Setup logging for testing"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )


async def test_api_connectivity():
    """Test API connectivity and data structure"""
    print("🧪 Testing API Connectivity")
    print("=" * 40)
    
    # Create minimal API automation for testing (without browser manager)
    api_automation = APIDataAutomation(None)
    
    try:
        # Test API fetch
        records = await api_automation.fetch_staging_data()
        print(f"✅ API Response: {len(records)} records fetched")
        
        if not records:
            print("⚠️ No records found in API response")
            return False
        
        # Show first record structure
        first_record = records[0]
        print("\n📋 First Record Structure:")
        print("-" * 30)
        for key, value in first_record.items():
            if isinstance(value, str) and len(value) > 50:
                value = value[:50] + "..."
            print(f"  {key}: {value}")
        
        print(f"\n✅ API connectivity test successful! Found {len(records)} records")
        return True
        
    except Exception as e:
        print(f"❌ API Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_charge_job_parsing():
    """Test charge job parsing logic"""
    print("\n🧪 Testing Charge Job Parsing")
    print("=" * 40)
    
    # Create minimal API automation for testing (without browser manager)
    api_automation = APIDataAutomation(None)
    
    # Sample charge job from user data
    sample_charge_job = "(OC7190) BOILER OPERATION / STN-BLR (STATION BOILER) / BLR00000 (LABOUR COST) / L (LABOUR)"
    
    print(f"📝 Input: {sample_charge_job}")
    print("\n🔄 Parsing...")
    
    task_code, station_code, machine_code, expense_code = api_automation.parse_charge_job(sample_charge_job)
    
    print("✅ Parsed Components:")
    print(f"  Task Code: '{task_code}'")
    print(f"  Station Code: '{station_code}'")
    print(f"  Machine Code: '{machine_code}'")
    print(f"  Expense Code: '{expense_code}'")
    
    # Verify expected results
    expected = {
        'task': "(OC7190) BOILER OPERATION",
        'station': "STN-BLR (STATION BOILER)",
        'machine': "BLR00000 (LABOUR COST)",
        'expense': "L (LABOUR)"
    }
    
    print("\n🎯 Validation:")
    print(f"  Task Match: {'✅' if task_code == expected['task'] else '❌'}")
    print(f"  Station Match: {'✅' if station_code == expected['station'] else '❌'}")
    print(f"  Machine Match: {'✅' if machine_code == expected['machine'] else '❌'}")
    print(f"  Expense Match: {'✅' if expense_code == expected['expense'] else '❌'}")
    
    return all([
        task_code == expected['task'],
        station_code == expected['station'],
        machine_code == expected['machine'],
        expense_code == expected['expense']
    ])


def test_date_formatting():
    """Test date formatting logic"""
    print("\n🧪 Testing Date Formatting")
    print("=" * 40)
    
    # Create minimal API automation for testing (without browser manager)
    api_automation = APIDataAutomation(None)
    
    # Test cases
    test_dates = [
        ("2025-05-30", "30/05/2025"),
        ("2025-06-10", "10/06/2025"),
        ("2025-12-25", "25/12/2025")
    ]
    
    all_passed = True
    for input_date, expected_output in test_dates:
        result = api_automation.format_date(input_date)
        passed = result == expected_output
        all_passed = all_passed and passed
        
        print(f"  {input_date} → {result} {'✅' if passed else '❌'}")
    
    return all_passed


async def main():
    """Run all tests"""
    setup_test_logging()
    
    print("🌟 Venus AutoFill - API Data Testing")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: API Connectivity
    if await test_api_connectivity():
        tests_passed += 1
        print("✅ API Connectivity Test: PASSED")
    else:
        print("❌ API Connectivity Test: FAILED")
    
    # Test 2: Charge Job Parsing
    if test_charge_job_parsing():
        tests_passed += 1
        print("✅ Charge Job Parsing Test: PASSED")
    else:
        print("❌ Charge Job Parsing Test: FAILED")
    
    # Test 3: Date Formatting
    if test_date_formatting():
        tests_passed += 1
        print("✅ Date Formatting Test: PASSED")
    else:
        print("❌ Date Formatting Test: FAILED")
    
    # Summary
    print("\n🎯 Test Summary")
    print("=" * 20)
    print(f"Passed: {tests_passed}/{total_tests}")
    print(f"Success Rate: {(tests_passed/total_tests)*100:.1f}%")
    
    if tests_passed == total_tests:
        print("\n🎉 All tests passed! Ready for automation.")
        return True
    else:
        print(f"\n⚠️ {total_tests - tests_passed} test(s) failed. Please fix before proceeding.")
        return False


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n🛑 Testing interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Testing failed: {e}")
        sys.exit(1) 