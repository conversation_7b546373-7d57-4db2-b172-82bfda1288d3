#!/usr/bin/env python3
"""
Test script for user-controlled automation workflow
Tests the complete flow from initialization to user selection to automation execution
"""

import sys
import json
import logging
import asyncio
import time
import requests
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def setup_logging():
    """Setup logging for test"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('test_user_controlled_workflow.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def load_test_config():
    """Load test configuration"""
    config = {
        "browser": {
            "headless": False,
            "window_size": [1280, 720],
            "disable_notifications": True,
            "event_delay": 0.5
        },
        "automation": {
            "implicit_wait": 10,
            "page_load_timeout": 30,
            "script_timeout": 30,
            "max_retries": 3,
            "element_timeout": 15
        },
        "credentials": {
            "username": "adm075",
            "password": "adm075"
        },
        "urls": {
            "login": "http://millwarep3:8004/",
            "taskRegister": "http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx"
        }
    }
    return config

def test_automation_service_initialization():
    """Test automation service initialization with user-controlled workflow"""
    print("\n" + "="*60)
    print("🔧 TESTING AUTOMATION SERVICE INITIALIZATION")
    print("="*60)
    
    try:
        from automation_service import get_automation_service
        
        config = load_test_config()
        
        print("Creating automation service with user-controlled workflow...")
        automation_service = get_automation_service(config)
        
        if automation_service:
            print("✅ Automation service created successfully")
            
            # Wait for engine initialization
            print("⏳ Waiting for engine initialization...")
            max_wait = 90  # 1.5 minutes
            start_time = time.time()
            
            while not automation_service.is_engine_initialized and (time.time() - start_time) < max_wait:
                elapsed = int(time.time() - start_time)
                if elapsed % 10 == 0:  # Log every 10 seconds
                    print(f"   Waiting... ({elapsed}s/{max_wait}s)")
                time.sleep(2)
            
            if automation_service.is_engine_initialized:
                print("✅ Automation engine initialized successfully")
                
                # Test engine status
                status = automation_service.get_engine_status()
                print(f"📊 Engine status: {status}")
                
                # Check if WebDriver is positioned correctly
                if automation_service.automation_engine:
                    driver = automation_service.automation_engine.browser_manager.get_driver()
                    if driver:
                        current_url = driver.current_url
                        print(f"🌐 Current WebDriver URL: {current_url}")
                        
                        if "frmPrTrxTaskRegisterDet.aspx" in current_url:
                            print("✅ WebDriver is correctly positioned at task register page")
                            return True
                        else:
                            print("⚠️ WebDriver is not at the expected task register page")
                            return False
                    else:
                        print("❌ WebDriver not available")
                        return False
                else:
                    print("❌ Automation engine not available")
                    return False
            else:
                print("❌ Automation engine initialization timeout")
                return False
        else:
            print("❌ Automation service creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Automation service initialization test failed: {e}")
        return False

def test_web_interface_availability():
    """Test if web interface is available for user selection"""
    print("\n" + "="*60)
    print("🌐 TESTING WEB INTERFACE AVAILABILITY")
    print("="*60)
    
    try:
        # Test if Flask app endpoints are accessible
        base_url = "http://localhost:5000"
        
        # Test main page
        try:
            response = requests.get(base_url, timeout=5)
            if response.status_code == 200:
                print("✅ Main web interface page accessible")
            else:
                print(f"⚠️ Main page returned status: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ Main page not accessible: {e}")
            return False
        
        # Test API endpoints
        api_endpoints = [
            "/api/staging-data",
            "/api/employees"
        ]
        
        for endpoint in api_endpoints:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    print(f"✅ API endpoint {endpoint} accessible")
                else:
                    print(f"⚠️ API endpoint {endpoint} returned status: {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"❌ API endpoint {endpoint} not accessible: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Web interface availability test failed: {e}")
        return False

def test_mock_user_selection():
    """Test mock user selection and automation trigger"""
    print("\n" + "="*60)
    print("👤 TESTING MOCK USER SELECTION")
    print("="*60)
    
    try:
        from automation_service import get_automation_service
        
        config = load_test_config()
        automation_service = get_automation_service(config)
        
        # Wait for engine to be ready
        if not automation_service.is_engine_initialized:
            print("⏳ Waiting for automation engine to be ready...")
            max_wait = 30
            start_time = time.time()
            
            while not automation_service.is_engine_initialized and (time.time() - start_time) < max_wait:
                time.sleep(2)
            
            if not automation_service.is_engine_initialized:
                print("❌ Automation engine not ready for testing")
                return False
        
        # Create mock selected record IDs
        mock_selected_ids = [
            "c1e595b4-d498-4320-bce3-8d0f0cf52060_2025-06-11",
            "emp001_2025-06-11",
            "emp002_2025-06-11"
        ]
        
        print(f"📋 Mock selected record IDs: {mock_selected_ids}")
        
        # Test job creation (simulating user clicking "Process Selected Records")
        print("🚀 Simulating user triggering automation...")
        
        try:
            job_id = automation_service.start_automation_job(mock_selected_ids)
            print(f"✅ Automation job created successfully: {job_id}")
            
            # Monitor job progress
            print("📊 Monitoring job progress...")
            max_monitor_time = 60  # 1 minute
            start_time = time.time()
            
            while (time.time() - start_time) < max_monitor_time:
                job_status = automation_service.get_job_status(job_id)
                if job_status:
                    status = job_status.get('status', 'unknown')
                    print(f"   Job status: {status}")
                    
                    if status in ['completed', 'failed']:
                        if status == 'completed':
                            successful = job_status.get('successful_records', 0)
                            failed = job_status.get('failed_records', 0)
                            print(f"✅ Job completed: {successful} successful, {failed} failed")
                        else:
                            error_msg = job_status.get('error_message', 'Unknown error')
                            print(f"❌ Job failed: {error_msg}")
                        break
                
                time.sleep(5)
            else:
                print("⚠️ Job monitoring timeout")
            
            return True
            
        except Exception as e:
            print(f"❌ Job creation failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Mock user selection test failed: {e}")
        return False

def test_api_integration():
    """Test API integration for user selection"""
    print("\n" + "="*60)
    print("🔌 TESTING API INTEGRATION")
    print("="*60)
    
    try:
        base_url = "http://localhost:5000"
        
        # Test process-selected endpoint with mock data
        mock_payload = {
            "selected_ids": [
                "c1e595b4-d498-4320-bce3-8d0f0cf52060_2025-06-11",
                "emp001_2025-06-11"
            ]
        }
        
        print(f"📤 Testing process-selected API with payload: {mock_payload}")
        
        try:
            response = requests.post(
                f"{base_url}/api/process-selected",
                json=mock_payload,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ API call successful: {result}")
                
                # Check if automation was triggered
                if result.get('success'):
                    automation_id = result.get('automation_id')
                    print(f"🚀 Automation triggered with ID: {automation_id}")
                    return True
                else:
                    print("⚠️ API call successful but automation not triggered")
                    return False
            else:
                print(f"❌ API call failed with status: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ API request failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ API integration test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 USER-CONTROLLED WORKFLOW TEST SUITE")
    print("="*60)
    print("This test suite verifies:")
    print("1. Automation service initialization with ready state")
    print("2. WebDriver positioning at task register page")
    print("3. Web interface availability for user selection")
    print("4. Mock user selection and automation trigger")
    print("5. API integration for processing selected records")
    print("="*60)
    
    setup_logging()
    
    # Run tests
    tests = [
        ("Automation Service Initialization", test_automation_service_initialization),
        ("Web Interface Availability", test_web_interface_availability),
        ("Mock User Selection", test_mock_user_selection),
        ("API Integration", test_api_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running test: {test_name}")
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST RESULTS SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! User-controlled workflow is working correctly.")
        print("\nWorkflow verified:")
        print("✅ WebDriver pre-positioning at task register page")
        print("✅ User selection interface availability")
        print("✅ Automation trigger on user selection")
        print("✅ Processing only user-selected records")
    else:
        print("⚠️ Some tests failed. Check the logs for details.")

if __name__ == "__main__":
    main()
