#!/usr/bin/env python3
"""
Test Transaction Date Logic by Mode
Tests the enhanced transaction date calculation for both testing and real modes
"""

import sys
import asyncio
import logging
from pathlib import Path
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

# Setup paths
sys.path.insert(0, str(Path(__file__).parent))

# Import our enhanced system
from run_user_controlled_automation import UserControlledAutomationSystem


class TestTransactionDateLogic:
    """Test cases for transaction date calculation by mode"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.system = UserControlledAutomationSystem()
        
    def test_transaction_date_calculation_modes(self):
        """Test transaction date calculation for both testing and real modes"""
        print("\n🧪 TESTING TRANSACTION DATE CALCULATION BY MODE")
        print("="*70)
        
        # Test cases with various date formats and scenarios
        test_cases = [
            {
                'name': 'Testing Mode - YYYY-MM-DD Format',
                'original_date': '2024-01-15',  # Monday in January
                'mode': 'testing',
                'expected_behavior': 'Should subtract 1 month from original attendance date',
                'expected_month_diff': 1
            },
            {
                'name': 'Testing Mode - DD/MM/YYYY Format',
                'original_date': '15/01/2024',  # Monday in January
                'mode': 'testing',
                'expected_behavior': 'Should subtract 1 month from original attendance date',
                'expected_month_diff': 1
            },
            {
                'name': 'Real Mode - YYYY-MM-DD Format',
                'original_date': '2024-01-15',  # Monday in January
                'mode': 'real',
                'expected_behavior': 'Should use original attendance date as-is',
                'expected_month_diff': 0
            },
            {
                'name': 'Real Mode - DD/MM/YYYY Format',
                'original_date': '15/01/2024',  # Monday in January
                'mode': 'real',
                'expected_behavior': 'Should use original attendance date as-is',
                'expected_month_diff': 0
            },
            {
                'name': 'Testing Mode - End of Month',
                'original_date': '2024-01-31',  # January 31st
                'mode': 'testing',
                'expected_behavior': 'Should handle month-end correctly (-1 month)',
                'expected_month_diff': 1
            },
            {
                'name': 'Real Mode - End of Month',
                'original_date': '2024-01-31',  # January 31st
                'mode': 'real',
                'expected_behavior': 'Should keep original date',
                'expected_month_diff': 0
            }
        ]
        
        passed_tests = 0
        total_tests = len(test_cases)
        
        for test_case in test_cases:
            try:
                print(f"\n🔧 {test_case['name']}")
                print(f"   📅 Original Date: {test_case['original_date']}")
                print(f"   🎯 Mode: {test_case['mode']}")
                print(f"   📝 Expected: {test_case['expected_behavior']}")
                
                # Calculate transaction date
                calculated_trx_date = self.system.calculate_transaction_date_by_mode(
                    test_case['original_date'], 
                    test_case['mode']
                )
                
                print(f"   📋 Calculated Transaction Date: {calculated_trx_date}")
                
                # Parse dates for comparison
                original_date_obj = None
                calculated_date_obj = None
                
                # Parse original date
                original_str = test_case['original_date']
                if '/' in original_str:
                    original_date_obj = datetime.strptime(original_str, "%d/%m/%Y")
                else:
                    original_date_obj = datetime.strptime(original_str, "%Y-%m-%d")
                
                # Parse calculated date
                calculated_date_obj = datetime.strptime(calculated_trx_date, "%d/%m/%Y")
                
                # Calculate month difference
                month_diff = (original_date_obj.year - calculated_date_obj.year) * 12 + (original_date_obj.month - calculated_date_obj.month)
                
                print(f"   📊 Actual Month Difference: {month_diff}")
                print(f"   📊 Expected Month Difference: {test_case['expected_month_diff']}")
                
                # Validate result
                if month_diff == test_case['expected_month_diff']:
                    print(f"   ✅ Transaction date calculation is CORRECT")
                    passed_tests += 1
                else:
                    print(f"   ❌ Transaction date calculation is INCORRECT")
                
                # Additional validation: check date format
                if "/" in calculated_trx_date and len(calculated_trx_date.split("/")) == 3:
                    print(f"   ✅ Date format (DD/MM/YYYY) is correct")
                else:
                    print(f"   ❌ Date format is incorrect")
                
            except Exception as e:
                print(f"   ❌ Exception: {e}")
        
        print(f"\n📊 Transaction Date Tests: {passed_tests}/{total_tests} passed")
        print(f"📈 Success Rate: {(passed_tests/total_tests*100):.1f}%")
        
        return passed_tests == total_tests
    
    def test_document_vs_transaction_date_consistency(self):
        """Test that document date uses current date -1 month and transaction date uses attendance date -1 month"""
        print("\n🧪 TESTING DOCUMENT vs TRANSACTION DATE LOGIC")
        print("="*60)
        
        test_attendance_dates = [
            '2024-01-15',  # January 15
            '2024-02-29',  # February 29 (leap year)
            '2024-03-31',  # March 31
            '15/06/2024',  # June 15 in DD/MM/YYYY format
        ]
        
        passed_tests = 0
        total_tests = len(test_attendance_dates)
        
        for attendance_date in test_attendance_dates:
            try:
                print(f"\n📅 Testing Attendance Date: {attendance_date}")
                
                # Calculate both dates in testing mode
                doc_date = self.system.calculate_document_date_by_mode(attendance_date, 'testing')
                trx_date = self.system.calculate_transaction_date_by_mode(attendance_date, 'testing')
                
                print(f"   📋 Document Date: {doc_date}")
                print(f"   📋 Transaction Date: {trx_date}")
                
                # Parse both dates
                doc_date_obj = datetime.strptime(doc_date, "%d/%m/%Y")
                trx_date_obj = datetime.strptime(trx_date, "%d/%m/%Y")
                
                # Parse attendance date
                attendance_date_obj = None
                if '/' in attendance_date:
                    attendance_date_obj = datetime.strptime(attendance_date, "%d/%m/%Y")
                else:
                    attendance_date_obj = datetime.strptime(attendance_date, "%Y-%m-%d")
                
                # Current date for comparison
                current_date = datetime.now()
                expected_doc_month = (current_date - relativedelta(months=1)).month
                expected_trx_month = (attendance_date_obj - relativedelta(months=1)).month
                
                print(f"   📊 Document Date Month: {doc_date_obj.month} (Expected: {expected_doc_month})")
                print(f"   📊 Transaction Date Month: {trx_date_obj.month} (Expected: {expected_trx_month})")
                
                # Check document date logic (current date - 1 month)
                doc_correct = doc_date_obj.month == expected_doc_month
                # Check transaction date logic (attendance date - 1 month)  
                trx_correct = trx_date_obj.month == expected_trx_month
                
                if doc_correct and trx_correct:
                    print(f"   ✅ Both date calculations are CORRECT")
                    passed_tests += 1
                else:
                    print(f"   ❌ Date calculations incorrect - Doc: {doc_correct}, Trx: {trx_correct}")
                
            except Exception as e:
                print(f"   ❌ Exception for {attendance_date}: {e}")
        
        print(f"\n📊 Date Logic Tests: {passed_tests}/{total_tests} passed")
        print(f"📈 Success Rate: {(passed_tests/total_tests*100):.1f}%")
        
        return passed_tests == total_tests
    
    def test_real_mode_transaction_date_accuracy(self):
        """Test that real mode uses original attendance dates accurately"""
        print("\n🧪 TESTING REAL MODE TRANSACTION DATE ACCURACY")
        print("="*50)
        
        test_cases = [
            {
                'original_date': '2024-01-15',
                'expected_formatted': '15/01/2024'
            },
            {
                'original_date': '15/01/2024',
                'expected_formatted': '15/01/2024'
            },
            {
                'original_date': '2024-12-25',
                'expected_formatted': '25/12/2024'
            },
            {
                'original_date': '29/02/2024',  # Leap year
                'expected_formatted': '29/02/2024'
            }
        ]
        
        passed_tests = 0
        total_tests = len(test_cases)
        
        for test_case in test_cases:
            try:
                original = test_case['original_date']
                expected = test_case['expected_formatted']
                
                print(f"\n📅 Original Attendance Date: {original}")
                print(f"📋 Expected Transaction Date: {expected}")
                
                # Calculate transaction date in real mode
                calculated = self.system.calculate_transaction_date_by_mode(original, 'real')
                
                print(f"📋 Calculated Transaction Date: {calculated}")
                
                if calculated == expected:
                    print(f"   ✅ Transaction date matches expected format (CORRECT)")
                    passed_tests += 1
                else:
                    print(f"   ❌ Transaction date doesn't match expected format (INCORRECT)")
                
            except Exception as e:
                print(f"   ❌ Exception for {test_case['original_date']}: {e}")
        
        print(f"\n📊 Real Mode Accuracy Tests: {passed_tests}/{total_tests} passed")
        print(f"📈 Success Rate: {(passed_tests/total_tests*100):.1f}%")
        
        return passed_tests == total_tests
    
    def test_edge_cases_transaction_date(self):
        """Test edge cases for transaction date calculation"""
        print("\n🧪 TESTING EDGE CASES FOR TRANSACTION DATE")
        print("="*50)
        
        edge_cases = [
            {
                'name': 'Invalid Date Format',
                'date': 'invalid-date',
                'mode': 'testing',
                'expect_fallback': True
            },
            {
                'name': 'Empty String',
                'date': '',
                'mode': 'real',
                'expect_fallback': True
            },
            {
                'name': 'Year Boundary (January Testing)',
                'date': '2024-01-01',
                'mode': 'testing',
                'expect_fallback': False
            },
            {
                'name': 'Leap Year February',
                'date': '2024-02-29',
                'mode': 'testing',
                'expect_fallback': False
            }
        ]
        
        passed_tests = 0
        total_tests = len(edge_cases)
        
        for case in edge_cases:
            try:
                print(f"\n🔧 {case['name']}")
                print(f"   📅 Input Date: {case['date']}")
                print(f"   🎯 Mode: {case['mode']}")
                
                result = self.system.calculate_transaction_date_by_mode(case['date'], case['mode'])
                
                print(f"   📋 Result: {result}")
                
                if case['expect_fallback']:
                    # For invalid inputs, should return some fallback value
                    if result and len(result) > 0:
                        print(f"   ✅ Fallback handling works (returned something)")
                        passed_tests += 1
                    else:
                        print(f"   ❌ Fallback handling failed (returned empty)")
                else:
                    # For valid inputs, should return proper date format
                    if "/" in result and len(result.split("/")) == 3:
                        print(f"   ✅ Valid date format returned")
                        passed_tests += 1
                    else:
                        print(f"   ❌ Invalid date format returned")
                
            except Exception as e:
                print(f"   ⚠️ Exception (may be expected): {e}")
                # For edge cases, exceptions might be expected
                if case['expect_fallback']:
                    print(f"   ✅ Exception handled gracefully")
                    passed_tests += 1
        
        print(f"\n📊 Edge Case Tests: {passed_tests}/{total_tests} passed")
        print(f"📈 Success Rate: {(passed_tests/total_tests*100):.1f}%")
        
        return passed_tests == total_tests
    
    async def run_all_tests(self):
        """Run all transaction date logic tests"""
        print("\n" + "="*80)
        print("🧪 TRANSACTION DATE LOGIC TESTS")
        print("="*80)
        print("Testing enhanced transaction date calculation by automation mode")
        print("="*80)
        
        test_results = []
        
        # Test 1: Transaction Date Calculation by Mode
        result1 = self.test_transaction_date_calculation_modes()
        test_results.append(("Transaction Date Calculation by Mode", result1))
        
        # Test 2: Document vs Transaction Date Consistency
        result2 = self.test_document_vs_transaction_date_consistency()
        test_results.append(("Document vs Transaction Date Consistency", result2))
        
        # Test 3: Real Mode Transaction Date Accuracy
        result3 = self.test_real_mode_transaction_date_accuracy()
        test_results.append(("Real Mode Transaction Date Accuracy", result3))
        
        # Test 4: Edge Cases
        result4 = self.test_edge_cases_transaction_date()
        test_results.append(("Edge Cases Transaction Date", result4))
        
        # Summary
        passed_tests = sum(1 for _, result in test_results if result)
        total_tests = len(test_results)
        
        print("\n" + "="*80)
        print("📊 TRANSACTION DATE LOGIC TEST RESULTS")
        print("="*80)
        
        for test_name, result in test_results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
        
        print(f"\n📈 Overall Results:")
        print(f"✅ Tests Passed: {passed_tests}/{total_tests}")
        print(f"📈 Success Rate: {(passed_tests/total_tests*100):.1f}%")
        
        if passed_tests == total_tests:
            print("🎉 ALL TRANSACTION DATE LOGIC TESTS PASSED!")
            print("\n🚀 Transaction date logic working correctly:")
            print("   • Testing Mode: Both doc date & transaction date -1 month")
            print("   • Real Mode: Doc date = current, Transaction date = attendance date")
            print("   • Consistent month/year in testing mode")
            print("   • Accurate original dates in real mode")
        else:
            print("⚠️ Some transaction date logic tests failed. Please review.")
        
        print("="*80)
        
        return passed_tests == total_tests


def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('test_transaction_date_logic.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )


async def main():
    """Main test function"""
    setup_logging()
    
    print("🚀 Starting Transaction Date Logic Tests...")
    
    tester = TestTransactionDateLogic()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 All transaction date logic tests completed successfully!")
        print("🚀 Enhanced transaction date logic ready:")
        print("\n📅 Testing Mode:")
        print("   • Document Date: Current date - 1 month")
        print("   • Transaction Date: Attendance date - 1 month")
        print("   • Both dates in same month for consistency")
        print("\n📅 Real Mode:")
        print("   • Document Date: Current date")
        print("   • Transaction Date: Original attendance date")
        print("   • Accurate data input for production")
    else:
        print("\n⚠️ Some transaction date logic tests failed. Please check implementation.")
    
    return success


if __name__ == "__main__":
    asyncio.run(main()) 