"""
Debug API Response Structure
Simple script to see what we're actually getting from the API
"""

import requests
import json

def debug_api_response():
    """Debug the API response to understand its structure"""
    try:
        api_url = "http://localhost:5173/api/staging/data"
        print(f"🌐 Fetching from: {api_url}")
        
        response = requests.get(api_url, timeout=10)
        response.raise_for_status()
        
        print(f"✅ Response status: {response.status_code}")
        print(f"📋 Response headers: {dict(response.headers)}")
        
        # Try to parse JSON
        data = response.json()
        print(f"📊 Response type: {type(data)}")
        print(f"📈 Response length/size: {len(data) if hasattr(data, '__len__') else 'No length'}")
        
        # Print the raw response structure
        print(f"\n📋 Raw response (first 500 chars):")
        print(response.text[:500])
        
        # If it's a dict, show keys
        if isinstance(data, dict):
            print(f"\n🔑 Dictionary keys: {list(data.keys())}")
            for key, value in data.items():
                print(f"   {key}: {type(value)} (length: {len(value) if hasattr(value, '__len__') else 'N/A'})")
        
        # If it's a list, show first element
        elif isinstance(data, list):
            print(f"\n📝 List with {len(data)} items")
            if len(data) > 0:
                first_item = data[0]
                print(f"   First item type: {type(first_item)}")
                if isinstance(first_item, dict):
                    print(f"   First item keys: {list(first_item.keys())}")
                    print(f"   Employee name: {first_item.get('employee_name', 'NOT FOUND')}")
                    print(f"   Date: {first_item.get('date', 'NOT FOUND')}")
                    print(f"   Raw charge job: {first_item.get('raw_charge_job', 'NOT FOUND')}")
        
        # Print full structure (formatted)
        print(f"\n📋 Full response structure:")
        print(json.dumps(data, indent=2, ensure_ascii=False)[:1000])
        
        return data
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ JSON decode failed: {e}")
        print(f"Raw response: {response.text[:200]}")
        return None
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return None

if __name__ == "__main__":
    print("🔍 Venus AutoFill - API Response Debug")
    print("=" * 40)
    debug_api_response() 