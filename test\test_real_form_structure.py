#!/usr/bin/env python3
"""
TEST: Real Form Structure Detection and Data Entry

This test validates the fixes for form detection based on the actual HTML structure
provided by the user, and tests with real staging data from localhost:5173/api/staging/data
"""

import asyncio
import sys
import json
import logging
import requests
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from core.enhanced_staging_automation import EnhancedStagingAutomationEngine

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def get_first_staging_record():
    """Get the first staging record from the API"""
    try:
        print("🔍 Fetching staging data from localhost:5173/api/staging/data...")
        response = requests.get("http://localhost:5173/api/staging/data", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data and len(data) > 0:
                first_record = data[0]
                print(f"✅ Found {len(data)} staging records")
                print(f"📝 First record: ID={first_record.get('id', 'N/A')}, Employee={first_record.get('employee_name', 'N/A')}")
                return first_record
            else:
                print("❌ No staging data found")
                return None
        else:
            print(f"❌ Failed to fetch staging data: HTTP {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error fetching staging data: {e}")
        return None

def create_test_record():
    """Create a test record for testing"""
    return {
        "id": "test-form-structure-001",
        "employee_name": "ADM075",
        "employee_id": "ADM075", 
        "date": "2025-06-11",
        "task_code": "OC7190",
        "station_code": "STN-BLR",
        "raw_charge_job": "(OC7190) BOILER OPERATION / STN-BLR (STATION BOILER) / BLR00000 (LABOUR COST) / L (LABOUR)",
        "status": "staged",
        "hours": 7.0,
        "unit": 1.0
    }

async def test_real_form_structure():
    """Test form detection and filling with real form structure"""
    
    print("🧪 REAL FORM STRUCTURE TEST")
    print("=" * 60)
    
    # Load configuration
    try:
        with open('config/app_config.json', 'r') as f:
            config = json.load(f)
    except Exception as e:
        print(f"❌ Failed to load config: {e}")
        return False
    
    # Get staging data
    staging_record_data = await get_first_staging_record()
    
    if not staging_record_data:
        print("⚠️ No staging data available, using test record")
        staging_record_data = create_test_record()
    
    print(f"\n📋 Using record data:")
    print(f"   ID: {staging_record_data.get('id')}")
    print(f"   Employee: {staging_record_data.get('employee_name')}")
    print(f"   Date: {staging_record_data.get('date')}")
    print(f"   Charge Job: {staging_record_data.get('raw_charge_job', '')[:50]}...")
    
    # Initialize automation engine
    engine = None
    try:
        print("\n1️⃣ TESTING: Engine Initialization")
        print("-" * 40)
        
        engine = EnhancedStagingAutomationEngine(config)
        
        print("🔄 Initializing automation engine...")
        init_success = await engine.initialize()
        
        if not init_success:
            print("❌ Engine initialization failed")
            return False
            
        print("✅ Engine initialization successful")
        
    except Exception as e:
        print(f"❌ Engine initialization error: {e}")
        return False
    
    # Test form detection with actual HTML structure
    try:
        print("\n2️⃣ TESTING: Form Detection (Actual HTML Structure)")
        print("-" * 40)
        
        print("🔄 Testing form readiness detection...")
        form_ready = await engine._wait_for_form_ready_enhanced()
        
        if form_ready:
            print("✅ Form detection successful")
            print(f"✅ Date field ID: {getattr(engine, 'date_field_id', 'Not set')}")
        else:
            print("❌ Form detection failed")
            return False
            
    except Exception as e:
        print(f"❌ Form detection error: {e}")
        return False
    
    # Test navigation to ensure we're on the right page
    try:
        print("\n3️⃣ TESTING: Page Navigation")
        print("-" * 40)
        
        current_url = engine.driver.current_url
        print(f"📍 Current URL: {current_url}")
        
        if "frmPrTrxTaskRegisterDet.aspx" not in current_url:
            print("🔄 Navigating to task register...")
            await engine._navigate_to_task_register_robust()
        else:
            print("✅ Already on task register page")
            
    except Exception as e:
        print(f"❌ Navigation error: {e}")
        return False
    
    # Test individual field filling
    try:
        print("\n4️⃣ TESTING: Individual Field Filling")
        print("-" * 40)
        
        # Convert to staging record
        staging_record = engine._dict_to_staging_record(staging_record_data)
        
        # Test date field filling
        print("📅 Testing date field filling...")
        await engine._fill_date_field_robust(staging_record.date)
        print("✅ Date field test completed")
        
        # Test employee field filling
        print("👤 Testing employee field filling...")
        await engine._fill_employee_field_robust(staging_record.employee_name)
        print("✅ Employee field test completed")
        
        # Test charge job fields filling
        print("🔧 Testing charge job fields filling...")
        charge_job_parts = engine.parse_charge_job(staging_record.raw_charge_job)
        print(f"📊 Parsed charge job into {len(charge_job_parts)} parts:")
        for i, part in enumerate(charge_job_parts, 1):
            print(f"   {i}. {part}")
        
        await engine._fill_charge_job_fields_robust(charge_job_parts)
        print("✅ Charge job fields test completed")
        
    except Exception as e:
        print(f"❌ Field filling error: {e}")
        return False
    
    # Test complete form filling workflow
    try:
        print("\n5️⃣ TESTING: Complete Form Filling Workflow")
        print("-" * 40)
        
        print("🔄 Testing complete form filling...")
        
        # Refresh the page to start clean
        print("🔄 Refreshing page for clean test...")
        await engine._refresh_page_state()
        
        # Test complete form filling
        await engine._fill_form_robust(staging_record)
        
        print("✅ Complete form filling workflow completed")
        
    except Exception as e:
        print(f"❌ Complete workflow error: {e}")
        print("⚠️ This might be expected if form validation requires specific data")
        return False
    
    # Test form submission
    try:
        print("\n6️⃣ TESTING: Form Submission")
        print("-" * 40)
        
        print("📤 Testing form submission...")
        await engine._submit_form_robust()
        
        print("✅ Form submission test completed")
        
        # Wait and check for any submission results
        await asyncio.sleep(3)
        
        final_url = engine.driver.current_url
        print(f"📍 Final URL after submission: {final_url}")
        
    except Exception as e:
        print(f"❌ Form submission error: {e}")
        print("⚠️ This might be expected if form validation fails")
    
    print("\n" + "=" * 60)
    print("🎉 REAL FORM STRUCTURE TEST COMPLETED!")
    print("=" * 60)
    
    print("\n📋 SUMMARY:")
    print("✅ Engine initialization")
    print("✅ Form detection with actual HTML structure")
    print("✅ Individual field filling tests")
    print("✅ Complete workflow testing")
    print("✅ Form submission testing")
    
    return True

async def main():
    """Main test execution"""
    success = False
    
    try:
        success = await test_real_form_structure()
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n🧹 Test completed")
        await asyncio.sleep(2)
    
    if success:
        print("\n🎉 REAL FORM STRUCTURE TEST: SUCCESS")
        return 0
    else:
        print("\n❌ REAL FORM STRUCTURE TEST: FAILED")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code) 